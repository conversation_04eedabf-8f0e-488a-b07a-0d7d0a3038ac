'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { webSocketService, NotificationEvent, ReminderEvent, AdherenceUpdateEvent, AchievementEvent, DashboardUpdateEvent } from '@/lib/websocket';
import { useAuthStore, useAppStore } from '@/lib/store';
import { toast } from 'sonner';

// WebSocket Hook Interface
interface UseWebSocketReturn {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  connect: () => Promise<void>;
  disconnect: () => void;
  ping: () => void;
  joinRoom: (room: string) => void;
  leaveRoom: (room: string) => void;
  sendNotification: (data: any) => void;
}

// Main WebSocket Hook
export function useWebSocket(): UseWebSocketReturn {
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, user } = useAuthStore();
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  const connect = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setError('User not authenticated');
      return;
    }

    setIsConnecting(true);
    setError(null);

    try {
      await webSocketService.connect();
      setIsConnected(true);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Connection failed';
      setError(errorMessage);
      console.error('WebSocket connection failed:', err);
    } finally {
      setIsConnecting(false);
    }
  }, [isAuthenticated, user]);

  const disconnect = useCallback(() => {
    webSocketService.disconnect();
    setIsConnected(false);
    setIsConnecting(false);
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
  }, []);

  const ping = useCallback(() => {
    webSocketService.ping();
  }, []);

  const joinRoom = useCallback((room: string) => {
    webSocketService.joinRoom(room);
  }, []);

  const leaveRoom = useCallback((room: string) => {
    webSocketService.leaveRoom(room);
  }, []);

  const sendNotification = useCallback((data: any) => {
    webSocketService.sendNotification(data);
  }, []);

  // Setup event listeners
  useEffect(() => {
    const handleConnected = (data: any) => {
      setIsConnected(true);
      setError(null);
      console.log('WebSocket connected successfully:', data);
    };

    const handleError = (error: any) => {
      setError(error.message || 'WebSocket error');
      setIsConnected(false);
    };

    const handleDisconnect = () => {
      setIsConnected(false);
      // Auto-reconnect after 3 seconds if authenticated
      if (isAuthenticated && user) {
        reconnectTimeoutRef.current = setTimeout(() => {
          connect();
        }, 3000);
      }
    };

    webSocketService.on('connected', handleConnected);
    webSocketService.on('error', handleError);
    webSocketService.on('disconnect', handleDisconnect);

    return () => {
      webSocketService.off('connected', handleConnected);
      webSocketService.off('error', handleError);
      webSocketService.off('disconnect', handleDisconnect);
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [isAuthenticated, user, connect]);

  // Auto-connect when authenticated
  useEffect(() => {
    if (isAuthenticated && user && !isConnected && !isConnecting) {
      connect();
    } else if (!isAuthenticated && isConnected) {
      disconnect();
    }
  }, [isAuthenticated, user, isConnected, isConnecting, connect, disconnect]);

  return {
    isConnected,
    isConnecting,
    error,
    connect,
    disconnect,
    ping,
    joinRoom,
    leaveRoom,
    sendNotification,
  };
}

// Notification Hook
export function useWebSocketNotifications() {
  const [notifications, setNotifications] = useState<NotificationEvent[]>([]);
  const { addNotification } = useAppStore();

  useEffect(() => {
    const handleNotification = (notification: NotificationEvent) => {
      setNotifications(prev => [notification, ...prev.slice(0, 49)]); // Keep last 50
      
      // Add to global notification store
      addNotification({
        type: notification.type as any,
        title: notification.title,
        message: notification.message,
        read: false,
      });

      // Show toast notification
      toast(notification.title, {
        description: notification.message,
        duration: 5000,
      });
    };

    webSocketService.on('notification', handleNotification);

    return () => {
      webSocketService.off('notification', handleNotification);
    };
  }, [addNotification]);

  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
    );
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  return {
    notifications,
    markAsRead,
    clearNotifications,
  };
}

// Reminder Hook
export function useWebSocketReminders() {
  const [reminders, setReminders] = useState<ReminderEvent[]>([]);

  useEffect(() => {
    const handleReminderSent = (reminder: ReminderEvent) => {
      setReminders(prev => [reminder, ...prev.slice(0, 19)]); // Keep last 20
      toast.success('Reminder Sent', {
        description: `${reminder.medicineName} reminder sent via ${reminder.type}`,
      });
    };

    const handleReminderFailed = (reminder: ReminderEvent & { error: string }) => {
      setReminders(prev => [reminder, ...prev.slice(0, 19)]);
      toast.error('Reminder Failed', {
        description: `Failed to send ${reminder.medicineName} reminder: ${reminder.error}`,
      });
    };

    webSocketService.on('reminder-sent', handleReminderSent);
    webSocketService.on('reminder-failed', handleReminderFailed);

    return () => {
      webSocketService.off('reminder-sent', handleReminderSent);
      webSocketService.off('reminder-failed', handleReminderFailed);
    };
  }, []);

  return { reminders };
}

// Adherence Hook
export function useWebSocketAdherence() {
  const [adherenceUpdates, setAdherenceUpdates] = useState<AdherenceUpdateEvent[]>([]);

  useEffect(() => {
    const handleAdherenceUpdate = (update: AdherenceUpdateEvent) => {
      setAdherenceUpdates(prev => [update, ...prev.slice(0, 19)]);
      toast.info('Adherence Updated', {
        description: `Your adherence rate is now ${Math.round(update.adherenceRate)}%`,
      });
    };

    webSocketService.on('adherence-updated', handleAdherenceUpdate);

    return () => {
      webSocketService.off('adherence-updated', handleAdherenceUpdate);
    };
  }, []);

  return { adherenceUpdates };
}

// Achievement Hook
export function useWebSocketAchievements() {
  const [achievements, setAchievements] = useState<AchievementEvent[]>([]);

  useEffect(() => {
    const handleAchievementUnlocked = (achievement: AchievementEvent) => {
      setAchievements(prev => [achievement, ...prev.slice(0, 19)]);
      toast.success('Achievement Unlocked! 🎉', {
        description: `${achievement.title} - ${achievement.points} points earned!`,
        duration: 8000,
      });
    };

    webSocketService.on('achievement-unlocked', handleAchievementUnlocked);

    return () => {
      webSocketService.off('achievement-unlocked', handleAchievementUnlocked);
    };
  }, []);

  return { achievements };
}

// Dashboard Updates Hook
export function useWebSocketDashboard() {
  const [dashboardUpdates, setDashboardUpdates] = useState<DashboardUpdateEvent[]>([]);

  useEffect(() => {
    const handleDashboardUpdate = (update: DashboardUpdateEvent) => {
      setDashboardUpdates(prev => [update, ...prev.slice(0, 9)]); // Keep last 10
      // Dashboard updates are usually silent, but can trigger data refetches
    };

    webSocketService.on('dashboard-update', handleDashboardUpdate);

    return () => {
      webSocketService.off('dashboard-update', handleDashboardUpdate);
    };
  }, []);

  return { dashboardUpdates };
}
