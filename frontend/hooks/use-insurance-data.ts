import { useEffect } from 'react';
import { useInsuranceStore } from '@/lib/stores/insurance-store';
import { PolicyHolderQueryDto, AdherenceReportQueryDto, RiskAssessmentQueryDto } from '@/lib/api/insurance';

// Hook for managing policy holders
export function usePolicyHolders(query?: PolicyHolderQueryDto) {
  const {
    policyHolders,
    policyHoldersTotal,
    policyHoldersPage,
    policyHoldersLimit,
    policyHoldersLoading,
    policyHoldersError,
    fetchPolicyHolders,
  } = useInsuranceStore();

  const refreshPolicyHolders = () => fetchPolicyHolders(query);

  return {
    policyHolders,
    total: policyHoldersTotal,
    page: policyHoldersPage,
    limit: policyHoldersLimit,
    isLoading: policyHoldersLoading,
    error: policyHoldersError,
    hasError: !!policyHoldersError,
    fetchPolicyHolders: (newQuery?: PolicyHolderQueryDto) => fetchPolicyHolders(newQuery || query),
    refreshPolicyHolders,
  };
}

// Hook for managing dashboard summary
export function useInsuranceDashboard() {
  const {
    dashboardSummary,
    dashboardLoading,
    dashboardError,
    fetchDashboardSummary,
  } = useInsuranceStore();

  const refreshDashboard = () => fetchDashboardSummary();

  return {
    dashboardSummary,
    isLoading: dashboardLoading,
    error: dashboardError,
    hasError: !!dashboardError,
    fetchDashboardSummary,
    refreshDashboard,
  };
}

// Hook for managing adherence reports
export function useAdherenceReports() {
  const {
    adherenceReports,
    adherenceReportsLoading,
    adherenceReportsError,
    fetchPatientAdherenceReport,
  } = useInsuranceStore();

  return {
    adherenceReports,
    adherenceReportsLoading,
    adherenceReportsError,
    fetchPatientAdherenceReport,
    getPatientReport: (patientId: string) => adherenceReports[patientId],
    isPatientReportLoading: (patientId: string) => adherenceReportsLoading[patientId] || false,
    getPatientReportError: (patientId: string) => adherenceReportsError[patientId],
  };
}

// Hook for managing bulk reports
export function useBulkAdherenceReport() {
  const {
    bulkAdherenceReport,
    bulkReportLoading,
    bulkReportError,
    fetchBulkAdherenceReport,
  } = useInsuranceStore();

  return {
    bulkAdherenceReport,
    isLoading: bulkReportLoading,
    error: bulkReportError,
    hasError: !!bulkReportError,
    fetchBulkAdherenceReport,
    refreshBulkReport: (patientIds: string[]) => 
      fetchBulkAdherenceReport({ patient_ids: patientIds, days: 30, include_details: true }),
  };
}

// Hook for managing risk assessment
export function useRiskAssessment() {
  const {
    riskAssessmentReport,
    riskAssessmentLoading,
    riskAssessmentError,
    fetchRiskAssessmentReport,
  } = useInsuranceStore();

  return {
    riskAssessmentReport,
    isLoading: riskAssessmentLoading,
    error: riskAssessmentError,
    hasError: !!riskAssessmentError,
    fetchRiskAssessmentReport,
    refreshRiskAssessment: (query?: RiskAssessmentQueryDto) => fetchRiskAssessmentReport(query),
  };
}

// Hook for managing hospital performance
export function useHospitalPerformance() {
  const {
    hospitalPerformance,
    hospitalPerformanceLoading,
    hospitalPerformanceError,
    fetchHospitalPerformance,
  } = useInsuranceStore();

  return {
    hospitalPerformance,
    isLoading: hospitalPerformanceLoading,
    error: hospitalPerformanceError,
    hasError: !!hospitalPerformanceError,
    fetchHospitalPerformance,
    refreshHospitalPerformance: () => fetchHospitalPerformance(),
  };
}

// Hook for managing adherence analytics
export function useInsuranceAnalytics() {
  const {
    adherenceAnalytics,
    analyticsLoading,
    analyticsError,
    fetchAdherenceAnalytics,
  } = useInsuranceStore();

  return {
    adherenceAnalytics,
    isLoading: analyticsLoading,
    error: analyticsError,
    hasError: !!analyticsError,
    fetchAdherenceAnalytics,
    refreshAnalytics: (params?: { days?: number; group_by?: 'condition' | 'hospital' | 'doctor' }) => 
      fetchAdherenceAnalytics(params),
  };
}

// Main hook that combines all insurance data management
export function useInsuranceData(options?: {
  autoFetchDashboard?: boolean;
  autoFetchPolicyHolders?: boolean;
  autoFetchHospitalPerformance?: boolean;
  autoFetchAnalytics?: boolean;
  policyHoldersQuery?: PolicyHolderQueryDto;
  analyticsParams?: { days?: number; group_by?: 'condition' | 'hospital' | 'doctor' };
}) {
  const {
    autoFetchDashboard = false,
    autoFetchPolicyHolders = false,
    autoFetchHospitalPerformance = false,
    autoFetchAnalytics = false,
    policyHoldersQuery,
    analyticsParams,
  } = options || {};

  const dashboard = useInsuranceDashboard();
  const policyHolders = usePolicyHolders(policyHoldersQuery);
  const adherenceReports = useAdherenceReports();
  const bulkReport = useBulkAdherenceReport();
  const riskAssessment = useRiskAssessment();
  const hospitalPerformance = useHospitalPerformance();
  const analytics = useInsuranceAnalytics();

  const { clearErrors, reset, refreshAll } = useInsuranceStore();

  // Auto-fetch data on mount if requested
  useEffect(() => {
    if (autoFetchDashboard && !dashboard.dashboardSummary && !dashboard.isLoading) {
      dashboard.fetchDashboardSummary();
    }
  }, [autoFetchDashboard, dashboard.dashboardSummary, dashboard.isLoading, dashboard.fetchDashboardSummary]);

  useEffect(() => {
    if (autoFetchPolicyHolders && !policyHolders.policyHolders.length && !policyHolders.isLoading) {
      policyHolders.fetchPolicyHolders(policyHoldersQuery);
    }
  }, [autoFetchPolicyHolders, policyHolders.policyHolders.length, policyHolders.isLoading, policyHolders.fetchPolicyHolders, policyHoldersQuery]);

  useEffect(() => {
    if (autoFetchHospitalPerformance && !hospitalPerformance.hospitalPerformance.length && !hospitalPerformance.isLoading) {
      hospitalPerformance.fetchHospitalPerformance();
    }
  }, [autoFetchHospitalPerformance, hospitalPerformance.hospitalPerformance.length, hospitalPerformance.isLoading, hospitalPerformance.fetchHospitalPerformance]);

  useEffect(() => {
    if (autoFetchAnalytics && !analytics.adherenceAnalytics && !analytics.isLoading) {
      analytics.fetchAdherenceAnalytics(analyticsParams);
    }
  }, [autoFetchAnalytics, analytics.adherenceAnalytics, analytics.isLoading, analytics.fetchAdherenceAnalytics, analyticsParams]);

  // Combined loading state
  const isLoading = dashboard.isLoading || policyHolders.isLoading || hospitalPerformance.isLoading || analytics.isLoading;

  // Combined error state
  const hasError = dashboard.hasError || policyHolders.hasError || hospitalPerformance.hasError || analytics.hasError;
  const error = dashboard.error || policyHolders.error || hospitalPerformance.error || analytics.error;

  return {
    // Dashboard
    dashboardSummary: dashboard.dashboardSummary,
    isDashboardLoading: dashboard.isLoading,
    dashboardError: dashboard.error,
    hasDashboardError: dashboard.hasError,
    refreshDashboard: dashboard.refreshDashboard,

    // Policy Holders
    policyHolders: policyHolders.policyHolders,
    policyHoldersTotal: policyHolders.total,
    policyHoldersPage: policyHolders.page,
    policyHoldersLimit: policyHolders.limit,
    isPolicyHoldersLoading: policyHolders.isLoading,
    policyHoldersError: policyHolders.error,
    hasPolicyHoldersError: policyHolders.hasError,
    refreshPolicyHolders: policyHolders.refreshPolicyHolders,

    // Adherence Reports
    adherenceReports: adherenceReports.adherenceReports,
    adherenceReportsLoading: adherenceReports.adherenceReportsLoading,
    adherenceReportsError: adherenceReports.adherenceReportsError,
    fetchPatientAdherenceReport: adherenceReports.fetchPatientAdherenceReport,
    getPatientReport: adherenceReports.getPatientReport,
    isPatientReportLoading: adherenceReports.isPatientReportLoading,
    getPatientReportError: adherenceReports.getPatientReportError,

    // Bulk Report
    bulkAdherenceReport: bulkReport.bulkAdherenceReport,
    isBulkReportLoading: bulkReport.isLoading,
    bulkReportError: bulkReport.error,
    hasBulkReportError: bulkReport.hasError,
    fetchBulkAdherenceReport: bulkReport.fetchBulkAdherenceReport,
    refreshBulkReport: bulkReport.refreshBulkReport,

    // Risk Assessment
    riskAssessmentReport: riskAssessment.riskAssessmentReport,
    isRiskAssessmentLoading: riskAssessment.isLoading,
    riskAssessmentError: riskAssessment.error,
    hasRiskAssessmentError: riskAssessment.hasError,
    fetchRiskAssessmentReport: riskAssessment.fetchRiskAssessmentReport,
    refreshRiskAssessment: riskAssessment.refreshRiskAssessment,

    // Hospital Performance
    hospitalPerformance: hospitalPerformance.hospitalPerformance,
    isHospitalPerformanceLoading: hospitalPerformance.isLoading,
    hospitalPerformanceError: hospitalPerformance.error,
    hasHospitalPerformanceError: hospitalPerformance.hasError,
    refreshHospitalPerformance: hospitalPerformance.refreshHospitalPerformance,

    // Analytics
    adherenceAnalytics: analytics.adherenceAnalytics,
    isAnalyticsLoading: analytics.isLoading,
    analyticsError: analytics.error,
    hasAnalyticsError: analytics.hasError,
    refreshAnalytics: analytics.refreshAnalytics,

    // Combined states
    isLoading,
    hasError,
    error,

    // Actions
    refreshAll,
    clearErrors,
    reset,
  };
}
