'use client';

import { useState, useEffect, useCallback } from 'react';
import { UsersService, type UserListQuery, type UserStats, type UserDetails } from '@/lib/api/users';

interface UseUsersDataOptions {
  autoFetch?: boolean;
  query?: UserListQuery;
}

interface UseUsersDataReturn {
  // Data
  users: UserDetails[];
  stats: UserStats | null;
  selectedUser: UserDetails | null;
  
  // Loading states
  isLoading: boolean;
  isStatsLoading: boolean;
  isUserLoading: boolean;
  
  // Error states
  error: string | null;
  statsError: string | null;
  userError: string | null;
  hasError: boolean;
  
  // Actions
  fetchUsers: (query?: UserListQuery) => Promise<void>;
  fetchStats: () => Promise<void>;
  fetchUser: (id: string) => Promise<void>;
  createUser: (userData: any) => Promise<UserDetails | null>;
  updateUser: (id: string, userData: any) => Promise<UserDetails | null>;
  deleteUser: (id: string) => Promise<boolean>;
  suspendUser: (id: string) => Promise<UserDetails | null>;
  activateUser: (id: string) => Promise<UserDetails | null>;
  resetUserPassword: (id: string) => Promise<boolean>;
  refreshAll: () => Promise<void>;
  clearSelectedUser: () => void;
}

/**
 * Hook for managing users data in admin dashboard
 */
export function useUsersData(options: UseUsersDataOptions = {}): UseUsersDataReturn {
  const { autoFetch = false, query = {} } = options;

  // State
  const [users, setUsers] = useState<UserDetails[]>([]);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [selectedUser, setSelectedUser] = useState<UserDetails | null>(null);
  
  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [isStatsLoading, setIsStatsLoading] = useState(false);
  const [isUserLoading, setIsUserLoading] = useState(false);
  
  // Error states
  const [error, setError] = useState<string | null>(null);
  const [statsError, setStatsError] = useState<string | null>(null);
  const [userError, setUserError] = useState<string | null>(null);

  // Computed
  const hasError = !!(error || statsError || userError);

  // Fetch users
  const fetchUsers = useCallback(async (fetchQuery: UserListQuery = {}) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const finalQuery = { ...query, ...fetchQuery };
      const data = await UsersService.getUsers(finalQuery);
      setUsers(data);
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to fetch users';
      setError(errorMessage);
      console.error('Error fetching users:', err);
    } finally {
      setIsLoading(false);
    }
  }, [query]);

  // Fetch stats
  const fetchStats = useCallback(async () => {
    setIsStatsLoading(true);
    setStatsError(null);
    
    try {
      const data = await UsersService.getUserStats();
      setStats(data);
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to fetch user statistics';
      setStatsError(errorMessage);
      console.error('Error fetching user stats:', err);
    } finally {
      setIsStatsLoading(false);
    }
  }, []);

  // Fetch single user
  const fetchUser = useCallback(async (id: string) => {
    setIsUserLoading(true);
    setUserError(null);
    
    try {
      const data = await UsersService.getUser(id);
      setSelectedUser(data);
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to fetch user details';
      setUserError(errorMessage);
      console.error('Error fetching user:', err);
    } finally {
      setIsUserLoading(false);
    }
  }, []);

  // Create user
  const createUser = useCallback(async (userData: any): Promise<UserDetails | null> => {
    try {
      const newUser = await UsersService.createUser(userData);
      // Refresh users list
      await fetchUsers();
      await fetchStats();
      return newUser;
    } catch (err: any) {
      console.error('Error creating user:', err);
      return null;
    }
  }, [fetchUsers, fetchStats]);

  // Update user
  const updateUser = useCallback(async (id: string, userData: any): Promise<UserDetails | null> => {
    try {
      const updatedUser = await UsersService.updateUser(id, userData);
      // Update local state
      setUsers(prev => prev.map(user => user.id === id ? updatedUser : user));
      if (selectedUser?.id === id) {
        setSelectedUser(updatedUser);
      }
      await fetchStats();
      return updatedUser;
    } catch (err: any) {
      console.error('Error updating user:', err);
      return null;
    }
  }, [selectedUser, fetchStats]);

  // Delete user
  const deleteUser = useCallback(async (id: string): Promise<boolean> => {
    try {
      await UsersService.deleteUser(id);
      // Remove from local state
      setUsers(prev => prev.filter(user => user.id !== id));
      if (selectedUser?.id === id) {
        setSelectedUser(null);
      }
      await fetchStats();
      return true;
    } catch (err: any) {
      console.error('Error deleting user:', err);
      return false;
    }
  }, [selectedUser, fetchStats]);

  // Suspend user
  const suspendUser = useCallback(async (id: string): Promise<UserDetails | null> => {
    try {
      const updatedUser = await UsersService.suspendUser(id);
      // Update local state
      setUsers(prev => prev.map(user => user.id === id ? updatedUser : user));
      if (selectedUser?.id === id) {
        setSelectedUser(updatedUser);
      }
      await fetchStats();
      return updatedUser;
    } catch (err: any) {
      console.error('Error suspending user:', err);
      return null;
    }
  }, [selectedUser, fetchStats]);

  // Activate user
  const activateUser = useCallback(async (id: string): Promise<UserDetails | null> => {
    try {
      const updatedUser = await UsersService.activateUser(id);
      // Update local state
      setUsers(prev => prev.map(user => user.id === id ? updatedUser : user));
      if (selectedUser?.id === id) {
        setSelectedUser(updatedUser);
      }
      await fetchStats();
      return updatedUser;
    } catch (err: any) {
      console.error('Error activating user:', err);
      return null;
    }
  }, [selectedUser, fetchStats]);

  // Reset user password
  const resetUserPassword = useCallback(async (id: string): Promise<boolean> => {
    try {
      await UsersService.resetUserPassword(id);
      return true;
    } catch (err: any) {
      console.error('Error resetting user password:', err);
      return false;
    }
  }, []);

  // Refresh all data
  const refreshAll = useCallback(async () => {
    await Promise.all([
      fetchUsers(),
      fetchStats(),
    ]);
  }, [fetchUsers, fetchStats]);

  // Clear selected user
  const clearSelectedUser = useCallback(() => {
    setSelectedUser(null);
    setUserError(null);
  }, []);

  // Auto-fetch on mount
  useEffect(() => {
    if (autoFetch) {
      refreshAll();
    }
  }, [autoFetch, refreshAll]);

  return {
    // Data
    users,
    stats,
    selectedUser,
    
    // Loading states
    isLoading,
    isStatsLoading,
    isUserLoading,
    
    // Error states
    error,
    statsError,
    userError,
    hasError,
    
    // Actions
    fetchUsers,
    fetchStats,
    fetchUser,
    createUser,
    updateUser,
    deleteUser,
    suspendUser,
    activateUser,
    resetUserPassword,
    refreshAll,
    clearSelectedUser,
  };
}
