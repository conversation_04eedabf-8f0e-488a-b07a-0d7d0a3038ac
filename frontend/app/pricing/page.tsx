'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  CheckCircle, 
  XCircle, 
  Pill, 
  Stethoscope, 
  Building2, 
  Landmark, 
  ArrowRight, 
  Zap, 
  Shield, 
  Bell, 
  Users, 
  Activity, 
  FileText, 
  BarChart3, 
  Calendar, 
  MessageSquare, 
  Heart, 
  Crown, 
  Sparkles,
  Target,
  Menu,
  X
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

// Plan features by role
const planFeatures = {
  patient: {
    free: [
      'Basic medication tracking',
      'Daily reminders',
      'Prescription uploads (3 per month)',
      'Basic adherence stats',
      'Email support'
    ],
    premium: [
      'Unlimited medication tracking',
      'Smart reminders (SMS, push, voice)',
      'Unlimited prescription uploads',
      'Advanced adherence analytics',
      'Priority support',
      'Family member access',
      'Health condition insights',
      'Export reports'
    ],
    family: [
      'Everything in Premium',
      'Up to 5 family members',
      'Family adherence dashboard',
      'Caregiver notifications',
      'Shared calendar',
      'Emergency contact alerts',
      'Medication inventory management',
      'Dedicated account manager'
    ]
  },
  doctor: {
    free: [
      'Up to 20 patients',
      'Basic patient monitoring',
      'Standard adherence reports',
      'Email notifications',
      'Email support'
    ],
    professional: [
      'Up to 100 patients',
      'Advanced patient monitoring',
      'Detailed adherence analytics',
      'Priority notifications',
      'Priority support',
      'Custom prescription templates',
      'Patient risk stratification',
      'Export reports'
    ],
    enterprise: [
      'Unlimited patients',
      'Comprehensive monitoring suite',
      'Real-time adherence alerts',
      'AI-powered insights',
      'Dedicated account manager',
      'Custom integrations',
      'Advanced analytics',
      'Team collaboration tools'
    ]
  },
  hospital: {
    professional: [
      'Up to 50 doctors',
      'Department-level analytics',
      'Patient adherence tracking',
      'Basic risk stratification',
      'Standard reports',
      'Email & phone support',
      'Basic API access',
      'Monthly insights'
    ],
    enterprise: [
      'Up to 200 doctors',
      'Advanced analytics dashboard',
      'Real-time adherence monitoring',
      'Advanced risk stratification',
      'Custom reports',
      'Priority support',
      'Full API access',
      'Weekly insights'
    ],
    ultimate: [
      'Unlimited doctors',
      'Comprehensive analytics suite',
      'Real-time monitoring & alerts',
      'AI-powered risk prediction',
      'Custom reporting & dashboards',
      'Dedicated account manager',
      'Enterprise API & integrations',
      'Daily insights & recommendations'
    ]
  },
  insurance: {
    standard: [
      'Up to 1,000 members',
      'Basic adherence monitoring',
      'Monthly adherence reports',
      'Provider network tracking',
      'Standard support',
      'Basic API access',
      'Quarterly insights',
      'Data export capabilities'
    ],
    professional: [
      'Up to 10,000 members',
      'Advanced adherence analytics',
      'Weekly adherence reports',
      'Provider performance metrics',
      'Priority support',
      'Full API access',
      'Monthly insights',
      'Custom data exports'
    ],
    enterprise: [
      'Unlimited members',
      'Comprehensive analytics suite',
      'Real-time adherence monitoring',
      'Advanced provider analytics',
      'Dedicated account manager',
      'Enterprise API & integrations',
      'Weekly insights & recommendations',
      'Custom reporting & dashboards'
    ]
  }
};

// Plan pricing
const planPricing = {
  patient: {
    free: {
      monthly: 0,
      annually: 0
    },
    premium: {
      monthly: 9.99,
      annually: 99.99
    },
    family: {
      monthly: 19.99,
      annually: 199.99
    }
  },
  doctor: {
    free: {
      monthly: 0,
      annually: 0
    },
    professional: {
      monthly: 49.99,
      annually: 499.99
    },
    enterprise: {
      monthly: 99.99,
      annually: 999.99
    }
  },
  hospital: {
    professional: {
      monthly: 299.99,
      annually: 2999.99
    },
    enterprise: {
      monthly: 599.99,
      annually: 5999.99
    },
    ultimate: {
      monthly: 999.99,
      annually: 9999.99
    }
  },
  insurance: {
    standard: {
      monthly: 499.99,
      annually: 4999.99
    },
    professional: {
      monthly: 999.99,
      annually: 9999.99
    },
    enterprise: {
      monthly: 1999.99,
      annually: 19999.99
    }
  }
};

// Plan colors
const planColors = {
  free: {
    badge: 'bg-blue-100 text-blue-800 border-blue-200',
    button: 'bg-blue-600 hover:bg-blue-700',
    card: 'border-blue-200 dark:border-blue-800',
    gradient: 'from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20'
  },
  premium: {
    badge: 'bg-purple-100 text-purple-800 border-purple-200',
    button: 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700',
    card: 'border-purple-200 dark:border-purple-800',
    gradient: 'from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20'
  },
  family: {
    badge: 'bg-pink-100 text-pink-800 border-pink-200',
    button: 'bg-gradient-to-r from-pink-600 to-rose-600 hover:from-pink-700 hover:to-rose-700',
    card: 'border-pink-200 dark:border-pink-800',
    gradient: 'from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20'
  },
  professional: {
    badge: 'bg-green-100 text-green-800 border-green-200',
    button: 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700',
    card: 'border-green-200 dark:border-green-800',
    gradient: 'from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20'
  },
  enterprise: {
    badge: 'bg-orange-100 text-orange-800 border-orange-200',
    button: 'bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700',
    card: 'border-orange-200 dark:border-orange-800',
    gradient: 'from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20'
  },
  ultimate: {
    badge: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    button: 'bg-gradient-to-r from-yellow-600 to-amber-600 hover:from-yellow-700 hover:to-amber-700',
    card: 'border-yellow-200 dark:border-yellow-800',
    gradient: 'from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20'
  },
  standard: {
    badge: 'bg-cyan-100 text-cyan-800 border-cyan-200',
    button: 'bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-700 hover:to-blue-700',
    card: 'border-cyan-200 dark:border-cyan-800',
    gradient: 'from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20'
  }
};

// Plan icons
const planIcons = {
  patient: {
    free: Pill,
    premium: Heart,
    family: Users
  },
  doctor: {
    free: Stethoscope,
    professional: Activity,
    enterprise: BarChart3
  },
  hospital: {
    professional: Building2,
    enterprise: Shield,
    ultimate: Crown
  },
  insurance: {
    standard: Landmark,
    professional: FileText,
    enterprise: Target
  }
};

export default function PricingPage() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annually'>('monthly');
  const [selectedRole, setSelectedRole] = useState<'patient' | 'doctor' | 'hospital' | 'insurance'>('patient');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleBillingCycleChange = () => {
    setBillingCycle(billingCycle === 'monthly' ? 'annually' : 'monthly');
  };

  const getDiscountPercentage = () => {
    return 16.7; // Approximately 2 months free for annual billing
  };

  const getPlanIcon = (plan: string): React.ComponentType<any> => {
    const Icon = planIcons[selectedRole][plan as keyof typeof planIcons[typeof selectedRole]] || Pill;
    return Icon;
  };

  const getFeatureIcon = (included: boolean) => {
    return included ? 
      <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" /> : 
      <XCircle className="h-4 w-4 text-red-400 flex-shrink-0" />;
  };

  const getPlansForRole = () => {
    switch (selectedRole) {
      case 'patient':
        return ['free', 'premium', 'family'];
      case 'doctor':
        return ['free', 'professional', 'enterprise'];
      case 'hospital':
        return ['professional', 'enterprise', 'ultimate'];
      case 'insurance':
        return ['standard', 'professional', 'enterprise'];
      default:
        return ['free', 'premium', 'enterprise'];
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'patient':
        return <Pill className="h-5 w-5" />;
      case 'doctor':
        return <Stethoscope className="h-5 w-5" />;
      case 'hospital':
        return <Building2 className="h-5 w-5" />;
      case 'insurance':
        return <Landmark className="h-5 w-5" />;
      default:
        return <Pill className="h-5 w-5" />;
    }
  };

  const getPopularPlan = () => {
    switch (selectedRole) {
      case 'patient':
        return 'premium';
      case 'doctor':
        return 'professional';
      case 'hospital':
        return 'enterprise';
      case 'insurance':
        return 'professional';
      default:
        return 'premium';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="sticky top-0 z-50 border-b bg-white/80 backdrop-blur-xl">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <Link href="/">
                <div className="flex items-center space-x-3">
                  <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg">
                    <Pill className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                      MedCare
                    </h1>
                    <p className="text-xs text-gray-500 -mt-1 hidden sm:block">AI-Powered Healthcare</p>
                  </div>
                </div>
              </Link>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-4">
              <Link href="/pricing">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
                  Pricing
                </Button>
              </Link>
              <Link href="/auth/login">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
                  Sign In
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200">
                  Get Started
                </Button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="p-2"
              >
                {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </Button>
            </div>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden border-t bg-white/95 backdrop-blur-xl">
              <div className="px-4 py-6 space-y-4">
                <Link href="/pricing" onClick={() => setMobileMenuOpen(false)}>
                  <Button variant="ghost" className="w-full justify-start text-gray-600 hover:text-gray-900">
                    Pricing
                  </Button>
                </Link>
                <Link href="/auth/login" onClick={() => setMobileMenuOpen(false)}>
                  <Button variant="ghost" className="w-full justify-start text-gray-600 hover:text-gray-900">
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth/register" onClick={() => setMobileMenuOpen(false)}>
                  <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg">
                    Get Started
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </div>
      </header>

      <main className="py-16 sm:py-24">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Pricing Header */}
          <div className="text-center mb-12 sm:mb-20">
            <Badge variant="outline" className="mb-4 sm:mb-6 px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium border-blue-200 text-blue-700 bg-blue-50">
              <Zap className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              Simple, Transparent Pricing
            </Badge>
            
            <h1 className="text-3xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-6 sm:mb-8 leading-tight">
              <span className="bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent">
                Choose the Perfect Plan
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                For Your Needs
              </span>
            </h1>
            
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
              Flexible plans designed for patients, doctors, hospitals, and insurance providers.
              Start with our free tier or choose a plan that scales with your needs.
            </p>
          </div>

          {/* Billing Toggle */}
          <div className="flex justify-center items-center mb-12">
            <span className={`text-sm ${billingCycle === 'monthly' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
              Monthly
            </span>
            <div className="mx-3">
              <Switch
                checked={billingCycle === 'annually'}
                onCheckedChange={handleBillingCycleChange}
              />
            </div>
            <div className="flex items-center">
              <span className={`text-sm ${billingCycle === 'annually' ? 'text-gray-900 font-medium' : 'text-gray-500'}`}>
                Annually
              </span>
              <Badge className="ml-2 bg-green-100 text-green-800 border-green-200">
                Save {getDiscountPercentage()}%
              </Badge>
            </div>
          </div>

          {/* Role Tabs */}
          <Tabs 
            defaultValue="patient" 
            value={selectedRole}
            onValueChange={(value) => setSelectedRole(value as typeof selectedRole)}
            className="mb-12"
          >
            <div className="flex justify-center">
              <TabsList className="grid grid-cols-2 sm:grid-cols-4 w-full max-w-2xl">
                <TabsTrigger value="patient" className="flex items-center space-x-2 py-3">
                  <Pill className="h-4 w-4" />
                  <span>Patients</span>
                </TabsTrigger>
                <TabsTrigger value="doctor" className="flex items-center space-x-2 py-3">
                  <Stethoscope className="h-4 w-4" />
                  <span>Doctors</span>
                </TabsTrigger>
                <TabsTrigger value="hospital" className="flex items-center space-x-2 py-3">
                  <Building2 className="h-4 w-4" />
                  <span>Hospitals</span>
                </TabsTrigger>
                <TabsTrigger value="insurance" className="flex items-center space-x-2 py-3">
                  <Landmark className="h-4 w-4" />
                  <span>Insurance</span>
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Pricing Cards */}
            <div className="mt-12">
              <div className="grid gap-6 sm:gap-8 md:grid-cols-2 lg:grid-cols-3">
                {getPlansForRole().map((plan) => {
                  const IconComponent = getPlanIcon(plan);
                  const price = (planPricing as any)[selectedRole]?.[plan]?.[billingCycle] || 0;
                  const features = (planFeatures as any)[selectedRole]?.[plan] || [];
                  const colors = planColors[plan as keyof typeof planColors];
                  const isPopular = plan === getPopularPlan();
                  
                  return (
                    <Card 
                      key={plan} 
                      className={`relative overflow-hidden border-2 ${colors.card} hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 bg-gradient-to-br ${colors.gradient}`}
                    >
                      {isPopular && (
                        <div className="absolute top-0 right-0">
                          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-xs font-bold px-4 py-1 transform rotate-45 translate-x-[30%] translate-y-[40%] shadow-lg">
                            POPULAR
                          </div>
                        </div>
                      )}
                      
                      <CardHeader className="pb-3 sm:pb-4 relative z-10 p-4 sm:p-6">
                        <div className="flex justify-between items-start">
                          <div className={`w-12 h-12 sm:w-16 sm:h-16 rounded-2xl sm:rounded-3xl bg-gradient-to-br ${plan === 'free' ? 'from-blue-500 to-indigo-500' : plan === 'premium' || plan === 'professional' ? 'from-purple-500 to-indigo-500' : plan === 'family' ? 'from-pink-500 to-rose-500' : plan === 'enterprise' ? 'from-orange-500 to-red-500' : plan === 'ultimate' ? 'from-yellow-500 to-amber-500' : 'from-cyan-500 to-blue-500'} flex items-center justify-center mb-4 sm:mb-6 shadow-lg`}>
                            <IconComponent className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                          </div>
                          <Badge variant="outline" className={`modern-badge ${colors.badge} capitalize`}>
                            {plan}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl sm:text-2xl font-bold text-gray-900 mt-2 capitalize">
                          {plan} Plan
                        </CardTitle>
                        <CardDescription className="text-gray-600 mt-2">
                          {plan === 'free' ? 'Basic features for individuals' : 
                           plan === 'premium' ? 'Enhanced features for individuals' :
                           plan === 'family' ? 'Complete coverage for families' :
                           plan === 'professional' ? 'Advanced tools for professionals' :
                           plan === 'enterprise' ? 'Comprehensive solution for organizations' :
                           plan === 'ultimate' ? 'All-inclusive enterprise package' :
                           plan === 'standard' ? 'Essential tools for insurers' : 
                           'Complete solution for your needs'}
                        </CardDescription>
                      </CardHeader>
                      
                      <CardContent className="p-4 sm:p-6 pt-0">
                        <div className="mb-6">
                          <div className="flex items-baseline">
                            <span className="text-3xl sm:text-4xl font-bold text-gray-900">
                              ${price === 0 ? '0' : price.toFixed(2)}
                            </span>
                            {price > 0 && (
                              <span className="text-gray-600 ml-2">
                                /{billingCycle === 'monthly' ? 'mo' : 'yr'}
                              </span>
                            )}
                          </div>
                          {price === 0 ? (
                            <p className="text-sm text-gray-600 mt-1">Free forever</p>
                          ) : (
                            <p className="text-sm text-gray-600 mt-1">
                              {billingCycle === 'annually' ? 'Billed annually' : 'Billed monthly'}
                            </p>
                          )}
                        </div>
                        
                        <div className="space-y-4">
                          {features.map((feature: string, index: number) => (
                            <div key={index} className="flex items-start space-x-3">
                              {getFeatureIcon(true)}
                              <span className="text-sm text-gray-700">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                      
                      <CardFooter className="p-4 sm:p-6 pt-0">
                        <Link href={`/auth/register?role=${selectedRole}&plan=${plan}`} className="w-full">
                          <Button 
                            className={`w-full ${plan === 'free' ? 'bg-blue-600 hover:bg-blue-700' : colors.button} text-white shadow-lg hover:shadow-xl transition-all duration-300 py-2.5 sm:py-3 text-base sm:text-lg font-semibold`}
                          >
                            {price === 0 ? 'Get Started' : 'Choose Plan'}
                            <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                          </Button>
                        </Link>
                      </CardFooter>
                    </Card>
                  );
                })}
              </div>
            </div>
          </Tabs>

          {/* Feature Comparison */}
          <div className="mt-20 mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Compare Plan Features</h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                See which plan is right for you with our detailed feature comparison
              </p>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="text-left p-4 bg-gray-50 border-b-2 border-gray-200">Feature</th>
                    {getPlansForRole().map(plan => (
                      <th key={plan} className="text-center p-4 bg-gray-50 border-b-2 border-gray-200 capitalize">
                        {plan}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {/* Feature rows - dynamically generated based on role */}
                  {selectedRole === 'patient' && (
                    <>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Medication Tracking</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Unlimited</td>
                        <td className="text-center p-4 border-b border-gray-200">Unlimited</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Reminders</td>
                        <td className="text-center p-4 border-b border-gray-200">Email only</td>
                        <td className="text-center p-4 border-b border-gray-200">Email, SMS, Push</td>
                        <td className="text-center p-4 border-b border-gray-200">Email, SMS, Push, Voice</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Prescription Uploads</td>
                        <td className="text-center p-4 border-b border-gray-200">3/month</td>
                        <td className="text-center p-4 border-b border-gray-200">Unlimited</td>
                        <td className="text-center p-4 border-b border-gray-200">Unlimited</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Analytics</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Family Members</td>
                        <td className="text-center p-4 border-b border-gray-200">—</td>
                        <td className="text-center p-4 border-b border-gray-200">—</td>
                        <td className="text-center p-4 border-b border-gray-200">Up to 5</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Caregiver Access</td>
                        <td className="text-center p-4 border-b border-gray-200">—</td>
                        <td className="text-center p-4 border-b border-gray-200">Limited</td>
                        <td className="text-center p-4 border-b border-gray-200">Full Access</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Health Insights</td>
                        <td className="text-center p-4 border-b border-gray-200">—</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Support</td>
                        <td className="text-center p-4 border-b border-gray-200">Email</td>
                        <td className="text-center p-4 border-b border-gray-200">Priority Email</td>
                        <td className="text-center p-4 border-b border-gray-200">Dedicated Support</td>
                      </tr>
                    </>
                  )}
                  
                  {selectedRole === 'doctor' && (
                    <>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Patient Limit</td>
                        <td className="text-center p-4 border-b border-gray-200">20</td>
                        <td className="text-center p-4 border-b border-gray-200">100</td>
                        <td className="text-center p-4 border-b border-gray-200">Unlimited</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Patient Monitoring</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced</td>
                        <td className="text-center p-4 border-b border-gray-200">Comprehensive</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Adherence Alerts</td>
                        <td className="text-center p-4 border-b border-gray-200">Daily Summary</td>
                        <td className="text-center p-4 border-b border-gray-200">Real-time</td>
                        <td className="text-center p-4 border-b border-gray-200">Real-time + AI Insights</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Analytics</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced</td>
                        <td className="text-center p-4 border-b border-gray-200">AI-powered</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Prescription Templates</td>
                        <td className="text-center p-4 border-b border-gray-200">Standard</td>
                        <td className="text-center p-4 border-b border-gray-200">Custom</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced Custom</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Risk Stratification</td>
                        <td className="text-center p-4 border-b border-gray-200">—</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Team Collaboration</td>
                        <td className="text-center p-4 border-b border-gray-200">—</td>
                        <td className="text-center p-4 border-b border-gray-200">Limited</td>
                        <td className="text-center p-4 border-b border-gray-200">Full Access</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Support</td>
                        <td className="text-center p-4 border-b border-gray-200">Email</td>
                        <td className="text-center p-4 border-b border-gray-200">Priority Email & Phone</td>
                        <td className="text-center p-4 border-b border-gray-200">Dedicated Manager</td>
                      </tr>
                    </>
                  )}
                  
                  {selectedRole === 'hospital' && (
                    <>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Doctor Limit</td>
                        <td className="text-center p-4 border-b border-gray-200">50</td>
                        <td className="text-center p-4 border-b border-gray-200">200</td>
                        <td className="text-center p-4 border-b border-gray-200">Unlimited</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Analytics Level</td>
                        <td className="text-center p-4 border-b border-gray-200">Department</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced</td>
                        <td className="text-center p-4 border-b border-gray-200">Comprehensive</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Adherence Monitoring</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Real-time</td>
                        <td className="text-center p-4 border-b border-gray-200">Real-time + Alerts</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Risk Stratification</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced</td>
                        <td className="text-center p-4 border-b border-gray-200">AI-powered</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Reporting</td>
                        <td className="text-center p-4 border-b border-gray-200">Standard</td>
                        <td className="text-center p-4 border-b border-gray-200">Custom</td>
                        <td className="text-center p-4 border-b border-gray-200">Custom + Dashboards</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">API Access</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Full</td>
                        <td className="text-center p-4 border-b border-gray-200">Enterprise</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Insights</td>
                        <td className="text-center p-4 border-b border-gray-200">Monthly</td>
                        <td className="text-center p-4 border-b border-gray-200">Weekly</td>
                        <td className="text-center p-4 border-b border-gray-200">Daily</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Support</td>
                        <td className="text-center p-4 border-b border-gray-200">Email & Phone</td>
                        <td className="text-center p-4 border-b border-gray-200">Priority</td>
                        <td className="text-center p-4 border-b border-gray-200">Dedicated Manager</td>
                      </tr>
                    </>
                  )}
                  
                  {selectedRole === 'insurance' && (
                    <>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Member Limit</td>
                        <td className="text-center p-4 border-b border-gray-200">1,000</td>
                        <td className="text-center p-4 border-b border-gray-200">10,000</td>
                        <td className="text-center p-4 border-b border-gray-200">Unlimited</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Adherence Monitoring</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced</td>
                        <td className="text-center p-4 border-b border-gray-200">Comprehensive</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Adherence Reports</td>
                        <td className="text-center p-4 border-b border-gray-200">Monthly</td>
                        <td className="text-center p-4 border-b border-gray-200">Weekly</td>
                        <td className="text-center p-4 border-b border-gray-200">Real-time</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Provider Analytics</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Performance Metrics</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced Analytics</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">API Access</td>
                        <td className="text-center p-4 border-b border-gray-200">Basic</td>
                        <td className="text-center p-4 border-b border-gray-200">Full</td>
                        <td className="text-center p-4 border-b border-gray-200">Enterprise</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Insights</td>
                        <td className="text-center p-4 border-b border-gray-200">Quarterly</td>
                        <td className="text-center p-4 border-b border-gray-200">Monthly</td>
                        <td className="text-center p-4 border-b border-gray-200">Weekly</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Data Exports</td>
                        <td className="text-center p-4 border-b border-gray-200">Standard</td>
                        <td className="text-center p-4 border-b border-gray-200">Custom</td>
                        <td className="text-center p-4 border-b border-gray-200">Advanced Custom</td>
                      </tr>
                      <tr>
                        <td className="p-4 border-b border-gray-200 font-medium">Support</td>
                        <td className="text-center p-4 border-b border-gray-200">Standard</td>
                        <td className="text-center p-4 border-b border-gray-200">Priority</td>
                        <td className="text-center p-4 border-b border-gray-200">Dedicated Manager</td>
                      </tr>
                    </>
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mt-20">
            <div className="text-center mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Find answers to common questions about our plans and pricing
              </p>
            </div>
            
            <div className="grid gap-6 md:grid-cols-2 max-w-4xl mx-auto">
              <div className="modern-card p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Can I change plans later?</h3>
                <p className="text-gray-700">
                  Yes, you can upgrade, downgrade, or change your plan at any time. Changes take effect at the start of your next billing cycle.
                </p>
              </div>
              
              <div className="modern-card p-6 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Is there a free trial?</h3>
                <p className="text-gray-700">
                  Yes, all paid plans come with a 14-day free trial. No credit card required to start your trial.
                </p>
              </div>
              
              <div className="modern-card p-6 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">How does billing work?</h3>
                <p className="text-gray-700">
                  You can choose between monthly or annual billing. Annual plans offer a significant discount equivalent to 2 months free.
                </p>
              </div>
              
              <div className="modern-card p-6 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Can I get a custom plan?</h3>
                <p className="text-gray-700">
                  Yes, for hospitals and insurance providers with specific needs, we offer custom enterprise solutions. Contact our sales team for details.
                </p>
              </div>
              
              <div className="modern-card p-6 bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-800">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">What payment methods do you accept?</h3>
                <p className="text-gray-700">
                  We accept all major credit cards, PayPal, and for enterprise plans, we also support invoicing and bank transfers.
                </p>
              </div>
              
              <div className="modern-card p-6 bg-gradient-to-br from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 border border-cyan-200 dark:border-cyan-800">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Can I cancel anytime?</h3>
                <p className="text-gray-700">
                  Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your current billing period.
                </p>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div className="mt-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 sm:p-12 text-white text-center">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-2xl sm:text-3xl font-bold mb-4">Ready to improve medication adherence?</h2>
              <p className="text-lg sm:text-xl text-blue-100 mb-8">
                Join thousands of healthcare providers and patients who trust MedCare to improve health outcomes
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth/register">
                  <Button size="lg" className="w-full sm:w-auto bg-white text-blue-600 hover:bg-gray-100 shadow-xl hover:shadow-2xl transition-all duration-300 px-8 py-3 text-lg font-semibold">
                    Start Free Trial
                    <Sparkles className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button 
                    size="lg" 
                    variant="outline" 
                    className="w-full sm:w-auto border-2 border-white bg-transparent text-white hover:bg-white hover:text-blue-600 transition-all duration-300 px-8 py-3 text-lg font-semibold"
                  >
                    Contact Sales
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 sm:py-16 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4 mb-8 sm:mb-12">
            <div className="sm:col-span-2 lg:col-span-1">
              <div className="flex items-center space-x-3 mb-4 sm:mb-6">
                <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center">
                  <Pill className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
                </div>
                <div>
                  <span className="text-xl sm:text-2xl font-bold">MedCare</span>
                  <p className="text-xs sm:text-sm text-gray-400 -mt-1">AI-Powered Healthcare</p>
                </div>
              </div>
              <p className="text-gray-400 text-base sm:text-lg leading-relaxed max-w-md">
                Transforming healthcare through intelligent medicine adherence solutions.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold text-base sm:text-lg mb-3 sm:mb-4">Product</h3>
              <ul className="space-y-2 sm:space-y-3 text-gray-400 text-sm sm:text-base">
                <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><a href="#" className="hover:text-white transition-colors">API</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Integrations</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-base sm:text-lg mb-3 sm:mb-4">Company</h3>
              <ul className="space-y-2 sm:space-y-3 text-gray-400 text-sm sm:text-base">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-base sm:text-lg mb-3 sm:mb-4">Resources</h3>
              <ul className="space-y-2 sm:space-y-3 text-gray-400 text-sm sm:text-base">
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-6 sm:pt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <p className="text-gray-400 text-sm sm:text-base text-center sm:text-left">
              © 2024 MedCare. All rights reserved.
            </p>
            <div className="flex space-x-4 sm:space-x-6 text-gray-400 text-sm sm:text-base">
              <a href="#" className="hover:text-white transition-colors">Terms</a>
              <a href="#" className="hover:text-white transition-colors">Privacy</a>
              <a href="#" className="hover:text-white transition-colors">Security</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}