'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Calendar, 
  Clock, 
  Plus, 
  Search, 
  Filter,
  User,
  Phone,
  MessageSquare,
  Video,
  MapPin,
  Edit,
  Trash2,
  CheckCircle,
  AlertCircle,
  CalendarDays,
  Timer,
  Users,
  Sparkles,
  Target,
  Activity,
  TrendingUp
} from 'lucide-react';
import { useState } from 'react';
import { format, addDays, startOfWeek, addHours, isSameDay } from 'date-fns';

// Mock appointment data
const mockAppointments = [
  {
    id: '1',
    patientName: 'Sarah Johnson',
    patientId: '1',
    avatar: 'SJ',
    type: 'Follow-up',
    date: new Date(),
    time: '09:00',
    duration: 30,
    status: 'confirmed',
    location: 'Room 101',
    notes: 'Diabetes check-up, review blood glucose logs',
    phone: '+****************',
    condition: 'Diabetes Type 2'
  },
  {
    id: '2',
    patientName: 'Michael Chen',
    patientId: '2',
    avatar: 'MC',
    type: 'Consultation',
    date: new Date(),
    time: '10:30',
    duration: 45,
    status: 'confirmed',
    location: 'Room 102',
    notes: 'Blood pressure monitoring, medication adjustment',
    phone: '+****************',
    condition: 'Hypertension'
  },
  {
    id: '3',
    patientName: 'Emily Rodriguez',
    patientId: '3',
    avatar: 'ER',
    type: 'Check-up',
    date: new Date(),
    time: '14:00',
    duration: 30,
    status: 'pending',
    location: 'Room 101',
    notes: 'Asthma control assessment, inhaler technique review',
    phone: '+****************',
    condition: 'Asthma'
  },
  {
    id: '4',
    patientName: 'Robert Wilson',
    patientId: '4',
    avatar: 'RW',
    type: 'Emergency',
    date: addDays(new Date(), 1),
    time: '11:00',
    duration: 60,
    status: 'urgent',
    location: 'Room 103',
    notes: 'Chest pain evaluation, ECG required',
    phone: '+****************',
    condition: 'Heart Disease'
  },
  {
    id: '5',
    patientName: 'Jennifer Davis',
    patientId: '5',
    avatar: 'JD',
    type: 'Therapy',
    date: addDays(new Date(), 1),
    time: '15:30',
    duration: 45,
    status: 'confirmed',
    location: 'Room 104',
    notes: 'Anxiety management session, medication review',
    phone: '+****************',
    condition: 'Anxiety'
  },
  {
    id: '6',
    patientName: 'Thomas Anderson',
    patientId: '6',
    avatar: 'TA',
    type: 'Follow-up',
    date: addDays(new Date(), 2),
    time: '09:30',
    duration: 30,
    status: 'confirmed',
    location: 'Room 101',
    notes: 'COPD management, spirometry test',
    phone: '+****************',
    condition: 'COPD'
  }
];

const timeSlots = [
  '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
  '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
  '16:00', '16:30', '17:00', '17:30'
];

const appointmentTypes = [
  'Consultation', 'Follow-up', 'Check-up', 'Emergency', 'Therapy', 'Procedure'
];

const mockPatients = [
  { id: '1', name: 'Sarah Johnson' },
  { id: '2', name: 'Michael Chen' },
  { id: '3', name: 'Emily Rodriguez' },
  { id: '4', name: 'Robert Wilson' },
  { id: '5', name: 'Jennifer Davis' },
  { id: '6', name: 'Thomas Anderson' }
];

export default function SchedulePage() {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState('day');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isNewAppointmentOpen, setIsNewAppointmentOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState('');
  const [appointmentType, setAppointmentType] = useState('');
  const [appointmentTime, setAppointmentTime] = useState('');
  const [duration, setDuration] = useState('30');
  const [notes, setNotes] = useState('');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'pending': return 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100';
      case 'urgent': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      case 'completed': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800 border-green-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'completed': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Filter appointments based on selected date and filters
  const filteredAppointments = mockAppointments
    .filter(appointment => {
      const matchesDate = isSameDay(appointment.date, selectedDate);
      const matchesSearch = appointment.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          appointment.type.toLowerCase().includes(searchTerm.toLowerCase());
      
      if (filterStatus === 'all') return matchesDate && matchesSearch;
      return matchesDate && matchesSearch && appointment.status === filterStatus;
    })
    .sort((a, b) => a.time.localeCompare(b.time));

  const handleCreateAppointment = () => {
    // Handle appointment creation logic here
    setIsNewAppointmentOpen(false);
    setSelectedPatient('');
    setAppointmentType('');
    setAppointmentTime('');
    setDuration('30');
    setNotes('');
  };

  const getStatusCounts = () => {
    const todayAppointments = mockAppointments.filter(apt => isSameDay(apt.date, selectedDate));
    return {
      total: todayAppointments.length,
      confirmed: todayAppointments.filter(apt => apt.status === 'confirmed').length,
      pending: todayAppointments.filter(apt => apt.status === 'pending').length,
      urgent: todayAppointments.filter(apt => apt.status === 'urgent').length,
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/doctor' },
            { label: 'Schedule', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Calendar className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Schedule Management</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Manage your appointments for {format(selectedDate, 'EEEE, MMMM d, yyyy')}
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <CalendarDays className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">View Calendar</span>
              <span className="sm:hidden">Calendar</span>
            </Button>
            <Dialog open={isNewAppointmentOpen} onOpenChange={setIsNewAppointmentOpen}>
              <DialogTrigger asChild>
                <Button className="btn-primary w-full sm:w-auto">
                  <Plus className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">New Appointment</span>
                  <span className="sm:hidden">New</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="modern-card max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="flex items-center space-x-2">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                      <Calendar className="h-5 w-5 text-white" />
                    </div>
                    <span>Schedule New Appointment</span>
                  </DialogTitle>
                  <DialogDescription>
                    Create a new appointment for your patient
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="patient">Select Patient</Label>
                      <Select value={selectedPatient} onValueChange={setSelectedPatient}>
                        <SelectTrigger className="bg-muted/30 border-border/50">
                          <SelectValue placeholder="Choose a patient" />
                        </SelectTrigger>
                        <SelectContent>
                          {mockPatients.map((patient) => (
                            <SelectItem key={patient.id} value={patient.id}>
                              {patient.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="type">Appointment Type</Label>
                      <Select value={appointmentType} onValueChange={setAppointmentType}>
                        <SelectTrigger className="bg-muted/30 border-border/50">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          {appointmentTypes.map((type) => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="time">Time</Label>
                      <Select value={appointmentTime} onValueChange={setAppointmentTime}>
                        <SelectTrigger className="bg-muted/30 border-border/50">
                          <SelectValue placeholder="Select time" />
                        </SelectTrigger>
                        <SelectContent>
                          {timeSlots.map((time) => (
                            <SelectItem key={time} value={time}>
                              {time}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="duration">Duration (minutes)</Label>
                      <Select value={duration} onValueChange={setDuration}>
                        <SelectTrigger className="bg-muted/30 border-border/50">
                          <SelectValue placeholder="Select duration" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="15">15 minutes</SelectItem>
                          <SelectItem value="30">30 minutes</SelectItem>
                          <SelectItem value="45">45 minutes</SelectItem>
                          <SelectItem value="60">60 minutes</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      placeholder="Add any notes or special instructions..."
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      className="bg-muted/30 border-border/50 min-h-[80px]"
                    />
                  </div>

                  <div className="flex justify-end space-x-3">
                    <Button variant="outline" onClick={() => setIsNewAppointmentOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateAppointment} className="btn-primary">
                      <Calendar className="h-4 w-4 mr-2" />
                      Schedule Appointment
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Calendar className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{statusCounts.total}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Today's Appointments</p>
                <div className="flex items-center mt-1">
                  <Activity className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">Scheduled</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{statusCounts.confirmed}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Confirmed</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">Ready to go</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-yellow-600">{statusCounts.pending}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Pending</p>
                <div className="flex items-center mt-1">
                  <Timer className="h-3 w-3 text-yellow-500 mr-1" />
                  <span className="text-xs text-yellow-600 font-medium">Awaiting confirmation</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl shadow-lg group-hover:shadow-red-500/25 transition-all duration-300">
                <AlertCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-red-600">{statusCounts.urgent}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Urgent</p>
                <div className="flex items-center mt-1">
                  <AlertCircle className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-xs text-red-600 font-medium">Immediate attention</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Date Navigation & Filters */}
        <Card className="modern-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Date Navigation */}
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setSelectedDate(addDays(selectedDate, -1))}
                  className="hover:bg-accent/50"
                >
                  ←
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => setSelectedDate(new Date())}
                  className="hover:bg-accent/50"
                >
                  Today
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setSelectedDate(addDays(selectedDate, 1))}
                  className="hover:bg-accent/50"
                >
                  →
                </Button>
              </div>

              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search appointments..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-muted/30 border-border/50 focus:bg-background transition-colors"
                  />
                </div>
              </div>
              
              {/* Filter */}
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Appointments</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Appointments List */}
        <div className="grid gap-4 sm:gap-6">
          {filteredAppointments.length === 0 ? (
            <Card className="modern-card">
              <CardContent className="text-center py-12">
                <div className="relative">
                  <Calendar className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                  <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No appointments found</h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm || filterStatus !== 'all' 
                    ? 'Try adjusting your search or filter criteria.'
                    : `No appointments scheduled for ${format(selectedDate, 'MMMM d, yyyy')}.`
                  }
                </p>
                <Button className="btn-primary" onClick={() => setIsNewAppointmentOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Schedule Appointment
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredAppointments.map((appointment) => (
              <Card key={appointment.id} className={`modern-card p-6 bg-gradient-to-r ${getStatusColor(appointment.status)} border hover:scale-[1.01] transition-all duration-300`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-12 w-12 sm:h-16 sm:w-16 ring-2 ring-white/50 shadow-lg">
                      <AvatarImage src={`/avatars/${appointment.avatar}.jpg`} />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold text-lg">
                        {appointment.avatar}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-bold text-lg">{appointment.patientName}</h3>
                      <p className="opacity-80">{appointment.condition}</p>
                      <div className="flex items-center space-x-4 mt-1 text-sm">
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1 opacity-70" />
                          <span className="opacity-80">{appointment.time} ({appointment.duration} min)</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1 opacity-70" />
                          <span className="opacity-80">{appointment.location}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className={`modern-badge ${getStatusBadgeColor(appointment.status)}`}>
                      {appointment.status}
                    </Badge>
                    <Badge variant="outline" className="modern-badge">
                      {appointment.type}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-3">
                  {appointment.notes && (
                    <div className="modern-card p-3 bg-white/20 dark:bg-black/10 backdrop-blur-sm">
                      <h4 className="font-semibold mb-1 text-sm">Notes:</h4>
                      <p className="text-sm opacity-90">{appointment.notes}</p>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 text-sm opacity-80">
                      <Phone className="h-4 w-4" />
                      <span>{appointment.phone}</span>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" className="hover:bg-white/20">
                        <Phone className="h-4 w-4 mr-1" />
                        Call
                      </Button>
                      <Button variant="outline" size="sm" className="hover:bg-white/20">
                        <MessageSquare className="h-4 w-4 mr-1" />
                        Message
                      </Button>
                      <Button variant="outline" size="sm" className="hover:bg-white/20">
                        <Video className="h-4 w-4 mr-1" />
                        Video
                      </Button>
                      <Button variant="outline" size="sm" className="hover:bg-white/20">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}