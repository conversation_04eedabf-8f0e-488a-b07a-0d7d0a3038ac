'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { AdherenceChart } from '@/components/charts/adherence-chart';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Users, 
  Activity, 
  Target,
  Calendar,
  Download,
  Share,
  AlertTriangle,
  CheckCircle,
  Clock,
  Pill,
  Heart,
  Brain,
  Sparkles,
  Zap,
  Star,
  Award,
  Building2,
  Stethoscope,
  DollarSign,
  Globe,
  Shield,
  Crown,
  Flame
} from 'lucide-react';
import { useState } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveC<PERSON>r, <PERSON>Chart, Bar, PieChart, Pie, Cell, AreaChart, Area, ComposedChart } from 'recharts';

// Mock platform-wide analytics data
const platformMetrics = [
  { month: 'Jan', users: 12450, hospitals: 45, doctors: 1250, patients: 8900, revenue: 2400000, adherence: 87 },
  { month: 'Feb', users: 13200, hospitals: 48, doctors: 1340, patients: 9650, revenue: 2650000, adherence: 89 },
  { month: 'Mar', users: 14100, hospitals: 52, doctors: 1420, patients: 10200, revenue: 2890000, adherence: 91 },
  { month: 'Apr', users: 15300, hospitals: 56, doctors: 1580, patients: 11100, revenue: 3150000, adherence: 88 },
  { month: 'May', users: 16800, hospitals: 61, doctors: 1720, patients: 12300, revenue: 3450000, adherence: 92 },
  { month: 'Jun', users: 18200, hospitals: 65, doctors: 1890, patients: 13500, revenue: 3780000, adherence: 94 },
];

const globalAdherence = [
  { region: 'North America', adherence: 92, patients: 45000, hospitals: 180 },
  { region: 'Europe', adherence: 89, patients: 38000, hospitals: 145 },
  { region: 'Asia Pacific', adherence: 87, patients: 52000, hospitals: 220 },
  { region: 'Latin America', adherence: 85, patients: 28000, hospitals: 95 },
  { region: 'Middle East', adherence: 88, patients: 15000, hospitals: 65 },
  { region: 'Africa', adherence: 83, patients: 12000, hospitals: 45 },
];

const topPerformingHospitals = [
  { name: 'Mayo Clinic', location: 'Rochester, MN', adherence: 96, patients: 2500, revenue: 450000 },
  { name: 'Johns Hopkins', location: 'Baltimore, MD', adherence: 95, patients: 2200, revenue: 420000 },
  { name: 'Cleveland Clinic', location: 'Cleveland, OH', adherence: 94, patients: 2100, revenue: 390000 },
  { name: 'Mass General', location: 'Boston, MA', adherence: 93, patients: 1950, revenue: 380000 },
  { name: 'Stanford Health', location: 'Stanford, CA', adherence: 92, patients: 1800, revenue: 360000 },
];

const medicationCategories = [
  { name: 'Cardiovascular', patients: 25000, adherence: 91, color: '#EF4444' },
  { name: 'Diabetes', patients: 22000, adherence: 94, color: '#3B82F6' },
  { name: 'Respiratory', patients: 18000, adherence: 88, color: '#10B981' },
  { name: 'Mental Health', patients: 15000, adherence: 85, color: '#8B5CF6' },
  { name: 'Oncology', patients: 12000, adherence: 89, color: '#F59E0B' },
  { name: 'Neurological', patients: 10000, adherence: 87, color: '#EC4899' },
];

const userGrowth = [
  { month: 'Jan', newUsers: 1200, totalUsers: 12450, churnRate: 2.1 },
  { month: 'Feb', newUsers: 1350, totalUsers: 13200, churnRate: 1.8 },
  { month: 'Mar', newUsers: 1450, totalUsers: 14100, churnRate: 1.9 },
  { month: 'Apr', newUsers: 1600, totalUsers: 15300, churnRate: 1.7 },
  { month: 'May', newUsers: 1750, totalUsers: 16800, churnRate: 1.6 },
  { month: 'Jun', newUsers: 1900, totalUsers: 18200, churnRate: 1.5 },
];

const systemHealth = [
  { metric: 'API Response Time', value: '145ms', status: 'excellent', trend: 'down' },
  { metric: 'Database Performance', value: '99.8%', status: 'excellent', trend: 'stable' },
  { metric: 'Server Uptime', value: '99.99%', status: 'excellent', trend: 'up' },
  { metric: 'Error Rate', value: '0.02%', status: 'excellent', trend: 'down' },
  { metric: 'User Satisfaction', value: '4.8/5', status: 'excellent', trend: 'up' },
  { metric: 'Support Response', value: '2.3h', status: 'good', trend: 'down' },
];

const revenueBreakdown = [
  { source: 'Enterprise Plans', amount: 2800000, percentage: 65, color: '#8B5CF6' },
  { source: 'Professional Plans', amount: 980000, percentage: 23, color: '#3B82F6' },
  { source: 'API Usage', amount: 340000, percentage: 8, color: '#10B981' },
  { source: 'Premium Features', amount: 180000, percentage: 4, color: '#F59E0B' },
];

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('6months');
  const [selectedMetric, setSelectedMetric] = useState('users');

  const getMetricColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-600" />;
      default: return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
    }
  };

  const currentMonth = platformMetrics[platformMetrics.length - 1];
  const previousMonth = platformMetrics[platformMetrics.length - 2];

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/admin' },
            { label: 'Analytics', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Platform Analytics</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Comprehensive insights across the entire MedCare ecosystem
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Share className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Share Report</span>
              <span className="sm:hidden">Share</span>
            </Button>
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Download className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Export Data</span>
              <span className="sm:hidden">Export</span>
            </Button>
          </div>
        </div>

        {/* Key Platform Metrics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{(currentMonth.users / 1000).toFixed(1)}K</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Users</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">
                    +{((currentMonth.users - previousMonth.users) / previousMonth.users * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <Target className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{currentMonth.adherence}%</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Global Adherence</p>
                <div className="flex items-center mt-1">
                  <Award className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">
                    +{currentMonth.adherence - previousMonth.adherence}% this month
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Building2 className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">{currentMonth.hospitals}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Partner Hospitals</p>
                <div className="flex items-center mt-1">
                  <Crown className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">
                    +{currentMonth.hospitals - previousMonth.hospitals} this month
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-orange-600">${(currentMonth.revenue / 1000000).toFixed(1)}M</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Monthly Revenue</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600 font-medium">
                    +{((currentMonth.revenue - previousMonth.revenue) / previousMonth.revenue * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Charts */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Platform Growth */}
            <Card className="modern-card">
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                      <TrendingUp className="h-5 w-5 text-white" />
                    </div>
                    <span>Platform Growth Metrics</span>
                  </CardTitle>
                  <Select value={timeRange} onValueChange={setTimeRange}>
                    <SelectTrigger className="w-32 bg-muted/30 border-border/50">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="3months">3 Months</SelectItem>
                      <SelectItem value="6months">6 Months</SelectItem>
                      <SelectItem value="1year">1 Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={platformMetrics}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                    <XAxis dataKey="month" stroke="hsl(var(--muted-foreground))" fontSize={12} />
                    <YAxis yAxisId="left" stroke="hsl(var(--muted-foreground))" fontSize={12} />
                    <YAxis yAxisId="right" orientation="right" stroke="hsl(var(--muted-foreground))" fontSize={12} />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: 'hsl(var(--card))',
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      }}
                    />
                    <Area yAxisId="left" type="monotone" dataKey="users" fill="#3B82F6" fillOpacity={0.3} stroke="#3B82F6" strokeWidth={2} />
                    <Bar yAxisId="right" dataKey="hospitals" fill="#8B5CF6" />
                    <Line yAxisId="left" type="monotone" dataKey="doctors" stroke="#10B981" strokeWidth={3} />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Global Adherence by Region */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Globe className="h-5 w-5 text-white" />
                  </div>
                  <span>Global Adherence Performance</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {globalAdherence.map((region, index) => (
                    <div key={index} className="modern-card p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                      <div className="flex justify-between items-center mb-3">
                        <div>
                          <h3 className="font-semibold text-blue-900 dark:text-blue-100">{region.region}</h3>
                          <p className="text-sm text-blue-700 dark:text-blue-300">
                            {region.patients.toLocaleString()} patients • {region.hospitals} hospitals
                          </p>
                        </div>
                        <Badge variant="outline" className="text-xs modern-badge bg-green-100 text-green-800 border-green-200">
                          {region.adherence}% adherence
                        </Badge>
                      </div>
                      <Progress value={region.adherence} className="h-3" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Performing Hospitals */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
                    <Award className="h-5 w-5 text-white" />
                  </div>
                  <span>Top Performing Hospitals</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {topPerformingHospitals.map((hospital, index) => (
                    <div key={index} className="modern-card p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                          {index + 1}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-purple-900 dark:text-purple-100">{hospital.name}</h3>
                          <p className="text-sm text-purple-700 dark:text-purple-300">{hospital.location}</p>
                        </div>
                        <Badge variant="outline" className="text-xs modern-badge bg-green-100 text-green-800 border-green-200">
                          {hospital.adherence}%
                        </Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-purple-700 dark:text-purple-300">Patients: </span>
                          <span className="font-medium text-purple-900 dark:text-purple-100">{hospital.patients.toLocaleString()}</span>
                        </div>
                        <div>
                          <span className="text-purple-700 dark:text-purple-300">Revenue: </span>
                          <span className="font-medium text-purple-900 dark:text-purple-100">${(hospital.revenue / 1000).toFixed(0)}K</span>
                        </div>
                        <div>
                          <span className="text-purple-700 dark:text-purple-300">Adherence: </span>
                          <span className="font-medium text-green-600">{hospital.adherence}%</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* System Health */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Shield className="h-4 w-4 text-white" />
                  </div>
                  <span>System Health</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {systemHealth.map((metric, index) => (
                  <div key={index} className="modern-card p-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-green-900 dark:text-green-100">{metric.metric}</span>
                      {getTrendIcon(metric.trend)}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`text-lg font-bold ${getMetricColor(metric.status)}`}>
                        {metric.value}
                      </span>
                      <Badge variant="outline" className={`text-xs modern-badge ${getMetricColor(metric.status)}`}>
                        {metric.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Medication Categories */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Pill className="h-4 w-4 text-white" />
                  </div>
                  <span>Medication Categories</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={medicationCategories}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="patients"
                    >
                      {medicationCategories.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                <div className="space-y-2 mt-4">
                  {medicationCategories.map((category, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: category.color }}
                        />
                        <span className="text-sm">{category.name}</span>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-medium">{(category.patients / 1000).toFixed(0)}K</span>
                        <span className="text-xs text-muted-foreground ml-1">({category.adherence}%)</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Revenue Breakdown */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg">
                    <DollarSign className="h-4 w-4 text-white" />
                  </div>
                  <span>Revenue Sources</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {revenueBreakdown.map((source, index) => (
                  <div key={index} className="modern-card p-3 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200 dark:border-orange-800">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-orange-900 dark:text-orange-100">{source.source}</span>
                      <Badge variant="outline" className="text-xs modern-badge">
                        {source.percentage}%
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-lg font-bold text-orange-600">
                        ${(source.amount / 1000000).toFixed(1)}M
                      </span>
                    </div>
                    <Progress value={source.percentage} className="h-2 mt-2" />
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Insights */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <Sparkles className="h-5 w-5 text-yellow-500" />
                  <span>AI Insights</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="modern-card p-3 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="font-semibold text-sm text-green-900 dark:text-green-100">Platform Growth</span>
                  </div>
                  <p className="text-xs text-green-700 dark:text-green-300">
                    User growth accelerated 23% this quarter, driven by hospital partnerships
                  </p>
                </div>
                
                <div className="modern-card p-3 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <Target className="h-4 w-4 text-blue-600" />
                    <span className="font-semibold text-sm text-blue-900 dark:text-blue-100">Adherence Leader</span>
                  </div>
                  <p className="text-xs text-blue-700 dark:text-blue-300">
                    North America leads global adherence at 92%, 5% above average
                  </p>
                </div>
                
                <div className="modern-card p-3 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <Brain className="h-4 w-4 text-purple-600" />
                    <span className="font-semibold text-sm text-purple-900 dark:text-purple-100">Optimization</span>
                  </div>
                  <p className="text-xs text-purple-700 dark:text-purple-300">
                    Diabetes medication category shows highest adherence potential
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}