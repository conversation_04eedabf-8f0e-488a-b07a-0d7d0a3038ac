'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Settings, 
  Server, 
  Database, 
  Shield, 
  Bell, 
  Mail,
  Globe,
  Key,
  Activity,
  Users,
  Building2,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Sparkles,
  Target,
  Award,
  Crown,
  Flame,
  Star,
  Heart,
  Brain,
  Lock,
  Unlock,
  Eye,
  EyeOff,
  Save,
  RefreshCw,
  Download,
  Upload,
  Trash2,
  Plus,
  Edit,
  MoreHorizontal
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { format } from 'date-fns';

// Mock system configuration data
const systemConfig = {
  general: {
    platformName: 'MedCare',
    platformDescription: 'AI-Powered Medicine Adherence Platform',
    supportEmail: '<EMAIL>',
    maintenanceMode: false,
    allowRegistrations: true,
    requireEmailVerification: true,
    defaultUserRole: 'patient',
    sessionTimeout: 24,
    maxLoginAttempts: 5
  },
  security: {
    twoFactorRequired: false,
    passwordMinLength: 8,
    passwordRequireSpecialChars: true,
    passwordRequireNumbers: true,
    passwordRequireUppercase: true,
    sessionSecure: true,
    apiRateLimit: 1000,
    encryptionEnabled: true,
    auditLogging: true,
    ipWhitelisting: false
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: true,
    webhookNotifications: false,
    notificationRetries: 3,
    emailProvider: 'sendgrid',
    smsProvider: 'twilio',
    defaultFromEmail: '<EMAIL>',
    adminAlerts: true
  },
  integrations: {
    stripeEnabled: true,
    twilioEnabled: true,
    sendgridEnabled: true,
    awsS3Enabled: true,
    googleAnalytics: true,
    intercomEnabled: false,
    slackWebhooks: true,
    apiDocumentation: true
  },
  performance: {
    cacheEnabled: true,
    cacheTTL: 3600,
    compressionEnabled: true,
    cdnEnabled: true,
    databasePoolSize: 20,
    maxRequestSize: 10,
    requestTimeout: 30,
    backgroundJobs: true
  }
};

const systemMetrics = [
  { name: 'Server Uptime', value: '99.99%', status: 'excellent', lastUpdate: '2 min ago' },
  { name: 'Database Performance', value: '99.8%', status: 'excellent', lastUpdate: '1 min ago' },
  { name: 'API Response Time', value: '145ms', status: 'good', lastUpdate: '30 sec ago' },
  { name: 'Error Rate', value: '0.02%', status: 'excellent', lastUpdate: '1 min ago' },
  { name: 'Memory Usage', value: '68%', status: 'good', lastUpdate: '30 sec ago' },
  { name: 'CPU Usage', value: '42%', status: 'excellent', lastUpdate: '30 sec ago' },
  { name: 'Disk Usage', value: '34%', status: 'excellent', lastUpdate: '5 min ago' },
  { name: 'Network I/O', value: '2.3 GB/s', status: 'good', lastUpdate: '1 min ago' }
];

const recentLogs = [
  {
    id: '1',
    timestamp: new Date('2024-01-20T10:30:00'),
    level: 'info',
    message: 'User authentication successful',
    source: 'auth-service',
    userId: 'user_123'
  },
  {
    id: '2',
    timestamp: new Date('2024-01-20T10:25:00'),
    level: 'warning',
    message: 'High API usage detected for hospital_456',
    source: 'api-gateway',
    userId: null
  },
  {
    id: '3',
    timestamp: new Date('2024-01-20T10:20:00'),
    level: 'error',
    message: 'Database connection timeout',
    source: 'database',
    userId: null
  },
  {
    id: '4',
    timestamp: new Date('2024-01-20T10:15:00'),
    level: 'info',
    message: 'Scheduled backup completed successfully',
    source: 'backup-service',
    userId: null
  },
  {
    id: '5',
    timestamp: new Date('2024-01-20T10:10:00'),
    level: 'info',
    message: 'New hospital registration: Metro Health',
    source: 'registration',
    userId: 'hospital_789'
  }
];

const systemAlerts = [
  {
    id: '1',
    title: 'High Memory Usage',
    message: 'Server memory usage has exceeded 80% for the past 15 minutes',
    severity: 'warning',
    timestamp: new Date('2024-01-20T10:30:00'),
    resolved: false
  },
  {
    id: '2',
    title: 'API Rate Limit Approaching',
    message: 'Monthly API calls are at 85% of the limit',
    severity: 'info',
    timestamp: new Date('2024-01-20T09:45:00'),
    resolved: false
  },
  {
    id: '3',
    title: 'Security Update Available',
    message: 'Critical security patch available for deployment',
    severity: 'high',
    timestamp: new Date('2024-01-20T08:30:00'),
    resolved: false
  }
];

export default function SystemPage() {
  const [activeTab, setActiveTab] = useState('general');
  const [config, setConfig] = useState(systemConfig);
  const [showApiKey, setShowApiKey] = useState(false);
  const [isMaintenanceDialogOpen, setIsMaintenanceDialogOpen] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600 bg-green-100 border-green-200';
      case 'good': return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'warning': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'critical': return 'text-red-600 bg-red-100 border-red-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'text-red-600 bg-red-100 border-red-200';
      case 'warning': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'info': return 'text-blue-600 bg-blue-100 border-blue-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      case 'warning': return 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100';
      case 'info': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const handleConfigChange = (section: string, key: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [key]: value
      }
    }));
  };

  const handleSaveConfig = () => {
    // Handle saving configuration
    console.log('Saving configuration:', config);
  };

  const tabs = [
    { id: 'general', label: 'General', icon: Settings },
    { id: 'security', label: 'Security', icon: Shield },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'integrations', label: 'Integrations', icon: Globe },
    { id: 'performance', label: 'Performance', icon: Zap }
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/admin' },
            { label: 'System', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Settings className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">System Settings</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Configure and monitor platform settings and performance
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Dialog open={isMaintenanceDialogOpen} onOpenChange={setIsMaintenanceDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Maintenance Mode</span>
                  <span className="sm:hidden">Maintenance</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="modern-card max-w-md">
                <DialogHeader>
                  <DialogTitle className="flex items-center space-x-2">
                    <div className="p-2 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-lg">
                      <AlertTriangle className="h-5 w-5 text-white" />
                    </div>
                    <span>Enable Maintenance Mode</span>
                  </DialogTitle>
                  <DialogDescription>
                    This will make the platform inaccessible to all users except administrators
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800">
                    <p className="text-sm font-medium">
                      Warning: Enabling maintenance mode will:
                    </p>
                    <ul className="text-sm mt-2 space-y-1 list-disc pl-5">
                      <li>Log out all non-admin users</li>
                      <li>Disable all API access</li>
                      <li>Show a maintenance page to all visitors</li>
                      <li>Pause all scheduled tasks</li>
                    </ul>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="maintenanceMessage">Maintenance Message</Label>
                    <Textarea
                      id="maintenanceMessage"
                      placeholder="We're currently performing scheduled maintenance. Please check back soon."
                      className="bg-muted/30 border-border/50 min-h-[80px]"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="maintenanceDuration">Estimated Duration</Label>
                    <Select defaultValue="1">
                      <SelectTrigger className="bg-muted/30 border-border/50">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0.5">30 minutes</SelectItem>
                        <SelectItem value="1">1 hour</SelectItem>
                        <SelectItem value="2">2 hours</SelectItem>
                        <SelectItem value="4">4 hours</SelectItem>
                        <SelectItem value="8">8 hours</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex justify-end space-x-3 pt-4">
                    <Button variant="outline" onClick={() => setIsMaintenanceDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button variant="destructive">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      Enable Maintenance Mode
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            <Button className="btn-primary w-full sm:w-auto" onClick={handleSaveConfig}>
              <Save className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Save Changes</span>
              <span className="sm:hidden">Save</span>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Configuration Tabs */}
            <Card className="modern-card">
              <CardHeader className="pb-0">
                <div className="flex overflow-x-auto pb-2 scrollbar-hide">
                  {tabs.map((tab) => (
                    <Button
                      key={tab.id}
                      variant={activeTab === tab.id ? 'default' : 'ghost'}
                      className={`mr-2 ${activeTab === tab.id ? 'bg-primary text-primary-foreground' : ''}`}
                      onClick={() => setActiveTab(tab.id)}
                    >
                      <tab.icon className="h-4 w-4 mr-2" />
                      {tab.label}
                    </Button>
                  ))}
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                {activeTab === 'general' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="platformName">Platform Name</Label>
                        <Input
                          id="platformName"
                          value={config.general.platformName}
                          onChange={(e) => handleConfigChange('general', 'platformName', e.target.value)}
                          className="bg-muted/30 border-border/50"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="supportEmail">Support Email</Label>
                        <Input
                          id="supportEmail"
                          value={config.general.supportEmail}
                          onChange={(e) => handleConfigChange('general', 'supportEmail', e.target.value)}
                          className="bg-muted/30 border-border/50"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="platformDescription">Platform Description</Label>
                      <Textarea
                        id="platformDescription"
                        value={config.general.platformDescription}
                        onChange={(e) => handleConfigChange('general', 'platformDescription', e.target.value)}
                        className="bg-muted/30 border-border/50 min-h-[80px]"
                      />
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="allowRegistrations">Allow Registrations</Label>
                          <p className="text-xs text-muted-foreground">Enable or disable new user registrations</p>
                        </div>
                        <Switch
                          id="allowRegistrations"
                          checked={config.general.allowRegistrations}
                          onCheckedChange={(checked) => handleConfigChange('general', 'allowRegistrations', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="requireEmailVerification">Require Email Verification</Label>
                          <p className="text-xs text-muted-foreground">Require users to verify their email address</p>
                        </div>
                        <Switch
                          id="requireEmailVerification"
                          checked={config.general.requireEmailVerification}
                          onCheckedChange={(checked) => handleConfigChange('general', 'requireEmailVerification', checked)}
                        />
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="defaultUserRole">Default User Role</Label>
                        <Select 
                          value={config.general.defaultUserRole}
                          onValueChange={(value) => handleConfigChange('general', 'defaultUserRole', value)}
                        >
                          <SelectTrigger className="bg-muted/30 border-border/50">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="patient">Patient</SelectItem>
                            <SelectItem value="doctor">Doctor</SelectItem>
                            <SelectItem value="hospital">Hospital</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="sessionTimeout">Session Timeout (hours)</Label>
                        <Input
                          id="sessionTimeout"
                          type="number"
                          value={config.general.sessionTimeout}
                          onChange={(e) => handleConfigChange('general', 'sessionTimeout', parseInt(e.target.value))}
                          className="bg-muted/30 border-border/50"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'security' && (
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="twoFactorRequired">Require Two-Factor Authentication</Label>
                          <p className="text-xs text-muted-foreground">Enforce 2FA for all users</p>
                        </div>
                        <Switch
                          id="twoFactorRequired"
                          checked={config.security.twoFactorRequired}
                          onCheckedChange={(checked) => handleConfigChange('security', 'twoFactorRequired', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="auditLogging">Audit Logging</Label>
                          <p className="text-xs text-muted-foreground">Log all user actions for security auditing</p>
                        </div>
                        <Switch
                          id="auditLogging"
                          checked={config.security.auditLogging}
                          onCheckedChange={(checked) => handleConfigChange('security', 'auditLogging', checked)}
                        />
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Password Requirements</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="passwordMinLength">Minimum Length</Label>
                          <Input
                            id="passwordMinLength"
                            type="number"
                            value={config.security.passwordMinLength}
                            onChange={(e) => handleConfigChange('security', 'passwordMinLength', parseInt(e.target.value))}
                            className="bg-muted/30 border-border/50"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
                          <Input
                            id="maxLoginAttempts"
                            type="number"
                            value={config.general.maxLoginAttempts}
                            onChange={(e) => handleConfigChange('general', 'maxLoginAttempts', parseInt(e.target.value))}
                            className="bg-muted/30 border-border/50"
                          />
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="passwordRequireSpecialChars"
                            checked={config.security.passwordRequireSpecialChars}
                            onCheckedChange={(checked) => handleConfigChange('security', 'passwordRequireSpecialChars', checked)}
                          />
                          <Label htmlFor="passwordRequireSpecialChars">Special Characters</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="passwordRequireNumbers"
                            checked={config.security.passwordRequireNumbers}
                            onCheckedChange={(checked) => handleConfigChange('security', 'passwordRequireNumbers', checked)}
                          />
                          <Label htmlFor="passwordRequireNumbers">Numbers</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="passwordRequireUppercase"
                            checked={config.security.passwordRequireUppercase}
                            onCheckedChange={(checked) => handleConfigChange('security', 'passwordRequireUppercase', checked)}
                          />
                          <Label htmlFor="passwordRequireUppercase">Uppercase</Label>
                        </div>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">API Security</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="apiRateLimit">API Rate Limit (requests/hour)</Label>
                          <Input
                            id="apiRateLimit"
                            type="number"
                            value={config.security.apiRateLimit}
                            onChange={(e) => handleConfigChange('security', 'apiRateLimit', parseInt(e.target.value))}
                            className="bg-muted/30 border-border/50"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="apiKey">API Key</Label>
                          <div className="relative">
                            <Input
                              id="apiKey"
                              type={showApiKey ? 'text' : 'password'}
                              value="*************************************************"
                              readOnly
                              className="bg-muted/30 border-border/50 pr-10"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                              onClick={() => setShowApiKey(!showApiKey)}
                            >
                              {showApiKey ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'notifications' && (
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="emailNotifications">Email Notifications</Label>
                          <p className="text-xs text-muted-foreground">Send notifications via email</p>
                        </div>
                        <Switch
                          id="emailNotifications"
                          checked={config.notifications.emailNotifications}
                          onCheckedChange={(checked) => handleConfigChange('notifications', 'emailNotifications', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="smsNotifications">SMS Notifications</Label>
                          <p className="text-xs text-muted-foreground">Send notifications via text message</p>
                        </div>
                        <Switch
                          id="smsNotifications"
                          checked={config.notifications.smsNotifications}
                          onCheckedChange={(checked) => handleConfigChange('notifications', 'smsNotifications', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="pushNotifications">Push Notifications</Label>
                          <p className="text-xs text-muted-foreground">Send notifications to mobile devices</p>
                        </div>
                        <Switch
                          id="pushNotifications"
                          checked={config.notifications.pushNotifications}
                          onCheckedChange={(checked) => handleConfigChange('notifications', 'pushNotifications', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="adminAlerts">Admin Alerts</Label>
                          <p className="text-xs text-muted-foreground">Send critical system alerts to administrators</p>
                        </div>
                        <Switch
                          id="adminAlerts"
                          checked={config.notifications.adminAlerts}
                          onCheckedChange={(checked) => handleConfigChange('notifications', 'adminAlerts', checked)}
                        />
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="emailProvider">Email Provider</Label>
                        <Select 
                          value={config.notifications.emailProvider}
                          onValueChange={(value) => handleConfigChange('notifications', 'emailProvider', value)}
                        >
                          <SelectTrigger className="bg-muted/30 border-border/50">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="sendgrid">SendGrid</SelectItem>
                            <SelectItem value="mailchimp">Mailchimp</SelectItem>
                            <SelectItem value="ses">AWS SES</SelectItem>
                            <SelectItem value="smtp">Custom SMTP</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="smsProvider">SMS Provider</Label>
                        <Select 
                          value={config.notifications.smsProvider}
                          onValueChange={(value) => handleConfigChange('notifications', 'smsProvider', value)}
                        >
                          <SelectTrigger className="bg-muted/30 border-border/50">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="twilio">Twilio</SelectItem>
                            <SelectItem value="nexmo">Nexmo</SelectItem>
                            <SelectItem value="sns">AWS SNS</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="defaultFromEmail">Default From Email</Label>
                      <Input
                        id="defaultFromEmail"
                        value={config.notifications.defaultFromEmail}
                        onChange={(e) => handleConfigChange('notifications', 'defaultFromEmail', e.target.value)}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="notificationRetries">Notification Retries</Label>
                      <Input
                        id="notificationRetries"
                        type="number"
                        value={config.notifications.notificationRetries}
                        onChange={(e) => handleConfigChange('notifications', 'notificationRetries', parseInt(e.target.value))}
                        className="bg-muted/30 border-border/50"
                      />
                      <p className="text-xs text-muted-foreground">Number of retry attempts for failed notifications</p>
                    </div>
                  </div>
                )}

                {activeTab === 'integrations' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                              <svg className="h-5 w-5 text-blue-600 dark:text-blue-300" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13.5 4.06c0-1.336-1.616-2.005-2.56-1.06l-4.5 4.5H4.508c-1.141 0-2.318.664-2.66 1.905A9.76 9.76 0 001.5 12c0 .898.121 1.768.35 2.595.341 1.24 1.518 1.905 2.659 1.905h1.93l4.5 4.5c.945.945 2.561.276 2.561-1.06V4.06zM18.584 5.106a.75.75 0 011.06 0c3.808 3.807 3.808 9.98 0 13.788a.75.75 0 11-1.06-1.06 8.25 8.25 0 000-11.668.75.75 0 010-1.06z" fill="currentColor" />
                                <path d="M15.932 7.757a.75.75 0 011.061 0 6 6 0 010 8.486.75.75 0 01-1.06-1.061 4.5 4.5 0 000-6.364.75.75 0 010-1.06z" fill="currentColor" />
                              </svg>
                            </div>
                            <h3 className="font-semibold text-blue-900 dark:text-blue-100">Stripe</h3>
                          </div>
                          <Switch
                            checked={config.integrations.stripeEnabled}
                            onCheckedChange={(checked) => handleConfigChange('integrations', 'stripeEnabled', checked)}
                          />
                        </div>
                        <p className="text-xs text-blue-700 dark:text-blue-300 mb-2">Payment processing integration</p>
                        <div className="flex justify-end">
                          <Button variant="outline" size="sm" className="text-xs">Configure</Button>
                        </div>
                      </div>
                      
                      <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                              <svg className="h-5 w-5 text-blue-600 dark:text-blue-300" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10.5 1.5H8.25A2.25 2.25 0 006 3.75v16.5a2.25 2.25 0 002.25 2.25h7.5A2.25 2.25 0 0018 20.25V3.75a2.25 2.25 0 00-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                              </svg>
                            </div>
                            <h3 className="font-semibold text-blue-900 dark:text-blue-100">Twilio</h3>
                          </div>
                          <Switch
                            checked={config.integrations.twilioEnabled}
                            onCheckedChange={(checked) => handleConfigChange('integrations', 'twilioEnabled', checked)}
                          />
                        </div>
                        <p className="text-xs text-blue-700 dark:text-blue-300 mb-2">SMS and voice call integration</p>
                        <div className="flex justify-end">
                          <Button variant="outline" size="sm" className="text-xs">Configure</Button>
                        </div>
                      </div>
                      
                      <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                              <Mail className="h-5 w-5 text-blue-600 dark:text-blue-300" />
                            </div>
                            <h3 className="font-semibold text-blue-900 dark:text-blue-100">SendGrid</h3>
                          </div>
                          <Switch
                            checked={config.integrations.sendgridEnabled}
                            onCheckedChange={(checked) => handleConfigChange('integrations', 'sendgridEnabled', checked)}
                          />
                        </div>
                        <p className="text-xs text-blue-700 dark:text-blue-300 mb-2">Email delivery service</p>
                        <div className="flex justify-end">
                          <Button variant="outline" size="sm" className="text-xs">Configure</Button>
                        </div>
                      </div>
                      
                      <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                              <Database className="h-5 w-5 text-blue-600 dark:text-blue-300" />
                            </div>
                            <h3 className="font-semibold text-blue-900 dark:text-blue-100">AWS S3</h3>
                          </div>
                          <Switch
                            checked={config.integrations.awsS3Enabled}
                            onCheckedChange={(checked) => handleConfigChange('integrations', 'awsS3Enabled', checked)}
                          />
                        </div>
                        <p className="text-xs text-blue-700 dark:text-blue-300 mb-2">File storage integration</p>
                        <div className="flex justify-end">
                          <Button variant="outline" size="sm" className="text-xs">Configure</Button>
                        </div>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="googleAnalytics">Google Analytics</Label>
                          <p className="text-xs text-muted-foreground">Track user behavior and platform usage</p>
                        </div>
                        <Switch
                          id="googleAnalytics"
                          checked={config.integrations.googleAnalytics}
                          onCheckedChange={(checked) => handleConfigChange('integrations', 'googleAnalytics', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="slackWebhooks">Slack Webhooks</Label>
                          <p className="text-xs text-muted-foreground">Send alerts and notifications to Slack</p>
                        </div>
                        <Switch
                          id="slackWebhooks"
                          checked={config.integrations.slackWebhooks}
                          onCheckedChange={(checked) => handleConfigChange('integrations', 'slackWebhooks', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="intercomEnabled">Intercom</Label>
                          <p className="text-xs text-muted-foreground">Customer support and engagement platform</p>
                        </div>
                        <Switch
                          id="intercomEnabled"
                          checked={config.integrations.intercomEnabled}
                          onCheckedChange={(checked) => handleConfigChange('integrations', 'intercomEnabled', checked)}
                        />
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'performance' && (
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="cacheEnabled">Enable Caching</Label>
                          <p className="text-xs text-muted-foreground">Cache responses to improve performance</p>
                        </div>
                        <Switch
                          id="cacheEnabled"
                          checked={config.performance.cacheEnabled}
                          onCheckedChange={(checked) => handleConfigChange('performance', 'cacheEnabled', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="compressionEnabled">Enable Compression</Label>
                          <p className="text-xs text-muted-foreground">Compress responses to reduce bandwidth</p>
                        </div>
                        <Switch
                          id="compressionEnabled"
                          checked={config.performance.compressionEnabled}
                          onCheckedChange={(checked) => handleConfigChange('performance', 'compressionEnabled', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="cdnEnabled">Enable CDN</Label>
                          <p className="text-xs text-muted-foreground">Use content delivery network for static assets</p>
                        </div>
                        <Switch
                          id="cdnEnabled"
                          checked={config.performance.cdnEnabled}
                          onCheckedChange={(checked) => handleConfigChange('performance', 'cdnEnabled', checked)}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label htmlFor="backgroundJobs">Background Jobs</Label>
                          <p className="text-xs text-muted-foreground">Process long-running tasks in the background</p>
                        </div>
                        <Switch
                          id="backgroundJobs"
                          checked={config.performance.backgroundJobs}
                          onCheckedChange={(checked) => handleConfigChange('performance', 'backgroundJobs', checked)}
                        />
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="cacheTTL">Cache TTL (seconds)</Label>
                        <Input
                          id="cacheTTL"
                          type="number"
                          value={config.performance.cacheTTL}
                          onChange={(e) => handleConfigChange('performance', 'cacheTTL', parseInt(e.target.value))}
                          className="bg-muted/30 border-border/50"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="databasePoolSize">Database Pool Size</Label>
                        <Input
                          id="databasePoolSize"
                          type="number"
                          value={config.performance.databasePoolSize}
                          onChange={(e) => handleConfigChange('performance', 'databasePoolSize', parseInt(e.target.value))}
                          className="bg-muted/30 border-border/50"
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="maxRequestSize">Max Request Size (MB)</Label>
                        <Input
                          id="maxRequestSize"
                          type="number"
                          value={config.performance.maxRequestSize}
                          onChange={(e) => handleConfigChange('performance', 'maxRequestSize', parseInt(e.target.value))}
                          className="bg-muted/30 border-border/50"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="requestTimeout">Request Timeout (seconds)</Label>
                        <Input
                          id="requestTimeout"
                          type="number"
                          value={config.performance.requestTimeout}
                          onChange={(e) => handleConfigChange('performance', 'requestTimeout', parseInt(e.target.value))}
                          className="bg-muted/30 border-border/50"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* System Logs */}
            <Card className="modern-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                      <Activity className="h-5 w-5 text-white" />
                    </div>
                    <span>System Logs</span>
                  </CardTitle>
                  <Button variant="outline" size="sm" className="hover:bg-accent/50 interactive-element">
                    <Download className="h-4 w-4 mr-2" />
                    Export Logs
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentLogs.map((log) => (
                    <div key={log.id} className="modern-card p-3 bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20 border border-gray-200 dark:border-gray-800">
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className={`text-xs modern-badge ${getLogLevelColor(log.level)}`}>
                            {log.level}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {format(log.timestamp, 'HH:mm:ss')}
                          </span>
                        </div>
                        <span className="text-xs font-medium">{log.source}</span>
                      </div>
                      <p className="text-sm">{log.message}</p>
                      {log.userId && (
                        <p className="text-xs text-muted-foreground mt-1">User ID: {log.userId}</p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* System Health */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Server className="h-4 w-4 text-white" />
                  </div>
                  <span>System Health</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {systemMetrics.slice(0, 5).map((metric, index) => (
                  <div key={index} className="modern-card p-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-green-900 dark:text-green-100">{metric.name}</span>
                      <Badge variant="outline" className={`text-xs modern-badge ${getStatusColor(metric.status)}`}>
                        {metric.status}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-bold text-green-600">{metric.value}</span>
                      <span className="text-xs text-green-700 dark:text-green-300">{metric.lastUpdate}</span>
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                  <Activity className="h-4 w-4 mr-2" />
                  View All Metrics
                </Button>
              </CardContent>
            </Card>

            {/* System Alerts */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-white" />
                  </div>
                  <span>System Alerts</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {systemAlerts.map((alert) => (
                  <div key={alert.id} className={`modern-card p-3 bg-gradient-to-r ${getSeverityColor(alert.severity)} border`}>
                    <div className="flex items-start justify-between">
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm">{alert.title}</p>
                        <p className="text-xs mt-1">{alert.message}</p>
                        <p className="text-xs opacity-70 mt-1">{format(alert.timestamp, 'MMM d, HH:mm')}</p>
                      </div>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-2 hover:bg-white/20">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  View All Alerts
                </Button>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Restart Services
                </Button>
                <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                  <Database className="h-4 w-4 mr-2" />
                  Database Backup
                </Button>
                <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                  <Key className="h-4 w-4 mr-2" />
                  Rotate API Keys
                </Button>
                <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                  <Lock className="h-4 w-4 mr-2" />
                  Security Audit
                </Button>
                <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear Cache
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}