'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { StatsCard } from '@/components/ui/stats-card';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  Users, 
  Building2, 
  Activity, 
  TrendingUp,
  AlertTriangle,
  DollarSign,
  Globe,
  Server,
  Database,
  Settings,
  Plus,
  Eye,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Clock,
  Sparkles,
  Target,
  Zap,
  Crown
} from 'lucide-react';
import Link from 'next/link';

// Mock data for admin dashboard
const mockHospitals = [
  {
    id: '1',
    name: 'City General Hospital',
    location: 'New York, NY',
    doctors: 45,
    patients: 1250,
    adherenceRate: 89,
    status: 'active',
    plan: 'Enterprise',
    revenue: 125000
  },
  {
    id: '2',
    name: 'St. Mary Medical Center',
    location: 'Los Angeles, CA',
    doctors: 38,
    patients: 980,
    adherenceRate: 92,
    status: 'active',
    plan: 'Professional',
    revenue: 98000
  },
  {
    id: '3',
    name: 'Regional Health System',
    location: 'Chicago, IL',
    doctors: 52,
    patients: 1450,
    adherenceRate: 87,
    status: 'active',
    plan: 'Enterprise',
    revenue: 145000
  },
  {
    id: '4',
    name: 'Community Care Clinic',
    location: 'Houston, TX',
    doctors: 22,
    patients: 650,
    adherenceRate: 85,
    status: 'trial',
    plan: 'Basic',
    revenue: 45000
  }
];

const systemMetrics = [
  {
    name: 'API Response Time',
    value: '145ms',
    status: 'good',
    trend: 'down'
  },
  {
    name: 'Database Performance',
    value: '99.8%',
    status: 'excellent',
    trend: 'stable'
  },
  {
    name: 'Server Uptime',
    value: '99.99%',
    status: 'excellent',
    trend: 'up'
  },
  {
    name: 'Error Rate',
    value: '0.02%',
    status: 'good',
    trend: 'down'
  }
];

const recentActivities = [
  {
    id: '1',
    type: 'hospital',
    action: 'New hospital registered',
    details: 'Metro Health Center joined the platform',
    time: '2 hours ago',
    status: 'success'
  },
  {
    id: '2',
    type: 'system',
    action: 'System maintenance completed',
    details: 'Database optimization and security updates',
    time: '6 hours ago',
    status: 'info'
  },
  {
    id: '3',
    type: 'alert',
    action: 'High usage alert',
    details: 'API calls exceeded 80% of monthly limit',
    time: '8 hours ago',
    status: 'warning'
  },
  {
    id: '4',
    type: 'user',
    action: 'Bulk user import',
    details: '500 new patients added to City General',
    time: '12 hours ago',
    status: 'success'
  }
];

const systemAlerts = [
  {
    id: '1',
    title: 'High API Usage',
    message: 'Monthly API limit at 85% for Enterprise customers',
    severity: 'medium',
    time: '1 hour ago'
  },
  {
    id: '2',
    title: 'Security Update Available',
    message: 'Critical security patch ready for deployment',
    severity: 'high',
    time: '3 hours ago'
  },
  {
    id: '3',
    title: 'Storage Optimization',
    message: 'Database cleanup completed, 15GB freed',
    severity: 'low',
    time: '5 hours ago'
  }
];

export default function AdminDashboard() {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'trial': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      case 'suspended': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'Enterprise': return 'from-purple-50 to-pink-50 border-purple-200 text-purple-900 dark:from-purple-900/20 dark:to-pink-900/20 dark:border-purple-800 dark:text-purple-100';
      case 'Professional': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      case 'Basic': return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getMetricStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      case 'medium': return 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100';
      case 'low': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getActivityStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-blue-600" />;
    }
  };

  const totalHospitals = mockHospitals.length;
  const totalDoctors = mockHospitals.reduce((sum, hospital) => sum + hospital.doctors, 0);
  const totalPatients = mockHospitals.reduce((sum, hospital) => sum + hospital.patients, 0);
  const totalRevenue = mockHospitals.reduce((sum, hospital) => sum + hospital.revenue, 0);
  const avgAdherence = Math.round(mockHospitals.reduce((sum, hospital) => sum + hospital.adherenceRate, 0) / mockHospitals.length);

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/admin', current: true },
          ]}
        />

        {/* Welcome Section */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Shield className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">System Administration 🛡️</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Managing {totalHospitals} hospitals, {totalDoctors} doctors, and {totalPatients.toLocaleString()} patients
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Link href="/dashboard/admin/system">
              <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
                <Settings className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">System Settings</span>
                <span className="sm:hidden">Settings</span>
              </Button>
            </Link>
            <Link href="/dashboard/admin/hospitals">
              <Button className="btn-primary w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Add Hospital</span>
                <span className="sm:hidden">Add</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Building2 className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{totalHospitals}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Hospitals</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">+3 this month</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{totalDoctors}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active Doctors</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">+15 this week</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Activity className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">{(totalPatients / 1000).toFixed(1)}K</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Patients</p>
                <div className="flex items-center mt-1">
                  <Zap className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">+8.5% this month</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-orange-600">${(totalRevenue / 1000).toFixed(0)}K</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Platform Revenue</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600 font-medium">+22% vs last month</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Hospital Overview */}
            <Card className="modern-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                      <Building2 className="h-5 w-5 text-white" />
                    </div>
                    <span>Hospital Network</span>
                  </CardTitle>
                  <Link href="/dashboard/admin/hospitals">
                    <Button variant="outline" size="sm" className="hover:bg-accent/50 interactive-element">
                      Manage All
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockHospitals.map((hospital) => (
                    <div key={hospital.id} className="modern-card p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 hover:scale-[1.01] transition-all duration-300">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3 min-w-0 flex-1">
                          <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                            <Building2 className="h-5 w-5 text-blue-600 dark:text-blue-300" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <h3 className="font-semibold text-blue-900 dark:text-blue-100 truncate text-sm sm:text-base">
                              {hospital.name}
                            </h3>
                            <p className="text-xs sm:text-sm text-blue-700 dark:text-blue-300 truncate">{hospital.location}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          <Badge variant="outline" className={`text-xs modern-badge ${getStatusColor(hospital.status)}`}>
                            {hospital.status}
                          </Badge>
                          <Badge variant="outline" className={`text-xs modern-badge ${getPlanColor(hospital.plan)}`}>
                            {hospital.plan}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                        <div>
                          <p className="text-xs text-blue-600 dark:text-blue-400">Doctors</p>
                          <p className="text-sm font-semibold text-blue-900 dark:text-blue-100">{hospital.doctors}</p>
                        </div>
                        <div>
                          <p className="text-xs text-blue-600 dark:text-blue-400">Patients</p>
                          <p className="text-sm font-semibold text-blue-900 dark:text-blue-100">{hospital.patients.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-xs text-blue-600 dark:text-blue-400">Adherence</p>
                          <p className="text-sm font-semibold text-green-600">{hospital.adherenceRate}%</p>
                        </div>
                        <div>
                          <p className="text-xs text-blue-600 dark:text-blue-400">Revenue</p>
                          <p className="text-sm font-semibold text-blue-900 dark:text-blue-100">
                            ${(hospital.revenue / 1000).toFixed(0)}K
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* System Metrics */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Server className="h-5 w-5 text-white" />
                  </div>
                  <span>System Performance</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  {systemMetrics.map((metric, index) => (
                    <div key={index} className="modern-card p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-green-900 dark:text-green-100 text-sm">{metric.name}</h4>
                        <div className="flex items-center space-x-1">
                          {metric.trend === 'up' && <TrendingUp className="h-3 w-3 text-green-600" />}
                          {metric.trend === 'down' && <TrendingUp className="h-3 w-3 text-red-600 rotate-180" />}
                          {metric.trend === 'stable' && <div className="h-3 w-3 bg-gray-400 rounded-full" />}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`text-2xl font-bold ${getMetricStatusColor(metric.status)}`}>
                          {metric.value}
                        </span>
                        <Badge variant="outline" className={`text-xs modern-badge ${getMetricStatusColor(metric.status)}`}>
                          {metric.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* System Alerts */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-white" />
                  </div>
                  <span>System Alerts</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {systemAlerts.map((alert) => (
                  <div key={alert.id} className={`modern-card p-3 bg-gradient-to-r ${getSeverityColor(alert.severity)} border`}>
                    <div className="flex items-start justify-between">
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm">{alert.title}</p>
                        <p className="text-xs mt-1">{alert.message}</p>
                        <p className="text-xs opacity-70 mt-1">{alert.time}</p>
                      </div>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-2 hover:bg-white/20">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  View All Alerts
                </Button>
              </CardContent>
            </Card>

            {/* Recent Activities */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Activity className="h-4 w-4 text-white" />
                  </div>
                  <span>Recent Activities</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="modern-card p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                    <div className="flex items-start space-x-3">
                      <div className="mt-0.5">
                        {getActivityStatusIcon(activity.status)}
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm text-blue-900 dark:text-blue-100">{activity.action}</p>
                        <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">{activity.details}</p>
                        <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">{activity.time}</p>
                      </div>
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                  <Activity className="h-4 w-4 mr-2" />
                  View All Activities
                </Button>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Link href="/dashboard/admin/users">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <Users className="h-4 w-4 mr-2" />
                    Manage Users
                  </Button>
                </Link>
                <Link href="/dashboard/admin/hospitals">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <Building2 className="h-4 w-4 mr-2" />
                    Hospital Network
                  </Button>
                </Link>
                <Link href="/dashboard/admin/analytics">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Platform Analytics
                  </Button>
                </Link>
                <Link href="/dashboard/admin/system">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <Settings className="h-4 w-4 mr-2" />
                    System Settings
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}