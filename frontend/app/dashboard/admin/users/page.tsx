'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Users, 
  Search, 
  Filter, 
  Plus, 
  Eye, 
  MessageSquare,
  Phone,
  Calendar,
  Activity,
  Shield,
  Building2,
  Stethoscope,
  TrendingUp,
  MoreHorizontal,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  Target,
  Sparkles,
  Zap,
  Star,
  Award,
  Crown,
  UserCheck,
  UserX,
  Ban
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { format, differenceInDays } from 'date-fns';
import { useUsersData } from '@/hooks/use-users-data';
import type { UserDetails } from '@/lib/api/users';

// Users page with real API integration


export default function UsersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [selectedUser, setSelectedUser] = useState<UserDetails | null>(null);

  // Use users data hook
  const {
    users,
    stats,
    isLoading,
    error,
    hasError,
    fetchUsers,
    refreshAll,
    updateUser,
    deleteUser,
    suspendUser,
    activateUser,
  } = useUsersData({
    autoFetch: true,
    query: { limit: 100 },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'suspended': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      case 'pending': return 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'doctor': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'patient': return 'bg-green-100 text-green-800 border-green-200';
      case 'hospital': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'admin': return 'bg-orange-100 text-orange-800 border-orange-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'doctor': return <Stethoscope className="h-4 w-4" />;
      case 'patient': return <Users className="h-4 w-4" />;
      case 'hospital': return <Building2 className="h-4 w-4" />;
      case 'admin': return <Shield className="h-4 w-4" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  const getSubscriptionColor = (subscription: string) => {
    switch (subscription) {
      case 'Enterprise': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Professional': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Basic': return 'bg-green-100 text-green-800 border-green-200';
      case 'Free': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Filter and sort users
  const filteredUsers = users
    .filter(user => {
      const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          (user.organization && user.organization.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesRole = filterRole === 'all' || user.role === filterRole;
      const matchesStatus = filterStatus === 'all' || user.status === filterStatus;

      return matchesSearch && matchesRole && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'created':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'last-login':
          const aLastLogin = a.lastLogin ? new Date(a.lastLogin).getTime() : 0;
          const bLastLogin = b.lastLogin ? new Date(b.lastLogin).getTime() : 0;
          return bLastLogin - aLastLogin;
        case 'role':
          return a.role.localeCompare(b.role);
        default:
          return 0;
      }
    });

  const getStatusCounts = () => {
    // Use stats from API if available, otherwise calculate from users
    if (stats) {
      return stats;
    }

    return {
      total: users.length,
      active: users.filter(u => u.status === 'active').length,
      suspended: users.filter(u => u.status === 'suspended').length,
      pending: users.filter(u => u.status === 'pending').length,
      doctors: users.filter(u => u.role === 'doctor').length,
      patients: users.filter(u => u.role === 'patient').length,
      hospitals: users.filter(u => u.role === 'hospital').length,
      insurance: users.filter(u => u.role === 'insurance').length,
      admin: users.filter(u => u.role === 'admin').length,
    };
  };

  const statusCounts = getStatusCounts();

  // Show loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-6 sm:space-y-8">
          <Breadcrumb
            items={[
              { label: 'Dashboard', href: '/dashboard/admin' },
              { label: 'Users', current: true },
            ]}
          />
          <div className="flex items-center justify-center h-64">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="text-muted-foreground">Loading users...</span>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Show error state
  if (hasError) {
    return (
      <DashboardLayout>
        <div className="space-y-6 sm:space-y-8">
          <Breadcrumb
            items={[
              { label: 'Dashboard', href: '/dashboard/admin' },
              { label: 'Users', current: true },
            ]}
          />
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-destructive mb-2">⚠️ Error loading users</div>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={refreshAll} variant="outline">
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/admin' },
            { label: 'Users', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Users className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">User Management</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Manage all users across the platform
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Activity className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">User Analytics</span>
              <span className="sm:hidden">Analytics</span>
            </Button>
            <Button className="btn-primary w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Add User</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{statusCounts.total}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Users</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">All platforms</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{statusCounts.active}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active Users</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">Currently online</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Stethoscope className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">{statusCounts.doctors}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Doctors</p>
                <div className="flex items-center mt-1">
                  <Award className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">Healthcare providers</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                <Building2 className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-orange-600">{statusCounts.hospitals}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Hospitals</p>
                <div className="flex items-center mt-1">
                  <Crown className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600 font-medium">Organizations</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="modern-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search users by name, email, or organization..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-muted/30 border-border/50 focus:bg-background transition-colors"
                  />
                </div>
              </div>
              
              {/* Filter by Role */}
              <Select value={filterRole} onValueChange={setFilterRole}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="doctor">Doctors</SelectItem>
                  <SelectItem value="patient">Patients</SelectItem>
                  <SelectItem value="hospital">Hospitals</SelectItem>
                  <SelectItem value="admin">Admins</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Filter by Status */}
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="created">Date Created</SelectItem>
                  <SelectItem value="last-login">Last Login</SelectItem>
                  <SelectItem value="role">Role</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Users List */}
        <div className="grid gap-4 sm:gap-6">
          {filteredUsers.length === 0 ? (
            <Card className="modern-card">
              <CardContent className="text-center py-12">
                <div className="relative">
                  <Users className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                  <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No users found</h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm || filterRole !== 'all' || filterStatus !== 'all'
                    ? 'Try adjusting your search or filter criteria.'
                    : 'No users have been added to the platform yet.'
                  }
                </p>
                <Button className="btn-primary">
                  <Plus className="h-4 w-4 mr-2" />
                  Add User
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredUsers.map((user) => (
              <Card key={user.id} className={`modern-card p-6 bg-gradient-to-r ${getStatusColor(user.status)} border hover:scale-[1.01] transition-all duration-300`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-16 w-16 ring-2 ring-white/50 shadow-lg">
                      <AvatarImage src={`/avatars/${user.avatar}.jpg`} />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold text-lg">
                        {user.avatar}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-bold text-xl">{user.name}</h3>
                        <Badge variant="outline" className={`modern-badge ${getRoleColor(user.role)}`}>
                          {getRoleIcon(user.role)}
                          <span className="ml-1 capitalize">{user.role}</span>
                        </Badge>
                        {user.verified && (
                          <Badge variant="outline" className="modern-badge bg-green-100 text-green-800 border-green-200">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Verified
                          </Badge>
                        )}
                      </div>
                      <p className="opacity-80 font-medium">{user.email}</p>
                      <p className="text-sm opacity-70">
                        {user.organization || user.location} • {user.subscription} Plan
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <MessageSquare className="h-4 w-4" />
                    </Button>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-10 w-10 p-0 hover:bg-white/20"
                          onClick={() => setSelectedUser(user)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="modern-card max-w-3xl">
                        <DialogHeader>
                          <DialogTitle className="flex items-center space-x-3">
                            <Avatar className="h-12 w-12 ring-2 ring-primary/20">
                              <AvatarImage src={`/avatars/${user.avatar}.jpg`} />
                              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold">
                                {user.avatar}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <span className="text-xl">{user.name}</span>
                              <p className="text-sm text-muted-foreground font-normal capitalize">{user.role}</p>
                            </div>
                          </DialogTitle>
                          <DialogDescription>
                            Complete user profile and account information
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-6">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Contact Information</h4>
                              <div className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                                <p>📧 {user.email}</p>
                                <p>📞 {user.phone}</p>
                                <p>📍 {user.location}</p>
                              </div>
                            </div>
                            <div className="modern-card p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                              <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Account Status</h4>
                              <div className="space-y-1 text-sm text-green-700 dark:text-green-300">
                                <p>Status: <span className="capitalize font-medium">{user.status}</span></p>
                                <p>Verified: {user.verified ? '✅ Yes' : '❌ No'}</p>
                                <p>Plan: {user.subscription}</p>
                              </div>
                            </div>
                          </div>
                          
                          {user.role === 'doctor' && (
                            <div className="modern-card p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                              <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">Professional Details</h4>
                              <div className="grid grid-cols-2 gap-4 text-sm text-purple-700 dark:text-purple-300">
                                <div>
                                  <p><strong>Specialization:</strong> {user.specialization}</p>
                                  <p><strong>Organization:</strong> {user.organization}</p>
                                </div>
                                <div>
                                  <p><strong>Patients:</strong> {user.patients}</p>
                                  <p><strong>Adherence Rate:</strong> {user.adherenceRate}%</p>
                                </div>
                              </div>
                            </div>
                          )}
                          
                          {user.role === 'hospital' && (
                            <div className="modern-card p-4 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200 dark:border-orange-800">
                              <h4 className="font-semibold text-orange-900 dark:text-orange-100 mb-2">Organization Details</h4>
                              <div className="grid grid-cols-2 gap-4 text-sm text-orange-700 dark:text-orange-300">
                                <div>
                                  <p><strong>Organization:</strong> {user.organization}</p>
                                  <p><strong>Location:</strong> {user.location}</p>
                                </div>
                                <div>
                                  <p><strong>Total Patients:</strong> {user.patients?.toLocaleString()}</p>
                                  <p><strong>Avg Adherence:</strong> {user.adherenceRate}%</p>
                                </div>
                              </div>
                            </div>
                          )}
                          
                          <div className="modern-card p-4 bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20 border border-gray-200 dark:border-gray-800">
                            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Account Activity</h4>
                            <div className="grid grid-cols-2 gap-4 text-sm text-gray-700 dark:text-gray-300">
                              <div>
                                <p><strong>Created:</strong> {format(user.createdAt, 'MMM d, yyyy')}</p>
                                <p><strong>Days Active:</strong> {differenceInDays(new Date(), user.createdAt)} days</p>
                              </div>
                              <div>
                                <p><strong>Last Login:</strong> {user.lastLogin ? format(user.lastLogin, 'MMM d, yyyy') : 'Never'}</p>
                                <p><strong>Login Frequency:</strong> Daily</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold">{differenceInDays(new Date(), user.createdAt)}</div>
                    <div className="text-xs opacity-70">Days Active</div>
                  </div>
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold">{user.lastLogin ? differenceInDays(new Date(), user.lastLogin) : 'N/A'}</div>
                    <div className="text-xs opacity-70">Days Since Login</div>
                  </div>
                  {user.patients && (
                    <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                      <div className="text-lg font-bold">{user.patients.toLocaleString()}</div>
                      <div className="text-xs opacity-70">Patients</div>
                    </div>
                  )}
                  {user.adherenceRate && (
                    <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                      <div className="text-lg font-bold text-green-600">{user.adherenceRate}%</div>
                      <div className="text-xs opacity-70">Adherence</div>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-sm opacity-80">
                    <Calendar className="h-4 w-4" />
                    <span>Joined {format(user.createdAt, 'MMM yyyy')}</span>
                  </div>
                  <div className="flex space-x-2">
                    {user.status === 'active' ? (
                      <Button variant="outline" size="sm" className="hover:bg-white/20">
                        <Ban className="h-4 w-4 mr-1" />
                        Suspend
                      </Button>
                    ) : (
                      <Button variant="outline" size="sm" className="hover:bg-white/20">
                        <UserCheck className="h-4 w-4 mr-1" />
                        Activate
                      </Button>
                    )}
                    <Button variant="outline" size="sm" className="hover:bg-white/20">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}