'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Building2, 
  Search, 
  Filter, 
  Plus, 
  Eye, 
  MessageSquare,
  Phone,
  Calendar,
  Activity,
  Users,
  Stethoscope,
  TrendingUp,
  MoreHorizontal,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  Target,
  Sparkles,
  Zap,
  Star,
  Award,
  Crown,
  DollarSign,
  MapPin,
  Globe,
  Mail,
  Shield
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { format, differenceInDays } from 'date-fns';

// Mock hospitals data
const mockHospitals = [
  {
    id: '1',
    name: 'City General Hospital',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'https://citygeneral.com',
    address: '123 Medical Center Dr, New York, NY 10001',
    location: 'New York, NY',
    avatar: 'CG',
    status: 'active',
    plan: 'Enterprise',
    joinDate: new Date('2022-03-15'),
    lastActivity: new Date('2024-01-20'),
    doctors: 45,
    patients: 1250,
    departments: 12,
    adherenceRate: 89,
    revenue: 125000,
    rating: 4.8,
    reviews: 234,
    verified: true,
    specialties: ['Cardiology', 'Neurology', 'Oncology', 'Pediatrics'],
    certifications: ['Joint Commission', 'HIMSS Level 7', 'Magnet Recognition'],
    contactPerson: 'Dr. Sarah Wilson',
    contactTitle: 'Chief Medical Officer'
  },
  {
    id: '2',
    name: 'St. Mary Medical Center',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'https://stmarymedical.com',
    address: '456 Healthcare Blvd, Los Angeles, CA 90210',
    location: 'Los Angeles, CA',
    avatar: 'SM',
    status: 'active',
    plan: 'Professional',
    joinDate: new Date('2022-06-10'),
    lastActivity: new Date('2024-01-19'),
    doctors: 38,
    patients: 980,
    departments: 10,
    adherenceRate: 92,
    revenue: 98000,
    rating: 4.7,
    reviews: 189,
    verified: true,
    specialties: ['Emergency Medicine', 'Surgery', 'Radiology', 'Pathology'],
    certifications: ['Joint Commission', 'AAAHC Accredited'],
    contactPerson: 'Dr. Michael Chen',
    contactTitle: 'Medical Director'
  },
  {
    id: '3',
    name: 'Regional Health System',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'https://regionalhealthsystem.com',
    address: '789 Regional Way, Chicago, IL 60601',
    location: 'Chicago, IL',
    avatar: 'RH',
    status: 'active',
    plan: 'Enterprise',
    joinDate: new Date('2021-11-20'),
    lastActivity: new Date('2024-01-18'),
    doctors: 52,
    patients: 1450,
    departments: 15,
    adherenceRate: 87,
    revenue: 145000,
    rating: 4.6,
    reviews: 298,
    verified: true,
    specialties: ['Cardiology', 'Orthopedics', 'Gastroenterology', 'Pulmonology'],
    certifications: ['Joint Commission', 'HIMSS Level 6', 'ISO 9001'],
    contactPerson: 'Dr. Emily Rodriguez',
    contactTitle: 'Chief Executive Officer'
  },
  {
    id: '4',
    name: 'Community Care Clinic',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'https://communitycare.com',
    address: '321 Community St, Houston, TX 77001',
    location: 'Houston, TX',
    avatar: 'CC',
    status: 'trial',
    plan: 'Basic',
    joinDate: new Date('2023-08-05'),
    lastActivity: new Date('2024-01-15'),
    doctors: 22,
    patients: 650,
    departments: 6,
    adherenceRate: 85,
    revenue: 45000,
    rating: 4.5,
    reviews: 145,
    verified: false,
    specialties: ['Family Medicine', 'Pediatrics', 'Internal Medicine'],
    certifications: ['AAAHC Accredited'],
    contactPerson: 'Dr. Robert Johnson',
    contactTitle: 'Medical Director'
  },
  {
    id: '5',
    name: 'Metro Health Center',
    email: '<EMAIL>',
    phone: '+****************',
    website: 'https://metrohealth.com',
    address: '654 Metro Ave, Phoenix, AZ 85001',
    location: 'Phoenix, AZ',
    avatar: 'MH',
    status: 'suspended',
    plan: 'Professional',
    joinDate: new Date('2023-02-14'),
    lastActivity: new Date('2024-01-05'),
    doctors: 31,
    patients: 820,
    departments: 8,
    adherenceRate: 78,
    revenue: 67000,
    rating: 4.2,
    reviews: 98,
    verified: false,
    specialties: ['Emergency Medicine', 'Urgent Care', 'Radiology'],
    certifications: ['Joint Commission'],
    contactPerson: 'Dr. Amanda Foster',
    contactTitle: 'Chief Medical Officer'
  }
];

export default function HospitalsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPlan, setFilterPlan] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [isNewHospitalOpen, setIsNewHospitalOpen] = useState(false);
  const [selectedHospital, setSelectedHospital] = useState<typeof mockHospitals[0] | null>(null);

  // Form state for new hospital
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    website: '',
    address: '',
    contactPerson: '',
    contactTitle: '',
    plan: 'Basic',
    specialties: '',
    description: ''
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'trial': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      case 'suspended': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      case 'pending': return 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'Enterprise': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Professional': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Basic': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'trial': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'suspended': return 'bg-red-100 text-red-800 border-red-200';
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Filter and sort hospitals
  const filteredHospitals = mockHospitals
    .filter(hospital => {
      const matchesSearch = hospital.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          hospital.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          hospital.specialties.some(s => s.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesStatus = filterStatus === 'all' || hospital.status === filterStatus;
      const matchesPlan = filterPlan === 'all' || hospital.plan === filterPlan;
      
      return matchesSearch && matchesStatus && matchesPlan;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'patients':
          return b.patients - a.patients;
        case 'revenue':
          return b.revenue - a.revenue;
        case 'rating':
          return b.rating - a.rating;
        case 'joined':
          return b.joinDate.getTime() - a.joinDate.getTime();
        default:
          return 0;
      }
    });

  const handleCreateHospital = () => {
    // Handle hospital creation logic here
    setIsNewHospitalOpen(false);
    setFormData({
      name: '',
      email: '',
      phone: '',
      website: '',
      address: '',
      contactPerson: '',
      contactTitle: '',
      plan: 'Basic',
      specialties: '',
      description: ''
    });
  };

  const getStatusCounts = () => {
    return {
      total: mockHospitals.length,
      active: mockHospitals.filter(h => h.status === 'active').length,
      trial: mockHospitals.filter(h => h.status === 'trial').length,
      suspended: mockHospitals.filter(h => h.status === 'suspended').length,
      totalPatients: mockHospitals.reduce((sum, h) => sum + h.patients, 0),
      totalDoctors: mockHospitals.reduce((sum, h) => sum + h.doctors, 0),
      totalRevenue: mockHospitals.reduce((sum, h) => sum + h.revenue, 0),
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/admin' },
            { label: 'Hospitals', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Building2 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Hospital Network</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Manage healthcare organizations and their performance
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Activity className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Network Analytics</span>
              <span className="sm:hidden">Analytics</span>
            </Button>
            <Dialog open={isNewHospitalOpen} onOpenChange={setIsNewHospitalOpen}>
              <DialogTrigger asChild>
                <Button className="btn-primary w-full sm:w-auto">
                  <Plus className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Add Hospital</span>
                  <span className="sm:hidden">Add</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="modern-card max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center space-x-2">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                      <Building2 className="h-5 w-5 text-white" />
                    </div>
                    <span>Add New Hospital</span>
                  </DialogTitle>
                  <DialogDescription>
                    Register a new healthcare organization to the platform
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Hospital Name</Label>
                      <Input
                        id="name"
                        placeholder="City General Hospital"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        placeholder="+****************"
                        value={formData.phone}
                        onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="website">Website</Label>
                      <Input
                        id="website"
                        placeholder="https://hospital.com"
                        value={formData.website}
                        onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      placeholder="123 Medical Center Dr, City, State 12345"
                      value={formData.address}
                      onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contactPerson">Contact Person</Label>
                      <Input
                        id="contactPerson"
                        placeholder="Dr. John Smith"
                        value={formData.contactPerson}
                        onChange={(e) => setFormData(prev => ({ ...prev, contactPerson: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contactTitle">Contact Title</Label>
                      <Input
                        id="contactTitle"
                        placeholder="Chief Medical Officer"
                        value={formData.contactTitle}
                        onChange={(e) => setFormData(prev => ({ ...prev, contactTitle: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="plan">Subscription Plan</Label>
                    <Select value={formData.plan} onValueChange={(value) => setFormData(prev => ({ ...prev, plan: value }))}>
                      <SelectTrigger className="bg-muted/30 border-border/50">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Basic">Basic</SelectItem>
                        <SelectItem value="Professional">Professional</SelectItem>
                        <SelectItem value="Enterprise">Enterprise</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="specialties">Specialties</Label>
                    <Input
                      id="specialties"
                      placeholder="Cardiology, Neurology, Oncology"
                      value={formData.specialties}
                      onChange={(e) => setFormData(prev => ({ ...prev, specialties: e.target.value }))}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="Brief description of the hospital..."
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      className="bg-muted/30 border-border/50 min-h-[80px]"
                    />
                  </div>

                  <div className="flex justify-end space-x-3">
                    <Button variant="outline" onClick={() => setIsNewHospitalOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateHospital} className="btn-primary">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Hospital
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Building2 className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{statusCounts.total}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Hospitals</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">Network partners</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{statusCounts.active}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">Fully operational</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">{(statusCounts.totalPatients / 1000).toFixed(1)}K</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Patients</p>
                <div className="flex items-center mt-1">
                  <Award className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">Across network</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-orange-600">${(statusCounts.totalRevenue / 1000).toFixed(0)}K</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Monthly Revenue</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600 font-medium">+15% this month</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="modern-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search hospitals by name, location, or specialty..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-muted/30 border-border/50 focus:bg-background transition-colors"
                  />
                </div>
              </div>
              
              {/* Filter by Status */}
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="trial">Trial</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Filter by Plan */}
              <Select value={filterPlan} onValueChange={setFilterPlan}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Filter by plan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Plans</SelectItem>
                  <SelectItem value="Enterprise">Enterprise</SelectItem>
                  <SelectItem value="Professional">Professional</SelectItem>
                  <SelectItem value="Basic">Basic</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="patients">Patient Count</SelectItem>
                  <SelectItem value="revenue">Revenue</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                  <SelectItem value="joined">Date Joined</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Hospitals List */}
        <div className="grid gap-4 sm:gap-6">
          {filteredHospitals.length === 0 ? (
            <Card className="modern-card">
              <CardContent className="text-center py-12">
                <div className="relative">
                  <Building2 className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                  <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No hospitals found</h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm || filterStatus !== 'all' || filterPlan !== 'all'
                    ? 'Try adjusting your search or filter criteria.'
                    : 'Add your first hospital to get started.'
                  }
                </p>
                <Button className="btn-primary" onClick={() => setIsNewHospitalOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Hospital
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredHospitals.map((hospital) => (
              <Card key={hospital.id} className={`modern-card p-6 bg-gradient-to-r ${getStatusColor(hospital.status)} border hover:scale-[1.01] transition-all duration-300`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-16 w-16 ring-2 ring-white/50 shadow-lg">
                      <AvatarImage src={`/avatars/${hospital.avatar}.jpg`} />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold text-lg">
                        {hospital.avatar}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-bold text-xl">{hospital.name}</h3>
                        <Badge variant="outline" className={`modern-badge ${getStatusBadgeColor(hospital.status)}`}>
                          {hospital.status}
                        </Badge>
                        <Badge variant="outline" className={`modern-badge ${getPlanColor(hospital.plan)}`}>
                          {hospital.plan}
                        </Badge>
                        {hospital.verified && (
                          <Badge variant="outline" className="modern-badge bg-green-100 text-green-800 border-green-200">
                            <Shield className="h-3 w-3 mr-1" />
                            Verified
                          </Badge>
                        )}
                      </div>
                      <p className="opacity-80 font-medium flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {hospital.location}
                      </p>
                      <p className="text-sm opacity-70">
                        {hospital.contactPerson} • {hospital.contactTitle}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <Mail className="h-4 w-4" />
                    </Button>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-10 w-10 p-0 hover:bg-white/20"
                          onClick={() => setSelectedHospital(hospital)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="modern-card max-w-4xl max-h-[90vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle className="flex items-center space-x-3">
                            <Avatar className="h-12 w-12 ring-2 ring-primary/20">
                              <AvatarImage src={`/avatars/${hospital.avatar}.jpg`} />
                              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold">
                                {hospital.avatar}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <span className="text-xl">{hospital.name}</span>
                              <p className="text-sm text-muted-foreground font-normal">{hospital.location}</p>
                            </div>
                          </DialogTitle>
                          <DialogDescription>
                            Complete hospital profile and performance metrics
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-6">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Contact Information</h4>
                              <div className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                                <p>📧 {hospital.email}</p>
                                <p>📞 {hospital.phone}</p>
                                <p>🌐 {hospital.website}</p>
                                <p>📍 {hospital.address}</p>
                              </div>
                            </div>
                            <div className="modern-card p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                              <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Performance Metrics</h4>
                              <div className="space-y-1 text-sm text-green-700 dark:text-green-300">
                                <p>👥 {hospital.patients.toLocaleString()} patients</p>
                                <p>👨‍⚕️ {hospital.doctors} doctors</p>
                                <p>📊 {hospital.adherenceRate}% adherence</p>
                                <p>⭐ {hospital.rating}/5 ({hospital.reviews} reviews)</p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="modern-card p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                            <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">Specialties</h4>
                            <div className="flex flex-wrap gap-2">
                              {hospital.specialties.map((specialty, index) => (
                                <Badge key={index} variant="outline" className="modern-badge">
                                  {specialty}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          
                          <div className="modern-card p-4 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                            <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">Certifications</h4>
                            <div className="flex flex-wrap gap-2">
                              {hospital.certifications.map((cert, index) => (
                                <Badge key={index} variant="outline" className="modern-badge">
                                  <Award className="h-3 w-3 mr-1" />
                                  {cert}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="modern-card p-4 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200 dark:border-orange-800">
                              <h4 className="font-semibold text-orange-900 dark:text-orange-100 mb-2">Financial</h4>
                              <div className="space-y-1 text-sm text-orange-700 dark:text-orange-300">
                                <p><strong>Plan:</strong> {hospital.plan}</p>
                                <p><strong>Monthly Revenue:</strong> ${hospital.revenue.toLocaleString()}</p>
                                <p><strong>Departments:</strong> {hospital.departments}</p>
                              </div>
                            </div>
                            <div className="modern-card p-4 bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20 border border-gray-200 dark:border-gray-800">
                              <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Account Details</h4>
                              <div className="space-y-1 text-sm text-gray-700 dark:text-gray-300">
                                <p><strong>Joined:</strong> {format(hospital.joinDate, 'MMM d, yyyy')}</p>
                                <p><strong>Last Activity:</strong> {format(hospital.lastActivity, 'MMM d, yyyy')}</p>
                                <p><strong>Status:</strong> {hospital.status}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold">{hospital.patients.toLocaleString()}</div>
                    <div className="text-xs opacity-70">Patients</div>
                  </div>
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold">{hospital.doctors}</div>
                    <div className="text-xs opacity-70">Doctors</div>
                  </div>
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold text-green-600">{hospital.adherenceRate}%</div>
                    <div className="text-xs opacity-70">Adherence</div>
                  </div>
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold text-yellow-600">{hospital.rating}⭐</div>
                    <div className="text-xs opacity-70">Rating</div>
                  </div>
                </div>

                <div className="space-y-3 mb-4">
                  <div className="flex flex-wrap gap-1">
                    {hospital.specialties.slice(0, 3).map((specialty, index) => (
                      <Badge key={index} variant="outline" className="text-xs modern-badge">
                        {specialty}
                      </Badge>
                    ))}
                    {hospital.specialties.length > 3 && (
                      <Badge variant="outline" className="text-xs modern-badge">
                        +{hospital.specialties.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-sm opacity-80">
                    <Calendar className="h-4 w-4" />
                    <span>Joined {format(hospital.joinDate, 'MMM yyyy')}</span>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" className="hover:bg-white/20">
                      <Globe className="h-4 w-4 mr-1" />
                      Visit
                    </Button>
                    <Button variant="outline" size="sm" className="hover:bg-white/20">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}