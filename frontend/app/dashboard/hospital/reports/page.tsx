'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  FileText, 
  Download, 
  Share, 
  Plus, 
  Eye, 
  Calendar,
  Filter,
  Search,
  BarChart3,
  Users,
  Activity,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  Building2,
  <PERSON>ethoscope,
  Target,
  Award,
  Sparkles,
  Zap,
  Star,
  Send,
  Settings,
  Pill,
  Heart,
  Brain
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { format, subDays, subMonths } from 'date-fns';

// Mock reports data focused on medicine adherence for different diseases
const mockReports = [
  {
    id: '1',
    title: 'Diabetes Medication Adherence Report - January 2024',
    description: 'Comprehensive analysis of diabetes medication adherence including Metformin, Insulin, and other diabetes medications',
    type: 'diabetes',
    period: 'monthly',
    generatedAt: new Date('2024-01-31'),
    generatedBy: 'Dr. Michael Chen',
    status: 'completed',
    fileSize: '2.4 MB',
    downloadCount: 23,
    departments: ['Endocrinology'],
    metrics: {
      totalPatients: 245,
      avgAdherence: 94,
      improvement: '+3.2%'
    }
  },
  {
    id: '2',
    title: 'Hypertension Medication Adherence Analysis',
    description: 'Detailed analysis of blood pressure medication adherence including ACE inhibitors, Beta blockers, and Diuretics',
    type: 'hypertension',
    period: 'quarterly',
    generatedAt: new Date('2024-01-20'),
    generatedBy: 'Dr. Sarah Wilson',
    status: 'completed',
    fileSize: '3.1 MB',
    downloadCount: 18,
    departments: ['Cardiology'],
    metrics: {
      totalPatients: 198,
      avgAdherence: 88,
      improvement: '+2.1%'
    }
  },
  {
    id: '3',
    title: 'Asthma Medication Adherence Study',
    description: 'Analysis of inhaler usage and adherence patterns for asthma patients including rescue and controller medications',
    type: 'asthma',
    period: 'custom',
    generatedAt: new Date('2024-01-15'),
    generatedBy: 'Dr. Emily Rodriguez',
    status: 'completed',
    fileSize: '1.8 MB',
    downloadCount: 12,
    departments: ['Pulmonology'],
    metrics: {
      totalPatients: 156,
      avgAdherence: 91,
      improvement: '+4.5%'
    }
  },
  {
    id: '4',
    title: 'Heart Disease Medication Effectiveness Report',
    description: 'Comprehensive study of cardiac medication adherence including statins, anticoagulants, and heart failure medications',
    type: 'heart-disease',
    period: 'custom',
    generatedAt: new Date('2024-01-10'),
    generatedBy: 'Dr. Sarah Wilson',
    status: 'completed',
    fileSize: '4.2 MB',
    downloadCount: 31,
    departments: ['Cardiology'],
    metrics: {
      totalPatients: 134,
      avgAdherence: 85,
      improvement: '+1.8%'
    }
  }
];

// Disease-specific report types for medicine adherence
const diseaseReportTypes = [
  { value: 'diabetes', label: 'Diabetes Medication Adherence', icon: Target, color: 'bg-blue-100 text-blue-800 border-blue-200' },
  { value: 'hypertension', label: 'Hypertension Medication Adherence', icon: Heart, color: 'bg-red-100 text-red-800 border-red-200' },
  { value: 'asthma', label: 'Asthma Medication Adherence', icon: Activity, color: 'bg-green-100 text-green-800 border-green-200' },
  { value: 'heart-disease', label: 'Heart Disease Medication Adherence', icon: Heart, color: 'bg-purple-100 text-purple-800 border-purple-200' },
  { value: 'copd', label: 'COPD Medication Adherence', icon: Activity, color: 'bg-orange-100 text-orange-800 border-orange-200' },
  { value: 'depression', label: 'Depression Medication Adherence', icon: Brain, color: 'bg-indigo-100 text-indigo-800 border-indigo-200' },
  { value: 'arthritis', label: 'Arthritis Medication Adherence', icon: Target, color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
  { value: 'epilepsy', label: 'Epilepsy Medication Adherence', icon: Brain, color: 'bg-pink-100 text-pink-800 border-pink-200' },
  { value: 'custom', label: 'Custom Disease Analysis', icon: Settings, color: 'bg-gray-100 text-gray-800 border-gray-200' }
];

const departments = [
  'All Departments',
  'Cardiology',
  'Endocrinology', 
  'Pulmonology',
  'Neurology',
  'Pediatrics',
  'Orthopedics',
  'Psychiatry',
  'Rheumatology'
];

// Specific medications by disease category
const medicationsByDisease = {
  diabetes: ['Metformin', 'Insulin', 'Glipizide', 'Pioglitazone', 'Sitagliptin'],
  hypertension: ['Lisinopril', 'Amlodipine', 'Hydrochlorothiazide', 'Metoprolol', 'Losartan'],
  asthma: ['Albuterol', 'Fluticasone', 'Budesonide', 'Montelukast', 'Ipratropium'],
  'heart-disease': ['Atorvastatin', 'Clopidogrel', 'Warfarin', 'Carvedilol', 'Aspirin'],
  copd: ['Tiotropium', 'Albuterol', 'Budesonide', 'Theophylline', 'Prednisone'],
  depression: ['Sertraline', 'Fluoxetine', 'Escitalopram', 'Bupropion', 'Venlafaxine'],
  arthritis: ['Methotrexate', 'Prednisone', 'Ibuprofen', 'Adalimumab', 'Sulfasalazine'],
  epilepsy: ['Phenytoin', 'Carbamazepine', 'Valproic Acid', 'Levetiracetam', 'Lamotrigine']
};

export default function ReportsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [isNewReportOpen, setIsNewReportOpen] = useState(false);

  // Form state for new report
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    diseaseType: '',
    customDisease: '',
    period: 'monthly',
    departments: [] as string[],
    medications: [] as string[],
    includeCharts: true,
    includeRawData: false,
    includeComparisons: true,
    includeTrends: true,
    format: 'pdf',
    recipients: '',
    scheduledGeneration: false
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'processing': return 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100';
      case 'failed': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'processing': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeColor = (type: string) => {
    const diseaseType = diseaseReportTypes.find(t => t.value === type);
    return diseaseType?.color || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  // Filter and sort reports
  const filteredReports = mockReports
    .filter(report => {
      const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          report.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = filterType === 'all' || report.type === filterType;
      const matchesStatus = filterStatus === 'all' || report.status === filterStatus;
      
      return matchesSearch && matchesType && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return b.generatedAt.getTime() - a.generatedAt.getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        case 'downloads':
          return b.downloadCount - a.downloadCount;
        default:
          return 0;
      }
    });

  const handleCreateReport = () => {
    // Handle report creation logic here
    setIsNewReportOpen(false);
    setFormData({
      title: '',
      description: '',
      diseaseType: '',
      customDisease: '',
      period: 'monthly',
      departments: [],
      medications: [],
      includeCharts: true,
      includeRawData: false,
      includeComparisons: true,
      includeTrends: true,
      format: 'pdf',
      recipients: '',
      scheduledGeneration: false
    });
  };

  const handleDepartmentChange = (department: string, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        departments: [...prev.departments, department]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        departments: prev.departments.filter(d => d !== department)
      }));
    }
  };

  const handleMedicationChange = (medication: string, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        medications: [...prev.medications, medication]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        medications: prev.medications.filter(m => m !== medication)
      }));
    }
  };

  const getStatusCounts = () => {
    return {
      total: mockReports.length,
      completed: mockReports.filter(r => r.status === 'completed').length,
      processing: mockReports.filter(r => r.status === 'processing').length,
      failed: mockReports.filter(r => r.status === 'failed').length,
    };
  };

  const statusCounts = getStatusCounts();
  const availableMedications = formData.diseaseType && formData.diseaseType !== 'custom' 
    ? medicationsByDisease[formData.diseaseType as keyof typeof medicationsByDisease] || []
    : [];

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/hospital' },
            { label: 'Reports', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Medicine Adherence Reports</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Generate detailed adherence reports for different diseases and medications
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Settings className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Report Templates</span>
              <span className="sm:hidden">Templates</span>
            </Button>
            <Dialog open={isNewReportOpen} onOpenChange={setIsNewReportOpen}>
              <DialogTrigger asChild>
                <Button className="btn-primary w-full sm:w-auto">
                  <Plus className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Generate Report</span>
                  <span className="sm:hidden">Generate</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="fixed top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] w-[95vw] max-w-4xl max-h-[90vh] overflow-y-auto bg-card border border-border rounded-lg shadow-lg z-50 p-0">
                <div className="p-6">
                  <DialogHeader className="mb-6">
                    <DialogTitle className="flex items-center space-x-2">
                      <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                        <Pill className="h-5 w-5 text-white" />
                      </div>
                      <span>Generate Medicine Adherence Report</span>
                    </DialogTitle>
                    <DialogDescription>
                      Create a custom adherence report for specific diseases and medications
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="title">Report Title</Label>
                        <Input
                          id="title"
                          placeholder="Diabetes Medication Adherence Report"
                          value={formData.title}
                          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                          className="bg-muted/30 border-border/50"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="diseaseType">Disease/Condition</Label>
                        <Select value={formData.diseaseType} onValueChange={(value) => setFormData(prev => ({ ...prev, diseaseType: value, medications: [] }))}>
                          <SelectTrigger className="bg-muted/30 border-border/50">
                            <SelectValue placeholder="Select disease type" />
                          </SelectTrigger>
                          <SelectContent>
                            {diseaseReportTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                <div className="flex items-center">
                                  <type.icon className="h-4 w-4 mr-2" />
                                  {type.label}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {formData.diseaseType === 'custom' && (
                      <div className="space-y-2">
                        <Label htmlFor="customDisease">Custom Disease/Condition</Label>
                        <Input
                          id="customDisease"
                          placeholder="Enter custom disease or condition"
                          value={formData.customDisease}
                          onChange={(e) => setFormData(prev => ({ ...prev, customDisease: e.target.value }))}
                          className="bg-muted/30 border-border/50"
                        />
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="description">Report Description</Label>
                      <Textarea
                        id="description"
                        placeholder="Brief description of what this report will analyze..."
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        className="bg-muted/30 border-border/50 min-h-[80px]"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="period">Time Period</Label>
                        <Select value={formData.period} onValueChange={(value) => setFormData(prev => ({ ...prev, period: value }))}>
                          <SelectTrigger className="bg-muted/30 border-border/50">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                            <SelectItem value="quarterly">Quarterly</SelectItem>
                            <SelectItem value="yearly">Yearly</SelectItem>
                            <SelectItem value="custom">Custom Range</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="format">Export Format</Label>
                        <Select value={formData.format} onValueChange={(value) => setFormData(prev => ({ ...prev, format: value }))}>
                          <SelectTrigger className="bg-muted/30 border-border/50">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pdf">PDF Document</SelectItem>
                            <SelectItem value="excel">Excel Spreadsheet</SelectItem>
                            <SelectItem value="csv">CSV Data</SelectItem>
                            <SelectItem value="powerpoint">PowerPoint</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label>Include Departments</Label>
                      <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                        {departments.slice(1).map((dept) => (
                          <div key={dept} className="flex items-center space-x-2">
                            <Checkbox
                              id={dept}
                              checked={formData.departments.includes(dept)}
                              onCheckedChange={(checked) => handleDepartmentChange(dept, checked as boolean)}
                            />
                            <Label htmlFor={dept} className="text-sm">{dept}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    {availableMedications.length > 0 && (
                      <div className="space-y-3">
                        <Label>Specific Medications to Analyze</Label>
                        <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                          {availableMedications.map((medication) => (
                            <div key={medication} className="flex items-center space-x-2">
                              <Checkbox
                                id={medication}
                                checked={formData.medications.includes(medication)}
                                onCheckedChange={(checked) => handleMedicationChange(medication, checked as boolean)}
                              />
                              <Label htmlFor={medication} className="text-sm">{medication}</Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    <div className="space-y-3">
                      <Label>Report Content Options</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="includeCharts"
                            checked={formData.includeCharts}
                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, includeCharts: checked as boolean }))}
                          />
                          <Label htmlFor="includeCharts" className="text-sm">Include adherence charts and visualizations</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="includeComparisons"
                            checked={formData.includeComparisons}
                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, includeComparisons: checked as boolean }))}
                          />
                          <Label htmlFor="includeComparisons" className="text-sm">Include medication comparisons</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="includeTrends"
                            checked={formData.includeTrends}
                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, includeTrends: checked as boolean }))}
                          />
                          <Label htmlFor="includeTrends" className="text-sm">Include trend analysis</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="includeRawData"
                            checked={formData.includeRawData}
                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, includeRawData: checked as boolean }))}
                          />
                          <Label htmlFor="includeRawData" className="text-sm">Include raw data tables</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="scheduledGeneration"
                            checked={formData.scheduledGeneration}
                            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, scheduledGeneration: checked as boolean }))}
                          />
                          <Label htmlFor="scheduledGeneration" className="text-sm">Schedule automatic generation</Label>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="recipients">Email Recipients (optional)</Label>
                      <Input
                        id="recipients"
                        placeholder="<EMAIL>, <EMAIL>"
                        value={formData.recipients}
                        onChange={(e) => setFormData(prev => ({ ...prev, recipients: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>

                    <div className="flex justify-end space-x-3 pt-4 border-t">
                      <Button variant="outline" onClick={() => setIsNewReportOpen(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleCreateReport} className="btn-primary">
                        <Send className="h-4 w-4 mr-2" />
                        Generate Report
                      </Button>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{statusCounts.total}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Reports</p>
                <div className="flex items-center mt-1">
                  <BarChart3 className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">Adherence focused</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{statusCounts.completed}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Completed</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">Ready to download</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-yellow-600">{statusCounts.processing}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Processing</p>
                <div className="flex items-center mt-1">
                  <Zap className="h-3 w-3 text-yellow-500 mr-1" />
                  <span className="text-xs text-yellow-600 font-medium">In progress</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Download className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">
                  {mockReports.reduce((sum, r) => sum + r.downloadCount, 0)}
                </p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Downloads</p>
                <div className="flex items-center mt-1">
                  <Star className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">This month</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="modern-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search reports by disease, medication, or title..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-muted/30 border-border/50 focus:bg-background transition-colors"
                  />
                </div>
              </div>
              
              {/* Filter by Disease Type */}
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by disease" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Diseases</SelectItem>
                  {diseaseReportTypes.slice(0, -1).map((type) => (
                    <SelectItem key={type.value} value={type.value}>{type.label.replace(' Medication Adherence', '')}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {/* Filter by Status */}
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Date Generated</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="downloads">Downloads</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Reports List */}
        <div className="grid gap-4 sm:gap-6">
          {filteredReports.length === 0 ? (
            <Card className="modern-card">
              <CardContent className="text-center py-12">
                <div className="relative">
                  <Pill className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                  <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No adherence reports found</h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm || filterType !== 'all' || filterStatus !== 'all'
                    ? 'Try adjusting your search or filter criteria.'
                    : 'Generate your first medicine adherence report to get started.'
                  }
                </p>
                <Button className="btn-primary" onClick={() => setIsNewReportOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Generate Adherence Report
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredReports.map((report) => (
              <Card key={report.id} className={`modern-card p-6 bg-gradient-to-r ${getStatusColor(report.status)} border hover:scale-[1.01] transition-all duration-300`}>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-4 min-w-0 flex-1">
                    <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                      <Pill className="h-6 w-6" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="font-bold text-lg truncate">{report.title}</h3>
                        <Badge variant="outline" className={`modern-badge ${getStatusBadgeColor(report.status)}`}>
                          {report.status}
                        </Badge>
                        <Badge variant="outline" className={`modern-badge ${getTypeColor(report.type)}`}>
                          {diseaseReportTypes.find(t => t.value === report.type)?.label.replace(' Medication Adherence', '') || report.type}
                        </Badge>
                      </div>
                      <p className="text-sm opacity-80 mb-2">{report.description}</p>
                      <div className="flex items-center space-x-4 text-xs opacity-70">
                        <span>Generated by {report.generatedBy}</span>
                        <span>•</span>
                        <span>{format(report.generatedAt, 'MMM d, yyyy')}</span>
                        <span>•</span>
                        <span>{report.fileSize}</span>
                        <span>•</span>
                        <span>{report.downloadCount} downloads</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-white/20">
                      <Eye className="h-4 w-4" />
                    </Button>
                    {report.status === 'completed' && (
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-white/20">
                        <Download className="h-4 w-4" />
                      </Button>
                    )}
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-white/20">
                      <Share className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 mb-4">
                  {Object.entries(report.metrics).map(([key, value], index) => (
                    <div key={index} className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                      <div className="text-lg font-bold">
                        {typeof value === 'number' && value > 1000 
                          ? value.toLocaleString() 
                          : value}
                      </div>
                      <div className="text-xs opacity-70 capitalize">
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-1">
                    {report.departments.map((dept, index) => (
                      <Badge key={index} variant="outline" className="text-xs modern-badge">
                        {dept}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex space-x-2">
                    {report.status === 'completed' && (
                      <Button variant="outline" size="sm" className="hover:bg-white/20">
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                    )}
                    <Button variant="outline" size="sm" className="hover:bg-white/20">
                      <Share className="h-4 w-4 mr-1" />
                      Share
                    </Button>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}