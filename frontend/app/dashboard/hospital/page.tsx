'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { StatsCard } from '@/components/ui/stats-card';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ErrorMessage } from '@/components/ui/error-message';
import {
  Building2,
  Users,
  Activity,
  TrendingUp,
  Stethoscope,
  AlertTriangle,
  DollarSign,
  Calendar,
  FileText,
  Plus,
  Eye,
  MoreHorizontal,

  Target,
  Zap,
  Heart
} from 'lucide-react';
import Link from 'next/link';
import { useMemo } from 'react';
import { useHospitalData } from '@/hooks/use-hospital-data';
import { useHospitalStore } from '@/lib/stores/hospital-store';

// Hospital dashboard with real API integration



export default function HospitalDashboard() {
  // Use hospital data hooks
  const {
    dashboardSummary,
    patients,
    isLoading,
    hasError,
    error,
    refreshAll,
  } = useHospitalData({
    autoFetchDashboard: true,
    autoFetchPatients: true,
    patientsQuery: { limit: 10 },
  });

  // Process dashboard data for display
  const dashboardStats = useMemo(() => {
    if (!dashboardSummary) {
      return {
        totalPatients: 0,
        totalDoctors: 0,
        totalDepartments: 0,
        activePrescriptions: 0,
        overallAdherence: 0,
        patientsAbove80: 0,
        patientsNeedingIntervention: 0,
        criticalPatients: 0,
        missedAppointments: 0,
        medicationAlerts: 0,
      };
    }

    return {
      totalPatients: dashboardSummary.overview.total_patients,
      totalDoctors: dashboardSummary.overview.total_doctors,
      totalDepartments: dashboardSummary.overview.total_departments,
      activePrescriptions: dashboardSummary.overview.active_prescriptions,
      overallAdherence: dashboardSummary.adherence_metrics.overall_adherence_rate,
      patientsAbove80: dashboardSummary.adherence_metrics.patients_above_80_percent,
      patientsNeedingIntervention: dashboardSummary.adherence_metrics.patients_needing_intervention,
      criticalPatients: dashboardSummary.recent_alerts.critical_patients,
      missedAppointments: dashboardSummary.recent_alerts.missed_appointments,
      medicationAlerts: dashboardSummary.recent_alerts.medication_adherence_alerts,
    };
  }, [dashboardSummary]);



  // Show loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading hospital dashboard..." />
        </div>
      </DashboardLayout>
    );
  }

  // Show error state
  if (hasError) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <ErrorMessage
            message={error || 'Failed to load hospital dashboard'}
            onRetry={refreshAll}
          />
        </div>
      </DashboardLayout>
    );
  }



  // Use real data from API instead of mock data

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/hospital', current: true },
          ]}
        />

        {/* Welcome Section */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Building2 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">
                {dashboardSummary?.hospital_name || 'Hospital Dashboard'} 🏥
              </h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Managing {dashboardStats.totalDoctors} doctors and {dashboardStats.totalPatients} patients across {dashboardStats.totalDepartments} departments
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Link href="/dashboard/hospital/reports">
              <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
                <FileText className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Generate Report</span>
                <span className="sm:hidden">Report</span>
              </Button>
            </Link>
            <Link href="/dashboard/hospital/doctors">
              <Button className="btn-primary w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Add Doctor</span>
                <span className="sm:hidden">Add</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{dashboardStats.totalPatients.toLocaleString()}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Patients</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">
                    {dashboardSummary?.adherence_metrics.trend_direction === 'improving' ? '+' :
                     dashboardSummary?.adherence_metrics.trend_direction === 'declining' ? '-' : ''}
                    {dashboardSummary?.adherence_metrics.trend_direction || 'stable'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <Stethoscope className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{dashboardStats.totalDoctors}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active Doctors</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">{dashboardStats.totalDepartments} departments</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Activity className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">{Math.round(dashboardStats.overallAdherence)}%</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Avg Adherence</p>
                <div className="flex items-center mt-1">
                  <Zap className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">{dashboardStats.patientsAbove80} above 80%</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-orange-600">{dashboardStats.activePrescriptions}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active Prescriptions</p>
                <div className="flex items-center mt-1">
                  <AlertTriangle className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600 font-medium">{dashboardStats.criticalPatients} critical alerts</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Department Overview */}
            <Card className="modern-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                      <Building2 className="h-5 w-5 text-white" />
                    </div>
                    <span>Department Performance</span>
                  </CardTitle>
                  <Link href="/dashboard/hospital/analytics">
                    <Button variant="outline" size="sm" className="hover:bg-accent/50 interactive-element">
                      View Analytics
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Best Performing Department */}
                  {dashboardSummary?.department_highlights.best_performing && (
                    <div className="modern-card p-4 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100 border hover:scale-[1.01] transition-all duration-300">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                            <Building2 className="h-5 w-5" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-sm sm:text-base">
                              {dashboardSummary.department_highlights.best_performing.department}
                            </h3>
                            <p className="text-xs sm:text-sm opacity-80">
                              Best Performing Department
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline" className="text-xs modern-badge">
                          excellent
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-xs opacity-70">Adherence Rate</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Progress value={dashboardSummary.department_highlights.best_performing.adherence_rate} className="h-2 flex-1" />
                            <span className="text-sm font-medium">{Math.round(dashboardSummary.department_highlights.best_performing.adherence_rate)}%</span>
                          </div>
                        </div>
                        <div>
                          <p className="text-xs opacity-70">Status</p>
                          <p className="text-sm font-semibold mt-1 text-green-600">
                            Top Performer
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Department Needing Attention */}
                  {dashboardSummary?.department_highlights.needs_attention && (
                    <div className="modern-card p-4 bg-gradient-to-r from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100 border hover:scale-[1.01] transition-all duration-300">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                            <AlertTriangle className="h-5 w-5" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-sm sm:text-base">
                              {dashboardSummary.department_highlights.needs_attention.department}
                            </h3>
                            <p className="text-xs sm:text-sm opacity-80">
                              {dashboardSummary.department_highlights.needs_attention.patient_count} patients
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline" className="text-xs modern-badge">
                          needs attention
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-xs opacity-70">Adherence Rate</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Progress value={dashboardSummary.department_highlights.needs_attention.adherence_rate} className="h-2 flex-1" />
                            <span className="text-sm font-medium">{Math.round(dashboardSummary.department_highlights.needs_attention.adherence_rate)}%</span>
                          </div>
                        </div>
                        <div>
                          <p className="text-xs opacity-70">Priority</p>
                          <p className="text-sm font-semibold mt-1 text-red-600">
                            High Priority
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Overall Hospital Performance */}
                  <div className="modern-card p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100 border hover:scale-[1.01] transition-all duration-300">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                          <Activity className="h-5 w-5" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-sm sm:text-base">
                            Overall Hospital Performance
                          </h3>
                          <p className="text-xs sm:text-sm opacity-80">
                            {dashboardStats.totalDepartments} departments • {dashboardStats.totalPatients} patients
                          </p>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs modern-badge">
                        {dashboardSummary?.adherence_metrics.trend_direction || 'stable'}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                      <div>
                        <p className="text-xs opacity-70">Overall Adherence</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <Progress value={dashboardStats.overallAdherence} className="h-2 flex-1" />
                          <span className="text-sm font-medium">{Math.round(dashboardStats.overallAdherence)}%</span>
                        </div>
                      </div>
                      <div>
                        <p className="text-xs opacity-70">High Performers</p>
                        <p className="text-sm font-semibold mt-1">
                          {dashboardStats.patientsAbove80} patients
                        </p>
                      </div>
                      <div className="hidden sm:block">
                        <p className="text-xs opacity-70">Need Intervention</p>
                        <p className="text-sm font-semibold mt-1">
                          {dashboardStats.patientsNeedingIntervention} patients
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Patients */}
            <Card className="modern-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                    <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                      <Users className="h-5 w-5 text-white" />
                    </div>
                    <span>Recent Patients</span>
                  </CardTitle>
                  <Link href="/dashboard/hospital/patients">
                    <Button variant="outline" size="sm" className="hover:bg-accent/50 interactive-element">
                      View All
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {patients.length > 0 ? (
                    patients.slice(0, 5).map((patient) => (
                      <div key={patient.patient_id} className="modern-card p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 hover:scale-[1.01] transition-all duration-300">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4 min-w-0 flex-1">
                            <Avatar className="h-10 w-10 sm:h-12 sm:w-12 ring-2 ring-blue-200 dark:ring-blue-800 shadow-lg">
                              <AvatarFallback className="bg-gradient-to-br from-green-500 to-emerald-500 text-white font-semibold">
                                {patient.patient_name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                              </AvatarFallback>
                            </Avatar>
                            <div className="min-w-0 flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <h3 className="font-semibold text-blue-900 dark:text-blue-100 truncate text-sm sm:text-base">
                                  {patient.patient_name}
                                </h3>
                                <Badge variant="default" className="text-xs modern-badge">
                                  active
                                </Badge>
                              </div>
                              <p className="text-xs sm:text-sm text-blue-700 dark:text-blue-300">
                                Dr. {patient.assigned_doctor.name} • {patient.assigned_doctor.specialization}
                              </p>
                              <div className="flex items-center space-x-4 mt-2">
                                <div className="flex items-center space-x-1">
                                  <span className="text-xs text-blue-600 dark:text-blue-400">Medications:</span>
                                  <span className="text-xs font-medium text-blue-900 dark:text-blue-100">{patient.current_medications_count}</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <span className="text-xs text-blue-600 dark:text-blue-400">Last Check:</span>
                                  <span className="text-xs font-medium text-green-600">
                                    {new Date(patient.last_adherence_check).toLocaleDateString()}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <Link href={`/dashboard/hospital/patients/${patient.patient_id}`}>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-800">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </Link>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-800">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No patients found</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* System Alerts */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-white" />
                  </div>
                  <span>System Alerts</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Critical Patients Alert */}
                {dashboardStats.criticalPatients > 0 && (
                  <div className="modern-card p-3 bg-gradient-to-r from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100 border">
                    <div className="flex items-start justify-between">
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm">Critical Patients Alert</p>
                        <p className="text-xs mt-1">{dashboardStats.criticalPatients} patients require immediate attention</p>
                        <p className="text-xs opacity-70 mt-1">Now</p>
                      </div>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-2 hover:bg-white/20">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}

                {/* Medication Alerts */}
                {dashboardStats.medicationAlerts > 0 && (
                  <div className="modern-card p-3 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100 border">
                    <div className="flex items-start justify-between">
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm">Medication Adherence Alert</p>
                        <p className="text-xs mt-1">{dashboardStats.medicationAlerts} medication adherence issues detected</p>
                        <p className="text-xs opacity-70 mt-1">1 hour ago</p>
                      </div>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-2 hover:bg-white/20">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}

                {/* Missed Appointments Alert */}
                {dashboardStats.missedAppointments > 0 && (
                  <div className="modern-card p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100 border">
                    <div className="flex items-start justify-between">
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm">Missed Appointments</p>
                        <p className="text-xs mt-1">{dashboardStats.missedAppointments} appointments missed today</p>
                        <p className="text-xs opacity-70 mt-1">2 hours ago</p>
                      </div>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-2 hover:bg-white/20">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}

                {/* Department Attention Alert */}
                {dashboardSummary?.department_highlights.needs_attention && (
                  <div className="modern-card p-3 bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200 text-purple-900 dark:from-purple-900/20 dark:to-pink-900/20 dark:border-purple-800 dark:text-purple-100 border">
                    <div className="flex items-start justify-between">
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm">Department Alert</p>
                        <p className="text-xs mt-1">
                          {dashboardSummary.department_highlights.needs_attention.department} department needs attention
                        </p>
                        <p className="text-xs opacity-70 mt-1">3 hours ago</p>
                      </div>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-2 hover:bg-white/20">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}

                {/* No alerts message */}
                {dashboardStats.criticalPatients === 0 &&
                 dashboardStats.medicationAlerts === 0 &&
                 dashboardStats.missedAppointments === 0 && (
                  <div className="text-center py-4">
                    <Heart className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">All systems running smoothly</p>
                  </div>
                )}

                <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  View All Alerts
                </Button>
              </CardContent>
            </Card>

            {/* Recent Activities */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Activity className="h-4 w-4 text-white" />
                  </div>
                  <span>Recent Activities</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Recent patient activities based on real data */}
                {patients.length > 0 ? (
                  <>
                    {patients.slice(0, 3).map((patient, index) => (
                      <div key={patient.patient_id} className="modern-card p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                        <div className="flex items-start space-x-3">
                          <div className="p-1.5 bg-blue-100 dark:bg-blue-800 rounded-full mt-0.5">
                            <div className="h-2 w-2 bg-blue-600 dark:bg-blue-300 rounded-full" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <p className="font-semibold text-sm text-blue-900 dark:text-blue-100">
                              Patient adherence check
                            </p>
                            <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                              {patient.patient_name} - Last check: {new Date(patient.last_adherence_check).toLocaleDateString()}
                            </p>
                            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                              {index === 0 ? '2 hours ago' : index === 1 ? '4 hours ago' : '6 hours ago'}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}

                    {/* Dashboard summary activity */}
                    {dashboardSummary && (
                      <div className="modern-card p-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                        <div className="flex items-start space-x-3">
                          <div className="p-1.5 bg-green-100 dark:bg-green-800 rounded-full mt-0.5">
                            <div className="h-2 w-2 bg-green-600 dark:bg-green-300 rounded-full" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <p className="font-semibold text-sm text-green-900 dark:text-green-100">
                              Dashboard updated
                            </p>
                            <p className="text-xs text-green-700 dark:text-green-300 mt-1">
                              Hospital performance metrics refreshed
                            </p>
                            <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                              Just now
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-4">
                    <Activity className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No recent activities</p>
                  </div>
                )}

                <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                  <Activity className="h-4 w-4 mr-2" />
                  View All Activities
                </Button>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Link href="/dashboard/hospital/doctors">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <Stethoscope className="h-4 w-4 mr-2" />
                    Manage Doctors
                  </Button>
                </Link>
                <Link href="/dashboard/hospital/patients">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <Users className="h-4 w-4 mr-2" />
                    Patient Records
                  </Button>
                </Link>
                <Link href="/dashboard/hospital/analytics">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    View Analytics
                  </Button>
                </Link>
                <Link href="/dashboard/hospital/reports">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Reports
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}