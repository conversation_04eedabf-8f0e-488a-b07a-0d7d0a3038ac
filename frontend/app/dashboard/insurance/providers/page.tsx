'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Building2, 
  Search, 
  Filter, 
  Eye, 
  MoreHorizontal,
  MapPin,
  Phone,
  Mail,
  Users,
  Activity,
  TrendingUp,
  Stethoscope,
  Target,
  Award,
  Star,
  FileText,
  Download
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

// Mock hospital data
const mockHospitals = [
  {
    id: '1',
    name: 'City General Hospital',
    location: 'New York, NY',
    address: '123 Medical Center Dr, New York, NY 10001',
    phone: '+****************',
    email: '<EMAIL>',
    website: 'https://citygeneral.com',
    patients: 156,
    doctors: 45,
    adherenceRate: 89,
    avatar: 'CG',
    specialties: ['Cardiology', 'Neurology', 'Oncology', 'Pediatrics'],
    rating: 4.8,
    reviews: 234,
    topDoctors: ['Dr. Sarah Wilson', 'Dr. Robert Johnson']
  },
  {
    id: '2',
    name: 'St. Mary Medical Center',
    location: 'Los Angeles, CA',
    address: '456 Healthcare Blvd, Los Angeles, CA 90210',
    phone: '+****************',
    email: '<EMAIL>',
    website: 'https://stmarymedical.com',
    patients: 203,
    doctors: 38,
    adherenceRate: 92,
    avatar: 'SM',
    specialties: ['Emergency Medicine', 'Surgery', 'Radiology', 'Pathology'],
    rating: 4.7,
    reviews: 189,
    topDoctors: ['Dr. Michael Chen', 'Dr. Amanda Foster']
  },
  {
    id: '3',
    name: 'Regional Health System',
    location: 'Chicago, IL',
    address: '789 Regional Way, Chicago, IL 60601',
    phone: '+****************',
    email: '<EMAIL>',
    website: 'https://regionalhealthsystem.com',
    patients: 134,
    doctors: 52,
    adherenceRate: 87,
    avatar: 'RH',
    specialties: ['Cardiology', 'Orthopedics', 'Gastroenterology', 'Pulmonology'],
    rating: 4.6,
    reviews: 298,
    topDoctors: ['Dr. Emily Rodriguez']
  },
  {
    id: '4',
    name: 'Community Care Clinic',
    location: 'Houston, TX',
    address: '321 Community St, Houston, TX 77001',
    phone: '+****************',
    email: '<EMAIL>',
    website: 'https://communitycare.com',
    patients: 98,
    doctors: 22,
    adherenceRate: 85,
    avatar: 'CC',
    specialties: ['Family Medicine', 'Pediatrics', 'Internal Medicine'],
    rating: 4.5,
    reviews: 145,
    topDoctors: []
  },
  {
    id: '5',
    name: 'Metro Health Center',
    location: 'Phoenix, AZ',
    address: '654 Metro Ave, Phoenix, AZ 85001',
    phone: '+****************',
    email: '<EMAIL>',
    website: 'https://metrohealth.com',
    patients: 112,
    doctors: 31,
    adherenceRate: 78,
    avatar: 'MH',
    specialties: ['Emergency Medicine', 'Urgent Care', 'Radiology'],
    rating: 4.2,
    reviews: 98,
    topDoctors: []
  }
];

// Mock doctor data
const mockDoctors = [
  {
    id: '1',
    name: 'Dr. Sarah Wilson',
    specialization: 'Cardiology',
    hospital: 'City General Hospital',
    patients: 45,
    adherenceRate: 94,
    avatar: 'SW'
  },
  {
    id: '2',
    name: 'Dr. Michael Chen',
    specialization: 'Endocrinology',
    hospital: 'St. Mary Medical Center',
    patients: 52,
    adherenceRate: 91,
    avatar: 'MC'
  },
  {
    id: '3',
    name: 'Dr. Emily Rodriguez',
    specialization: 'Pulmonology',
    hospital: 'Regional Health System',
    patients: 38,
    adherenceRate: 89,
    avatar: 'ER'
  },
  {
    id: '4',
    name: 'Dr. Robert Johnson',
    specialization: 'Neurology',
    hospital: 'City General Hospital',
    patients: 41,
    adherenceRate: 85,
    avatar: 'RJ'
  },
  {
    id: '5',
    name: 'Dr. Amanda Foster',
    specialization: 'Pediatrics',
    hospital: 'St. Mary Medical Center',
    patients: 63,
    adherenceRate: 96,
    avatar: 'AF'
  }
];

export default function ProvidersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAdherence, setFilterAdherence] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [selectedHospital, setSelectedHospital] = useState<typeof mockHospitals[0] | null>(null);
  const [viewMode, setViewMode] = useState<'hospitals' | 'doctors'>('hospitals');

  // Filter and sort hospitals
  const filteredHospitals = mockHospitals
    .filter(hospital => {
      const matchesSearch = hospital.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          hospital.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          hospital.specialties.some(s => s.toLowerCase().includes(searchTerm.toLowerCase()));
      
      if (filterAdherence === 'high') return matchesSearch && hospital.adherenceRate >= 90;
      if (filterAdherence === 'medium') return matchesSearch && hospital.adherenceRate >= 80 && hospital.adherenceRate < 90;
      if (filterAdherence === 'low') return matchesSearch && hospital.adherenceRate < 80;
      
      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'adherence':
          return b.adherenceRate - a.adherenceRate;
        case 'patients':
          return b.patients - a.patients;
        case 'rating':
          return b.rating - a.rating;
        default:
          return 0;
      }
    });

  // Filter and sort doctors
  const filteredDoctors = mockDoctors
    .filter(doctor => {
      const matchesSearch = doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          doctor.specialization.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          doctor.hospital.toLowerCase().includes(searchTerm.toLowerCase());
      
      if (filterAdherence === 'high') return matchesSearch && doctor.adherenceRate >= 90;
      if (filterAdherence === 'medium') return matchesSearch && doctor.adherenceRate >= 80 && doctor.adherenceRate < 90;
      if (filterAdherence === 'low') return matchesSearch && doctor.adherenceRate < 80;
      
      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'adherence':
          return b.adherenceRate - a.adherenceRate;
        case 'patients':
          return b.patients - a.patients;
        case 'hospital':
          return a.hospital.localeCompare(b.hospital);
        default:
          return 0;
      }
    });

  const getAdherenceColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAdherenceBadgeColor = (rate: number) => {
    if (rate >= 90) return 'bg-green-100 text-green-800 border-green-200';
    if (rate >= 80) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/insurance' },
            { label: 'Providers', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Building2 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Healthcare Providers</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Monitor adherence performance across hospitals and doctors
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Download className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Export Report</span>
              <span className="sm:hidden">Export</span>
            </Button>
          </div>
        </div>

        {/* View Toggle */}
        <div className="flex space-x-2">
          <Button 
            variant={viewMode === 'hospitals' ? 'default' : 'outline'} 
            onClick={() => setViewMode('hospitals')}
            className="flex-1 sm:flex-none"
          >
            <Building2 className="h-4 w-4 mr-2" />
            Hospitals
          </Button>
          <Button 
            variant={viewMode === 'doctors' ? 'default' : 'outline'} 
            onClick={() => setViewMode('doctors')}
            className="flex-1 sm:flex-none"
          >
            <Stethoscope className="h-4 w-4 mr-2" />
            Doctors
          </Button>
        </div>

        {/* Filters and Search */}
        <Card className="modern-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder={viewMode === 'hospitals' 
                      ? "Search hospitals by name, location, or specialty..." 
                      : "Search doctors by name, specialization, or hospital..."}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-muted/30 border-border/50 focus:bg-background transition-colors"
                  />
                </div>
              </div>
              
              {/* Filter by Adherence */}
              <Select value={filterAdherence} onValueChange={setFilterAdherence}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by adherence" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Adherence</SelectItem>
                  <SelectItem value="high">High (90%+)</SelectItem>
                  <SelectItem value="medium">Medium (80-89%)</SelectItem>
                  <SelectItem value="low">Low (Below 80%)</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="adherence">Adherence Rate</SelectItem>
                  <SelectItem value="patients">Patient Count</SelectItem>
                  {viewMode === 'hospitals' ? (
                    <SelectItem value="rating">Rating</SelectItem>
                  ) : (
                    <SelectItem value="hospital">Hospital</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Hospitals View */}
        {viewMode === 'hospitals' && (
          <div className="grid gap-4 sm:gap-6">
            {filteredHospitals.length === 0 ? (
              <Card className="modern-card">
                <CardContent className="text-center py-12">
                  <div className="relative">
                    <Building2 className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                    <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">No hospitals found</h3>
                  <p className="text-muted-foreground mb-6">
                    {searchTerm || filterAdherence !== 'all'
                      ? 'Try adjusting your search or filter criteria.'
                      : 'No hospitals have been added to the network yet.'
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              filteredHospitals.map((hospital) => (
                <Card key={hospital.id} className="modern-card p-6 hover:scale-[1.01] transition-all duration-300">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-16 w-16 ring-2 ring-white/50 shadow-lg">
                        <AvatarImage src={`/avatars/${hospital.avatar}.jpg`} />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold text-lg">
                          {hospital.avatar}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-bold text-xl">{hospital.name}</h3>
                          <Badge variant="outline" className={`modern-badge ${getAdherenceBadgeColor(hospital.adherenceRate)}`}>
                            {hospital.adherenceRate}% adherence
                          </Badge>
                        </div>
                        <p className="opacity-80 font-medium flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {hospital.location}
                        </p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {hospital.specialties.slice(0, 3).map((specialty, index) => (
                            <Badge key={index} variant="outline" className="text-xs modern-badge">
                              {specialty}
                            </Badge>
                          ))}
                          {hospital.specialties.length > 3 && (
                            <Badge variant="outline" className="text-xs modern-badge">
                              +{hospital.specialties.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-10 w-10 p-0 hover:bg-white/20"
                            onClick={() => setSelectedHospital(hospital)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="modern-card max-w-3xl">
                          <DialogHeader>
                            <DialogTitle className="flex items-center space-x-3">
                              <Avatar className="h-12 w-12 ring-2 ring-primary/20">
                                <AvatarImage src={`/avatars/${hospital.avatar}.jpg`} />
                                <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold">
                                  {hospital.avatar}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <span className="text-xl">{hospital.name}</span>
                                <p className="text-sm text-muted-foreground font-normal">{hospital.location}</p>
                              </div>
                            </DialogTitle>
                            <DialogDescription>
                              Complete hospital information and adherence metrics
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-6">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                                <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Contact Information</h4>
                                <div className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                                  <p>📧 {hospital.email}</p>
                                  <p>📞 {hospital.phone}</p>
                                  <p>🌐 {hospital.website}</p>
                                  <p>📍 {hospital.address}</p>
                                </div>
                              </div>
                              <div className="modern-card p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                                <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Performance Metrics</h4>
                                <div className="space-y-1 text-sm text-green-700 dark:text-green-300">
                                  <p>👥 {hospital.patients.toLocaleString()} patients</p>
                                  <p>👨‍⚕️ {hospital.doctors} doctors</p>
                                  <p>📊 {hospital.adherenceRate}% adherence</p>
                                  <p>⭐ {hospital.rating}/5 ({hospital.reviews} reviews)</p>
                                </div>
                              </div>
                            </div>
                            
                            <div className="modern-card p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                              <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">Specialties</h4>
                              <div className="flex flex-wrap gap-2">
                                {hospital.specialties.map((specialty, index) => (
                                  <Badge key={index} variant="outline" className="modern-badge">
                                    {specialty}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                            
                            <div className="modern-card p-4 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                              <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">Top Doctors</h4>
                              <div className="space-y-2">
                                {hospital.topDoctors.length > 0 ? (
                                  hospital.topDoctors.map((doctor, index) => (
                                    <div key={index} className="flex items-center space-x-2 p-2 bg-white/50 dark:bg-black/20 rounded-lg">
                                      <Stethoscope className="h-4 w-4 text-yellow-600" />
                                      <span className="text-sm text-yellow-700 dark:text-yellow-300">{doctor}</span>
                                    </div>
                                  ))
                                ) : (
                                  <p className="text-sm text-yellow-700 dark:text-yellow-300">No top doctors listed</p>
                                )}
                              </div>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-4">
                    <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                      <div className="text-lg font-bold">{hospital.patients}</div>
                      <div className="text-xs opacity-70">Patients</div>
                    </div>
                    <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                      <div className="text-lg font-bold">{hospital.doctors}</div>
                      <div className="text-xs opacity-70">Doctors</div>
                    </div>
                    <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                      <div className={`text-lg font-bold ${getAdherenceColor(hospital.adherenceRate)}`}>{hospital.adherenceRate}%</div>
                      <div className="text-xs opacity-70">Adherence</div>
                    </div>
                    <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                      <div className="text-lg font-bold text-yellow-600">{hospital.rating}⭐</div>
                      <div className="text-xs opacity-70">Rating</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="opacity-70">Adherence Progress</span>
                      <span className="font-medium">{hospital.adherenceRate}%</span>
                    </div>
                    <Progress value={hospital.adherenceRate} className="h-2" />
                  </div>
                </Card>
              ))
            )}
          </div>
        )}

        {/* Doctors View */}
        {viewMode === 'doctors' && (
          <div className="grid gap-4 sm:gap-6">
            {filteredDoctors.length === 0 ? (
              <Card className="modern-card">
                <CardContent className="text-center py-12">
                  <div className="relative">
                    <Stethoscope className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                    <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                  </div>
                  <h3 className="text-lg font-semibold text-foreground mb-2">No doctors found</h3>
                  <p className="text-muted-foreground mb-6">
                    {searchTerm || filterAdherence !== 'all'
                      ? 'Try adjusting your search or filter criteria.'
                      : 'No doctors have been added to the network yet.'
                    }
                  </p>
                </CardContent>
              </Card>
            ) : (
              filteredDoctors.map((doctor) => (
                <Card key={doctor.id} className="modern-card p-6 hover:scale-[1.01] transition-all duration-300">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-16 w-16 ring-2 ring-white/50 shadow-lg">
                        <AvatarImage src={`/avatars/${doctor.avatar}.jpg`} />
                        <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold text-lg">
                          {doctor.avatar}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-bold text-xl">{doctor.name}</h3>
                          <Badge variant="outline" className={`modern-badge ${getAdherenceBadgeColor(doctor.adherenceRate)}`}>
                            {doctor.adherenceRate}% adherence
                          </Badge>
                        </div>
                        <p className="opacity-80 font-medium">{doctor.specialization}</p>
                        <p className="text-sm opacity-70">{doctor.hospital}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 mb-4">
                    <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                      <div className="text-lg font-bold">{doctor.patients}</div>
                      <div className="text-xs opacity-70">Patients</div>
                    </div>
                    <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                      <div className={`text-lg font-bold ${getAdherenceColor(doctor.adherenceRate)}`}>{doctor.adherenceRate}%</div>
                      <div className="text-xs opacity-70">Adherence</div>
                    </div>
                    <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                      <div className="text-lg font-bold">{doctor.hospital}</div>
                      <div className="text-xs opacity-70">Hospital</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span className="opacity-70">Adherence Progress</span>
                      <span className="font-medium">{doctor.adherenceRate}%</span>
                    </div>
                    <Progress value={doctor.adherenceRate} className="h-2" />
                  </div>
                </Card>
              ))
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
}