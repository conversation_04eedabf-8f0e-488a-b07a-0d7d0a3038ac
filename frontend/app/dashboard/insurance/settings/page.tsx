'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useAuthStore } from '@/lib/store';
import {
  Settings,
  Landmark,
  Bell,
  Shield,
  Eye,
  EyeOff,
  Save,
  Lock,
  LogOut,
  Trash2,
  Upload,
  Download,
  Building2,
  FileCheck,
  ClipboardCheck,
  Zap,
  DollarSign,
  Users,
  CreditCard,
  FileText,
  Mail,
  Phone,
  Globe,
  MapPin,
  Receipt,
  CheckCircle,
  AlertCircle,
  BarChart3
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function InsuranceSettingsPage() {
  const { user, logout } = useAuthStore();
  const [showPassword, setShowPassword] = useState(false);
  
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: '+****************',
    website: 'https://insuranceprovider.com',
    address: '123 Insurance Blvd, New York, NY 10001',
    description: 'Leading health insurance provider offering comprehensive coverage plans for individuals and organizations.',
    contactPerson: 'Jane Smith',
    contactTitle: 'Chief Operations Officer',
    taxId: '12-3456789',
    foundedYear: '1985',
    password: '••••••••',
    newPassword: '',
    confirmPassword: '',
    language: 'english',
    timezone: 'America/New_York',
    theme: 'light',
    emailNotifications: true,
    smsNotifications: true,
    pushNotifications: true,
    adherenceAlerts: true,
    hospitalAlerts: true,
    doctorAlerts: true,
    dataSharing: true,
    anonymousDataCollection: true,
    publicProfile: true
  });

  // Subscription data
  const [subscriptionData, setSubscriptionData] = useState({
    plan: 'professional',
    status: 'active',
    billingCycle: 'annual',
    nextBillingDate: new Date('2025-01-15'),
    amount: 9999.99,
    paymentMethod: {
      type: 'credit_card',
      last4: '4242',
      expiryDate: '05/25',
      brand: 'Visa'
    },
    autoRenew: true,
    invoices: [
      { id: 'INV-2024-001', date: new Date('2024-01-15'), amount: 9999.99, status: 'paid' },
      { id: 'INV-2023-002', date: new Date('2023-01-15'), amount: 8999.99, status: 'paid' },
      { id: 'INV-2022-003', date: new Date('2022-01-15'), amount: 8999.99, status: 'paid' }
    ],
    memberLimit: 10000,
    membersUsed: 4256,
    features: [
      'Up to 10,000 members',
      'Advanced adherence analytics',
      'Weekly adherence reports',
      'Provider performance metrics',
      'Priority support',
      'Full API access',
      'Monthly insights',
      'Custom data exports'
    ]
  });

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveProfile = () => {
    toast.success('Insurance provider profile saved successfully!');
  };

  const handleSaveNotifications = () => {
    toast.success('Notification preferences saved successfully!');
  };

  const handleSavePrivacy = () => {
    toast.success('Privacy settings saved successfully!');
  };

  const handleExportData = () => {
    toast.success('Your data export has been initiated. You will receive an email with download instructions shortly.');
  };

  const handleDeleteAccount = () => {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      toast.error('Account deletion initiated. You will receive a confirmation email.');
    }
  };

  const handleUpdatePaymentMethod = () => {
    toast.success('Payment method updated successfully!');
  };

  const handleCancelSubscription = () => {
    if (confirm('Are you sure you want to cancel your subscription? This will affect all members and providers associated with your organization.')) {
      toast.success('Your subscription has been canceled. You will continue to have access until the end of your current billing period.');
      setSubscriptionData(prev => ({ ...prev, status: 'canceled' }));
    }
  };

  const handleToggleAutoRenew = () => {
    setSubscriptionData(prev => ({ ...prev, autoRenew: !prev.autoRenew }));
    toast.success(`Auto-renewal has been ${subscriptionData.autoRenew ? 'disabled' : 'enabled'}.`);
  };

  const handleUpgradePlan = () => {
    toast.success('You will be redirected to the plan upgrade page.');
  };

  const getPlanBadgeColor = (plan: string) => {
    switch (plan) {
      case 'standard': return 'bg-cyan-100 text-cyan-800 border-cyan-200';
      case 'professional': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'enterprise': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'canceled': return 'bg-red-100 text-red-800 border-red-200';
      case 'past_due': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/insurance' },
            { label: 'Settings', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Settings className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Insurance Provider Settings</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Manage your organization profile and platform preferences
              </p>
            </div>
          </div>
          <Button className="btn-primary w-full sm:w-auto" onClick={handleSaveProfile}>
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Organization Profile */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Landmark className="h-5 w-5 text-white" />
                  </div>
                  <span>Organization Profile</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex flex-col sm:flex-row items-center gap-4 sm:gap-8">
                  <Avatar className="h-24 w-24 border-4 border-background">
                    <AvatarImage src="/avatars/IP.jpg" />
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white text-xl">
                      {user?.name?.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 space-y-2 text-center sm:text-left">
                    <h3 className="text-xl font-semibold">{user?.name}</h3>
                    <p className="text-muted-foreground">{user?.email}</p>
                    <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
                      <Button variant="outline" size="sm" className="text-xs">
                        <Upload className="h-3 w-3 mr-1" />
                        Change Logo
                      </Button>
                      <Button variant="outline" size="sm" className="text-xs text-destructive hover:text-destructive">
                        <Trash2 className="h-3 w-3 mr-1" />
                        Remove
                      </Button>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Insurance Provider Name</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={formData.website}
                      onChange={(e) => handleInputChange('website', e.target.value)}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    className="bg-muted/30 border-border/50"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Company Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className="bg-muted/30 border-border/50 min-h-[100px]"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contactPerson">Primary Contact Person</Label>
                    <Input
                      id="contactPerson"
                      value={formData.contactPerson}
                      onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="contactTitle">Contact Title</Label>
                    <Input
                      id="contactTitle"
                      value={formData.contactTitle}
                      onChange={(e) => handleInputChange('contactTitle', e.target.value)}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="taxId">Tax ID / EIN</Label>
                    <Input
                      id="taxId"
                      value={formData.taxId}
                      onChange={(e) => handleInputChange('taxId', e.target.value)}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="foundedYear">Year Founded</Label>
                    <Input
                      id="foundedYear"
                      value={formData.foundedYear}
                      onChange={(e) => handleInputChange('foundedYear', e.target.value)}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Subscription & Billing */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <CreditCard className="h-5 w-5 text-white" />
                  </div>
                  <span>Subscription & Billing</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Current Plan */}
                <div className="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-semibold text-blue-900 dark:text-blue-100">Current Plan</h3>
                        <Badge className={`modern-badge ${getPlanBadgeColor(subscriptionData.plan)} capitalize`}>
                          {subscriptionData.plan}
                        </Badge>
                        <Badge className={`modern-badge ${getStatusBadgeColor(subscriptionData.status)} capitalize`}>
                          {subscriptionData.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        {subscriptionData.billingCycle === 'monthly' ? 'Monthly' : 'Annual'} billing • 
                        Next payment on {subscriptionData.nextBillingDate.toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" className="bg-white/50 dark:bg-black/20" onClick={handleUpgradePlan}>
                        Upgrade Plan
                      </Button>
                      {subscriptionData.status === 'active' && (
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="text-red-600 border-red-200 hover:bg-red-50"
                          onClick={handleCancelSubscription}
                        >
                          Cancel
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
                    <div className="p-3 bg-white/50 dark:bg-black/20 rounded-lg">
                      <p className="text-xs text-blue-700 dark:text-blue-300">Amount</p>
                      <p className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                        ${subscriptionData.amount.toFixed(2)}/{subscriptionData.billingCycle === 'monthly' ? 'mo' : 'yr'}
                      </p>
                    </div>
                    <div className="p-3 bg-white/50 dark:bg-black/20 rounded-lg">
                      <p className="text-xs text-blue-700 dark:text-blue-300">Member Limit</p>
                      <p className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                        {subscriptionData.membersUsed.toLocaleString()}/{subscriptionData.memberLimit.toLocaleString()}
                      </p>
                    </div>
                    <div className="p-3 bg-white/50 dark:bg-black/20 rounded-lg">
                      <p className="text-xs text-blue-700 dark:text-blue-300">Auto-Renew</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Switch
                          checked={subscriptionData.autoRenew}
                          onCheckedChange={handleToggleAutoRenew}
                        />
                        <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                          {subscriptionData.autoRenew ? 'Enabled' : 'Disabled'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="text-sm text-blue-700 dark:text-blue-300">
                    {subscriptionData.status === 'active' ? (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Your subscription is active and will renew automatically.
                      </div>
                    ) : subscriptionData.status === 'canceled' ? (
                      <div className="flex items-center text-red-600">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        Your subscription has been canceled and will end on {subscriptionData.nextBillingDate.toLocaleDateString()}.
                      </div>
                    ) : (
                      <div className="flex items-center text-yellow-600">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        Your subscription requires attention.
                      </div>
                    )}
                  </div>
                </div>

                {/* Usage Stats */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Usage Statistics</h3>
                  <div className="p-4 border border-border rounded-lg bg-muted/30">
                    <div className="mb-4">
                      <div className="flex justify-between mb-1">
                        <span className="text-sm">Member Usage</span>
                        <span className="text-sm font-medium">{subscriptionData.membersUsed.toLocaleString()}/{subscriptionData.memberLimit.toLocaleString()}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2.5">
                        <div 
                          className="bg-blue-600 h-2.5 rounded-full" 
                          style={{ width: `${(subscriptionData.membersUsed / subscriptionData.memberLimit) * 100}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {(subscriptionData.memberLimit - subscriptionData.membersUsed).toLocaleString()} members remaining in your plan
                      </p>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                      <div className="p-3 bg-muted/50 rounded-lg text-center">
                        <p className="text-xs text-muted-foreground">Hospitals</p>
                        <p className="text-lg font-semibold">5</p>
                      </div>
                      <div className="p-3 bg-muted/50 rounded-lg text-center">
                        <p className="text-xs text-muted-foreground">API Calls</p>
                        <p className="text-lg font-semibold">156,789</p>
                      </div>
                      <div className="p-3 bg-muted/50 rounded-lg text-center">
                        <p className="text-xs text-muted-foreground">Storage Used</p>
                        <p className="text-lg font-semibold">45.8 GB</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Method */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Payment Method</h3>
                  <div className="p-4 border border-border rounded-lg bg-muted/30">
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 rounded-md">
                          <CreditCard className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">{subscriptionData.paymentMethod.brand} •••• {subscriptionData.paymentMethod.last4}</p>
                          <p className="text-sm text-muted-foreground">Expires {subscriptionData.paymentMethod.expiryDate}</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm" onClick={handleUpdatePaymentMethod}>
                        Update
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Billing History */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Billing History</h3>
                  <div className="border rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-border">
                      <thead className="bg-muted/50">
                        <tr>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Invoice
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Date
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Amount
                          </th>
                          <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                            Action
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-card divide-y divide-border">
                        {subscriptionData.invoices.map((invoice) => (
                          <tr key={invoice.id}>
                            <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">
                              {invoice.id}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">
                              {invoice.date.toLocaleDateString()}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm">
                              ${invoice.amount.toFixed(2)}
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm">
                              <Badge className={invoice.status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                                {invoice.status}
                              </Badge>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                              <Button variant="ghost" size="sm">
                                <Download className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Security Settings */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <div className="p-2 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg">
                    <Shield className="h-5 w-5 text-white" />
                  </div>
                  <span>Security</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <div className="relative">
                    <Input
                      id="currentPassword"
                      type={showPassword ? 'text' : 'password'}
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      className="bg-muted/30 border-border/50 pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">New Password</Label>
                    <Input
                      id="newPassword"
                      type={showPassword ? 'text' : 'password'}
                      value={formData.newPassword}
                      onChange={(e) => handleInputChange('newPassword', e.target.value)}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">Confirm New Password</Label>
                    <Input
                      id="confirmPassword"
                      type={showPassword ? 'text' : 'password'}
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>
                </div>

                <div className="pt-2">
                  <Button variant="outline" className="w-full sm:w-auto">
                    <Lock className="h-4 w-4 mr-2" />
                    Change Password
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Notification Settings */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Bell className="h-5 w-5 text-white" />
                  </div>
                  <span>Notification Preferences</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="emailNotifications">Email Notifications</Label>
                      <p className="text-xs text-muted-foreground">Receive updates and alerts via email</p>
                    </div>
                    <Switch
                      id="emailNotifications"
                      checked={formData.emailNotifications}
                      onCheckedChange={(checked) => handleInputChange('emailNotifications', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="smsNotifications">SMS Notifications</Label>
                      <p className="text-xs text-muted-foreground">Receive updates and alerts via text message</p>
                    </div>
                    <Switch
                      id="smsNotifications"
                      checked={formData.smsNotifications}
                      onCheckedChange={(checked) => handleInputChange('smsNotifications', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="pushNotifications">Push Notifications</Label>
                      <p className="text-xs text-muted-foreground">Receive updates and alerts on your device</p>
                    </div>
                    <Switch
                      id="pushNotifications"
                      checked={formData.pushNotifications}
                      onCheckedChange={(checked) => handleInputChange('pushNotifications', checked)}
                    />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="adherenceAlerts">Adherence Alerts</Label>
                      <p className="text-xs text-muted-foreground">Notifications about patient medication adherence</p>
                    </div>
                    <Switch
                      id="adherenceAlerts"
                      checked={formData.adherenceAlerts}
                      onCheckedChange={(checked) => handleInputChange('adherenceAlerts', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="hospitalAlerts">Hospital Updates</Label>
                      <p className="text-xs text-muted-foreground">Notifications about hospital network changes</p>
                    </div>
                    <Switch
                      id="hospitalAlerts"
                      checked={formData.hospitalAlerts}
                      onCheckedChange={(checked) => handleInputChange('hospitalAlerts', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="doctorAlerts">Doctor Updates</Label>
                      <p className="text-xs text-muted-foreground">Notifications about doctor network changes</p>
                    </div>
                    <Switch
                      id="doctorAlerts"
                      checked={formData.doctorAlerts}
                      onCheckedChange={(checked) => handleInputChange('doctorAlerts', checked)}
                    />
                  </div>
                </div>

                <div className="pt-2">
                  <Button variant="outline" className="w-full sm:w-auto" onClick={handleSaveNotifications}>
                    <Bell className="h-4 w-4 mr-2" />
                    Save Notification Preferences
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Plan Summary */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <DollarSign className="h-4 w-4 text-white" />
                  </div>
                  <span>Plan Summary</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <Badge className={`modern-badge ${getPlanBadgeColor(subscriptionData.plan)} capitalize`}>
                      {subscriptionData.plan} Plan
                    </Badge>
                    <Badge className={`modern-badge ${getStatusBadgeColor(subscriptionData.status)} capitalize`}>
                      {subscriptionData.status}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-blue-700 dark:text-blue-300">Price</span>
                      <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        ${subscriptionData.amount.toFixed(2)}/{subscriptionData.billingCycle === 'monthly' ? 'mo' : 'yr'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-blue-700 dark:text-blue-300">Billing Cycle</span>
                      <span className="text-sm font-medium text-blue-900 dark:text-blue-100 capitalize">
                        {subscriptionData.billingCycle}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-blue-700 dark:text-blue-300">Next Payment</span>
                      <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        {subscriptionData.nextBillingDate.toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-blue-700 dark:text-blue-300">Members</span>
                      <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        {subscriptionData.membersUsed.toLocaleString()}/{subscriptionData.memberLimit.toLocaleString()}
                      </span>
                    </div>
                  </div>
                  <div className="mt-4">
                    <Button variant="outline" className="w-full text-sm">
                      <Receipt className="h-4 w-4 mr-2" />
                      View Plan Details
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Plan Features</h4>
                  <ul className="space-y-2 text-sm">
                    {subscriptionData.features.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="pt-2 space-y-2">
                  <Button variant="outline" className="w-full text-sm" onClick={handleUpgradePlan}>
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Upgrade to Enterprise
                  </Button>
                  <Button variant="outline" className="w-full text-sm">
                    <CreditCard className="h-4 w-4 mr-2" />
                    Update Payment Method
                  </Button>
                  {subscriptionData.status === 'active' && (
                    <Button 
                      variant="outline" 
                      className="w-full text-sm text-red-600 border-red-200 hover:bg-red-50"
                      onClick={handleCancelSubscription}
                    >
                      <AlertCircle className="h-4 w-4 mr-2" />
                      Cancel Subscription
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Privacy Settings */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
                    <Shield className="h-4 w-4 text-white" />
                  </div>
                  <span>Privacy Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="dataSharing">Data Sharing with Network</Label>
                    <p className="text-xs text-muted-foreground">Share aggregated data with healthcare network</p>
                  </div>
                  <Switch
                    id="dataSharing"
                    checked={formData.dataSharing}
                    onCheckedChange={(checked) => handleInputChange('dataSharing', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="anonymousDataCollection">Anonymous Data Collection</Label>
                    <p className="text-xs text-muted-foreground">Help improve our service with anonymous usage data</p>
                  </div>
                  <Switch
                    id="anonymousDataCollection"
                    checked={formData.anonymousDataCollection}
                    onCheckedChange={(checked) => handleInputChange('anonymousDataCollection', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="publicProfile">Public Company Profile</Label>
                    <p className="text-xs text-muted-foreground">Make your company visible in public directory</p>
                  </div>
                  <Switch
                    id="publicProfile"
                    checked={formData.publicProfile}
                    onCheckedChange={(checked) => handleInputChange('publicProfile', checked)}
                  />
                </div>

                <Button variant="outline" className="w-full text-sm mt-2" onClick={handleSavePrivacy}>
                  <Shield className="h-4 w-4 mr-2" />
                  Save Privacy Settings
                </Button>
              </CardContent>
            </Card>

            {/* Preferences */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Preferences</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="theme">Theme</Label>
                  <Select value={formData.theme} onValueChange={(value) => handleInputChange('theme', value)}>
                    <SelectTrigger className="bg-muted/30 border-border/50">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                      <SelectItem value="system">System Default</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select value={formData.timezone} onValueChange={(value) => handleInputChange('timezone', value)}>
                    <SelectTrigger className="bg-muted/30 border-border/50">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="America/New_York">Eastern Time (ET)</SelectItem>
                      <SelectItem value="America/Chicago">Central Time (CT)</SelectItem>
                      <SelectItem value="America/Denver">Mountain Time (MT)</SelectItem>
                      <SelectItem value="America/Los_Angeles">Pacific Time (PT)</SelectItem>
                      <SelectItem value="Europe/London">London (GMT)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Account Actions */}
            <Card className="modern-card border-destructive/20">
              <CardHeader>
                <CardTitle className="text-base sm:text-lg text-destructive">Account Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element" onClick={handleExportData}>
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </Button>
                <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element" onClick={() => logout()}>
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </Button>
                <Button variant="destructive" className="w-full justify-start text-sm" onClick={handleDeleteAccount}>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Account
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}