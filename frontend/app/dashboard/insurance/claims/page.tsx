'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  FileCheck, 
  Search, 
  Filter, 
  Plus, 
  Eye, 
  Download,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  MoreHorizontal,
  DollarSign,
  FileText,
  Pill,
  Activity,
  Building2,
  Stethoscope,
  User,
  Landmark
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { format } from 'date-fns';
import { mockClaims } from '@/lib/mock-data';
import { Claim } from '@/types';

export default function ClaimsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [selectedClaim, setSelectedClaim] = useState<Claim | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'pending': return 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100';
      case 'rejected': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      case 'review': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="h-3 w-3 mr-1" /> Approved</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><Clock className="h-3 w-3 mr-1" /> Pending</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800 border-red-200"><XCircle className="h-3 w-3 mr-1" /> Rejected</Badge>;
      case 'review':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200"><AlertCircle className="h-3 w-3 mr-1" /> Under Review</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'medication': return <Pill className="h-4 w-4 text-blue-600" />;
      case 'procedure': return <Activity className="h-4 w-4 text-purple-600" />;
      case 'consultation': return <Stethoscope className="h-4 w-4 text-green-600" />;
      case 'hospitalization': return <Building2 className="h-4 w-4 text-red-600" />;
      default: return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  // Filter and sort claims
  const filteredClaims = mockClaims
    .filter(claim => {
      const matchesSearch = claim.claimNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          claim.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || claim.status === filterStatus;
      const matchesType = filterType === 'all' || claim.type === filterType;
      
      return matchesSearch && matchesStatus && matchesType;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return b.submissionDate.getTime() - a.submissionDate.getTime();
        case 'amount':
          return b.amount - a.amount;
        case 'claimNumber':
          return a.claimNumber.localeCompare(b.claimNumber);
        default:
          return 0;
      }
    });

  const getStatusCounts = () => {
    return {
      total: mockClaims.length,
      pending: mockClaims.filter(c => c.status === 'pending').length,
      approved: mockClaims.filter(c => c.status === 'approved').length,
      rejected: mockClaims.filter(c => c.status === 'rejected').length,
      review: mockClaims.filter(c => c.status === 'review').length,
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/insurance' },
            { label: 'Claims', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <FileCheck className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Claims Management</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Process and manage insurance claims
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Download className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Export Claims</span>
              <span className="sm:hidden">Export</span>
            </Button>
            <Link href="/dashboard/insurance/claims/new">
              <Button className="btn-primary w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Process New Claim</span>
                <span className="sm:hidden">New Claim</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-5 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <FileCheck className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{statusCounts.total}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Claims</p>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-yellow-600">{statusCounts.pending}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Pending</p>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <AlertCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-blue-600">{statusCounts.review}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">In Review</p>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{statusCounts.approved}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Approved</p>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl shadow-lg group-hover:shadow-red-500/25 transition-all duration-300">
                <XCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-red-600">{statusCounts.rejected}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Rejected</p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="modern-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by claim number or description..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-muted/30 border-border/50 focus:bg-background transition-colors"
                  />
                </div>
              </div>
              
              {/* Filter by Status */}
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="review">In Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Filter by Type */}
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="medication">Medication</SelectItem>
                  <SelectItem value="procedure">Procedure</SelectItem>
                  <SelectItem value="consultation">Consultation</SelectItem>
                  <SelectItem value="hospitalization">Hospitalization</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Submission Date</SelectItem>
                  <SelectItem value="amount">Amount</SelectItem>
                  <SelectItem value="claimNumber">Claim Number</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Claims List */}
        <div className="grid gap-4 sm:gap-6">
          {filteredClaims.length === 0 ? (
            <Card className="modern-card">
              <CardContent className="text-center py-12">
                <div className="relative">
                  <FileCheck className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                  <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No claims found</h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm || filterStatus !== 'all' || filterType !== 'all'
                    ? 'Try adjusting your search or filter criteria.'
                    : 'No claims have been submitted yet.'
                  }
                </p>
                <Link href="/dashboard/insurance/claims/new">
                  <Button className="btn-primary">
                    <Plus className="h-4 w-4 mr-2" />
                    Process New Claim
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            filteredClaims.map((claim) => (
              <Card key={claim.id} className={`modern-card p-6 bg-gradient-to-r ${getStatusColor(claim.status)} border hover:scale-[1.01] transition-all duration-300`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                      {getTypeIcon(claim.type)}
                    </div>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-bold text-xl">Claim #{claim.claimNumber}</h3>
                        {getStatusBadge(claim.status)}
                      </div>
                      <p className="opacity-80 font-medium">{claim.description}</p>
                      <p className="text-sm opacity-70">
                        Submitted on {format(claim.submissionDate, 'MMM d, yyyy')}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-10 w-10 p-0 hover:bg-white/20"
                          onClick={() => setSelectedClaim(claim)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="modern-card max-w-3xl">
                        <DialogHeader>
                          <DialogTitle className="flex items-center space-x-3">
                            <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                              <FileCheck className="h-5 w-5 text-white" />
                            </div>
                            <span>Claim #{claim.claimNumber}</span>
                          </DialogTitle>
                          <DialogDescription>
                            Complete claim details and processing information
                          </DialogDescription>
                        </DialogHeader>
                        {selectedClaim && (
                          <div className="space-y-6">
                            <div className="grid grid-cols-2 gap-4">
                              <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                                <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Claim Information</h4>
                                <div className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                                  <p><strong>Claim #:</strong> {selectedClaim.claimNumber}</p>
                                  <p><strong>Type:</strong> <span className="capitalize">{selectedClaim.type}</span></p>
                                  <p><strong>Submitted:</strong> {format(selectedClaim.submissionDate, 'MMM d, yyyy')}</p>
                                  <p><strong>Service Date:</strong> {format(selectedClaim.serviceDate, 'MMM d, yyyy')}</p>
                                </div>
                              </div>
                              <div className="modern-card p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                                <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Financial Details</h4>
                                <div className="space-y-1 text-sm text-green-700 dark:text-green-300">
                                  <p><strong>Amount Claimed:</strong> ${selectedClaim.amount.toFixed(2)}</p>
                                  {selectedClaim.approvedAmount && (
                                    <p><strong>Amount Approved:</strong> ${selectedClaim.approvedAmount.toFixed(2)}</p>
                                  )}
                                  {selectedClaim.approvalDate && (
                                    <p><strong>Approval Date:</strong> {format(selectedClaim.approvalDate, 'MMM d, yyyy')}</p>
                                  )}
                                </div>
                              </div>
                            </div>
                            
                            <div className="modern-card p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                              <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">Description</h4>
                              <p className="text-sm text-purple-700 dark:text-purple-300">{selectedClaim.description}</p>
                            </div>
                            
                            <div className="modern-card p-4 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                              <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">Documents</h4>
                              <div className="space-y-2">
                                {selectedClaim.documents.map((doc, index) => (
                                  <div key={index} className="flex items-center justify-between p-2 bg-white/50 dark:bg-black/20 rounded-lg">
                                    <div className="flex items-center">
                                      <FileText className="h-4 w-4 mr-2 text-yellow-600" />
                                      <span className="text-sm text-yellow-700 dark:text-yellow-300">{doc}</span>
                                    </div>
                                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                      <Download className="h-3 w-3 text-yellow-600" />
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            </div>
                            
                            {selectedClaim.status === 'rejected' && selectedClaim.rejectionReason && (
                              <div className="modern-card p-4 bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-800">
                                <h4 className="font-semibold text-red-900 dark:text-red-100 mb-2">Rejection Reason</h4>
                                <p className="text-sm text-red-700 dark:text-red-300">{selectedClaim.rejectionReason}</p>
                              </div>
                            )}
                            
                            {selectedClaim.notes && (
                              <div className="modern-card p-4 bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20 border border-gray-200 dark:border-gray-800">
                                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">Notes</h4>
                                <p className="text-sm text-gray-700 dark:text-gray-300">{selectedClaim.notes}</p>
                              </div>
                            )}
                            
                            {selectedClaim.status === 'pending' && (
                              <div className="flex justify-end space-x-3">
                                <Button variant="outline">
                                  <XCircle className="h-4 w-4 mr-2" />
                                  Reject
                                </Button>
                                <Button className="btn-primary">
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Approve
                                </Button>
                              </div>
                            )}
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold">${claim.amount.toFixed(2)}</div>
                    <div className="text-xs opacity-70">Amount</div>
                  </div>
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold capitalize">{claim.type}</div>
                    <div className="text-xs opacity-70">Type</div>
                  </div>
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold">{format(claim.serviceDate, 'MMM d, yyyy')}</div>
                    <div className="text-xs opacity-70">Service Date</div>
                  </div>
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold">{claim.documents.length}</div>
                    <div className="text-xs opacity-70">Documents</div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-sm opacity-80">
                    <Calendar className="h-4 w-4" />
                    <span>Submitted {format(claim.submissionDate, 'MMM d, yyyy')}</span>
                  </div>
                  <div className="flex space-x-2">
                    {claim.status === 'pending' && (
                      <>
                        <Button variant="outline" size="sm" className="hover:bg-white/20">
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-white/20">
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                      </>
                    )}
                    {claim.status === 'review' && (
                      <Button variant="outline" size="sm" className="hover:bg-white/20">
                        <Eye className="h-4 w-4 mr-1" />
                        Review
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}