'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { MedicineCard } from '@/components/medicine/medicine-card';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useMedicineStore, useAppStore } from '@/lib/store';
import { usePatientData } from '@/hooks/use-patient-data';
import { 
  Search, 
  Filter, 
  Plus, 
  Pill, 
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  SortAsc,
  Sparkles,
  TrendingUp,
  Target,
  Activity
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { format, differenceInDays } from 'date-fns';

export default function MedicinesPage() {
  const { addNotification } = useAppStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('name');

  // Use API-integrated patient data
  const {
    medicines,
    isLoading,
    hasError,
    error,
    refreshData
  } = usePatientData();

  // Use medicines from API
  const allMedicines = medicines;

  const handleTakeMedicine = async (medicineId: string) => {
    try {
      // TODO: Implement API call to mark medicine as taken
      // await medicinesService.markMedicineAsTaken(medicineId);
      addNotification({
        type: 'success',
        title: 'Medicine Taken',
        message: 'Great job! Keep up the good work.',
        read: false,
      });
      // Refresh data to get updated status
      refreshData();
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to mark medicine as taken. Please try again.',
        read: false,
      });
    }
  };

  const handleSnooze = (medicineId: string) => {
    addNotification({
      type: 'info',
      title: 'Reminder Snoozed',
      message: 'We\'ll remind you again in 15 minutes.',
      read: false,
    });
  };

  // Show loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-6 space-y-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2 text-muted-foreground">Loading medicines...</span>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Show error state
  if (hasError) {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-6 space-y-6">
          <Card className="modern-card">
            <CardContent className="text-center py-12">
              <AlertTriangle className="mx-auto h-16 w-16 text-destructive mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">Error Loading Medicines</h3>
              <p className="text-muted-foreground mb-6">{error}</p>
              <Button onClick={refreshData} className="btn-primary">
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Filter and sort medicines
  const filteredMedicines = allMedicines
    .filter(medicine => {
      const matchesSearch = medicine.name.toLowerCase().includes(searchTerm.toLowerCase());
      const daysRemaining = differenceInDays(medicine.endDate, new Date());
      
      if (filterStatus === 'active') return matchesSearch && daysRemaining > 0;
      if (filterStatus === 'ending-soon') return matchesSearch && daysRemaining <= 7 && daysRemaining > 0;
      if (filterStatus === 'expired') return matchesSearch && daysRemaining <= 0;
      
      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'start-date':
          return b.startDate.getTime() - a.startDate.getTime();
        case 'end-date':
          return a.endDate.getTime() - b.endDate.getTime();
        default:
          return 0;
      }
    });

  const getStatusCounts = () => {
    const now = new Date();
    return {
      total: allMedicines.length,
      active: allMedicines.filter(m => differenceInDays(m.endDate, now) > 0).length,
      endingSoon: allMedicines.filter(m => {
        const days = differenceInDays(m.endDate, now);
        return days <= 7 && days > 0;
      }).length,
      expired: allMedicines.filter(m => differenceInDays(m.endDate, now) <= 0).length,
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/patient' },
            { label: 'My Medicines', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Pill className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">My Medicines</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Manage your current medications and track your progress
              </p>
            </div>
          </div>
          <Link href="/dashboard/patient/prescriptions">
            <Button className="btn-primary w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Add New Medicine</span>
              <span className="sm:hidden">Add Medicine</span>
            </Button>
          </Link>
        </div>

        {/* Status Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Pill className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{statusCounts.total}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Medicines</p>
                <div className="flex items-center mt-1">
                  <Activity className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">All medications</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{statusCounts.active}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">Currently taking</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-yellow-600">{statusCounts.endingSoon}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Ending Soon</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-yellow-500 mr-1" />
                  <span className="text-xs text-yellow-600 font-medium">Within 7 days</span>
                </div>
              </div>
            </div>
          </div>
          
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl shadow-lg group-hover:shadow-red-500/25 transition-all duration-300">
                <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-red-600">{statusCounts.expired}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Expired</p>
                <div className="flex items-center mt-1">
                  <AlertTriangle className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-xs text-red-600 font-medium">Need renewal</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="modern-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search medicines..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-muted/30 border-border/50 focus:bg-background transition-colors"
                  />
                </div>
              </div>
              
              {/* Filter by Status */}
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Medicines</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="ending-soon">Ending Soon</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SortAsc className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="start-date">Start Date</SelectItem>
                  <SelectItem value="end-date">End Date</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Medicines List */}
        <div className="space-y-4 sm:space-y-6">
          {filteredMedicines.length === 0 ? (
            <Card className="modern-card">
              <CardContent className="text-center py-12">
                <div className="relative">
                  <Pill className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                  <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No medicines found</h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm || filterStatus !== 'all' 
                    ? 'Try adjusting your search or filter criteria.'
                    : 'Upload your first prescription to get started.'
                  }
                </p>
                <Link href="/dashboard/patient/prescriptions">
                  <Button className="btn-primary">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Medicine
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 sm:gap-6">
              {filteredMedicines.map((medicine) => (
                <div key={medicine.id} className="modern-card">
                  <MedicineCard
                    medicine={medicine}
                    nextDose={new Date(Date.now() + Math.random() * 4 * 60 * 60 * 1000)}
                    adherenceRate={Math.floor(Math.random() * 20) + 80}
                    onTakeMedicine={handleTakeMedicine}
                    onSnooze={handleSnooze}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}