'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { AdherenceChart } from '@/components/charts/adherence-chart';
import { StatsCard } from '@/components/ui/stats-card';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useGamificationStore } from '@/lib/store';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Target, 
  Award,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle,
  Download,
  Share,
  BarChart3,
  LineChart,
  PieChart,
  <PERSON>rk<PERSON>,
  Zap,
  Star,
  Trophy
} from 'lucide-react';
import { useState } from 'react';
import { format, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';

export default function ProgressPage() {
  const { stats } = useGamificationStore();
  const [timeRange, setTimeRange] = useState('week');
  const [chartType, setChartType] = useState('line');

  // Mock progress data
  const weeklyData = [
    { date: 'Mon', adherence: 95, target: 100, doses: 4 },
    { date: 'Tue', adherence: 100, target: 100, doses: 4 },
    { date: 'Wed', adherence: 85, target: 100, doses: 4 },
    { date: 'Thu', adherence: 100, target: 100, doses: 4 },
    { date: 'Fri', adherence: 90, target: 100, doses: 4 },
    { date: 'Sat', adherence: 100, target: 100, doses: 4 },
    { date: 'Sun', adherence: 95, target: 100, doses: 4 },
  ];

  const monthlyData = [
    { date: 'Week 1', adherence: 92, target: 100, doses: 28 },
    { date: 'Week 2', adherence: 88, target: 100, doses: 28 },
    { date: 'Week 3', adherence: 95, target: 100, doses: 28 },
    { date: 'Week 4', adherence: 90, target: 100, doses: 28 },
  ];

  const medicineProgress = [
    { name: 'Metformin', adherence: 95, streak: 12, totalDoses: 60, takenDoses: 57 },
    { name: 'Lisinopril', adherence: 88, streak: 8, totalDoses: 30, takenDoses: 26 },
    { name: 'Omeprazole', adherence: 100, streak: 14, totalDoses: 14, takenDoses: 14 },
  ];

  const insights = [
    {
      type: 'positive',
      title: 'Great Consistency!',
      message: 'You\'ve maintained above 90% adherence for 2 weeks straight.',
      icon: CheckCircle,
      color: 'from-green-50 to-emerald-50 border-green-200 text-green-900',
      darkColor: 'dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100'
    },
    {
      type: 'warning',
      title: 'Morning Doses',
      message: 'You tend to miss morning doses more often. Consider setting earlier reminders.',
      icon: AlertTriangle,
      color: 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900',
      darkColor: 'dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100'
    },
    {
      type: 'info',
      title: 'Weekend Pattern',
      message: 'Your adherence is slightly lower on weekends. Plan ahead for better consistency.',
      icon: Calendar,
      color: 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900',
      darkColor: 'dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100'
    }
  ];

  const currentData = timeRange === 'week' ? weeklyData : monthlyData;
  const averageAdherence = Math.round(currentData.reduce((sum, day) => sum + day.adherence, 0) / currentData.length);
  const totalDoses = currentData.reduce((sum, day) => sum + day.doses, 0);
  const takenDoses = Math.round(totalDoses * (averageAdherence / 100));

  const getChartIcon = (type: string) => {
    switch (type) {
      case 'line': return LineChart;
      case 'bar': return BarChart3;
      case 'pie': return PieChart;
      default: return LineChart;
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/patient' },
            { label: 'Progress', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Progress Tracking</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Monitor your medication adherence and health improvements
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Share className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Share Progress</span>
              <span className="sm:hidden">Share</span>
            </Button>
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Download className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Export Report</span>
              <span className="sm:hidden">Export</span>
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                <Award className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{stats.currentStreak}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Current Streak</p>
                <div className="flex items-center mt-1">
                  <Trophy className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600 font-medium">Best: {stats.longestStreak} days</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <Target className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{averageAdherence}%</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Adherence Rate</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">+3% this week</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-blue-600">{takenDoses}/{totalDoses}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Doses Taken</p>
                <div className="flex items-center mt-1">
                  <Activity className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">{Math.round((takenDoses/totalDoses)*100)}% complete</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Zap className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">{stats.totalPoints}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Points</p>
                <div className="flex items-center mt-1">
                  <Star className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">+50 this week</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Charts */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Adherence Chart */}
            <Card className="modern-card">
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                      <TrendingUp className="h-5 w-5 text-white" />
                    </div>
                    <span>Adherence Trends</span>
                  </CardTitle>
                  <div className="flex space-x-2">
                    <Select value={timeRange} onValueChange={setTimeRange}>
                      <SelectTrigger className="w-32 bg-muted/30 border-border/50">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="week">This Week</SelectItem>
                        <SelectItem value="month">This Month</SelectItem>
                        <SelectItem value="quarter">This Quarter</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={chartType} onValueChange={setChartType}>
                      <SelectTrigger className="w-24 bg-muted/30 border-border/50">
                        {(() => {
                          const Icon = getChartIcon(chartType);
                          return <Icon className="h-4 w-4" />;
                        })()}
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="line">
                          <div className="flex items-center">
                            <LineChart className="h-4 w-4 mr-2" />
                            Line
                          </div>
                        </SelectItem>
                        <SelectItem value="bar">
                          <div className="flex items-center">
                            <BarChart3 className="h-4 w-4 mr-2" />
                            Bar
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <AdherenceChart 
                  data={currentData} 
                  type={chartType as 'line' | 'bar'}
                  title=""
                />
              </CardContent>
            </Card>

            {/* Medicine-wise Progress */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Activity className="h-5 w-5 text-white" />
                  </div>
                  <span>Medicine-wise Progress</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {medicineProgress.map((medicine, index) => (
                    <div key={index} className="modern-card p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h3 className="font-semibold text-blue-900 dark:text-blue-100">{medicine.name}</h3>
                          <p className="text-sm text-blue-700 dark:text-blue-300">
                            {medicine.takenDoses}/{medicine.totalDoses} doses taken
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge variant="outline" className="text-xs modern-badge bg-gradient-to-r from-orange-50 to-red-50 text-orange-700 border-orange-200">
                            {medicine.streak} day streak
                          </Badge>
                          <Badge variant={medicine.adherence >= 90 ? 'default' : 'destructive'} className="modern-badge">
                            {medicine.adherence}%
                          </Badge>
                        </div>
                      </div>
                      <Progress value={medicine.adherence} className="h-3" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Weekly Summary */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Calendar className="h-4 w-4 text-white" />
                  </div>
                  <span>Weekly Summary</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center modern-card p-3 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                    <div className="text-2xl font-bold text-green-600">6</div>
                    <div className="text-xs text-green-700 dark:text-green-300">Perfect Days</div>
                  </div>
                  <div className="text-center modern-card p-3 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                    <div className="text-2xl font-bold text-yellow-600">1</div>
                    <div className="text-xs text-yellow-700 dark:text-yellow-300">Missed Doses</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Best Day</span>
                    <span className="font-medium">Tuesday (100%)</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Improvement</span>
                    <span className="font-medium text-green-600">+3% vs last week</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Insights */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
                    <Sparkles className="h-4 w-4 text-white" />
                  </div>
                  <span>Insights</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {insights.map((insight, index) => (
                  <div key={index} className={`modern-card p-3 bg-gradient-to-r ${insight.color} ${insight.darkColor} border`}>
                    <div className="flex items-start space-x-2">
                      <insight.icon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm">{insight.title}</p>
                        <p className="text-xs mt-1">{insight.message}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Goals */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg">
                    <Target className="h-4 w-4 text-white" />
                  </div>
                  <span>Goals</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Monthly Adherence</span>
                      <span className="font-medium">92/95%</span>
                    </div>
                    <Progress value={92} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Streak Goal</span>
                      <span className="font-medium">{stats.currentStreak}/30 days</span>
                    </div>
                    <Progress value={(stats.currentStreak / 30) * 100} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Points Goal</span>
                      <span className="font-medium">{stats.totalPoints}/1000 pts</span>
                    </div>
                    <Progress value={(stats.totalPoints / 1000) * 100} className="h-2" />
                  </div>
                </div>
                
                <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                  <Target className="h-4 w-4 mr-2" />
                  Set New Goals
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}