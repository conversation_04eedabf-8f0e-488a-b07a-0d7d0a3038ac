'use client';

import { useState } from 'react';
import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useWebSocketContext } from '@/components/providers/websocket-provider';
import { useWebSocketNotifications, useWebSocketReminders, useWebSocketAdherence, useWebSocketAchievements } from '@/hooks/use-websocket';
import { NotificationFeed } from '@/components/realtime/notification-feed';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { 
  Wifi, 
  WifiOff, 
  Send, 
  Bell, 
  Clock, 
  TrendingUp, 
  Trophy, 
  RefreshCw,
  TestTube,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';

export default function RealTimeTestPage() {
  const { 
    isConnected, 
    isConnecting, 
    error, 
    connect, 
    disconnect, 
    ping, 
    sendNotification 
  } = useWebSocketContext();

  const { notifications } = useWebSocketNotifications();
  const { reminders } = useWebSocketReminders();
  const { adherenceUpdates } = useWebSocketAdherence();
  const { achievements } = useWebSocketAchievements();

  const [testNotification, setTestNotification] = useState({
    title: 'Test Notification',
    message: 'This is a test notification from the WebSocket system',
    type: 'info'
  });

  const handleSendTestNotification = () => {
    if (!isConnected) {
      toast.error('WebSocket not connected');
      return;
    }

    // This would typically be sent from the backend, but for testing we can simulate
    toast.info('Test notification sent', {
      description: 'In a real scenario, this would be sent from the backend'
    });
  };

  const handlePing = () => {
    ping();
    toast.success('Ping sent to server');
  };

  const handleConnect = async () => {
    try {
      await connect();
      toast.success('Connected to WebSocket');
    } catch (err) {
      toast.error('Failed to connect');
    }
  };

  const handleDisconnect = () => {
    disconnect();
    toast.info('Disconnected from WebSocket');
  };

  const breadcrumbItems = [
    { label: 'Dashboard', href: '/dashboard/patient' },
    { label: 'Real-time Test', href: '/dashboard/patient/realtime-test' },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Breadcrumb items={breadcrumbItems} />
            <h1 className="text-2xl sm:text-3xl font-bold text-foreground mt-2">
              Real-time Features Test
            </h1>
            <p className="text-muted-foreground mt-1">
              Test and monitor WebSocket connectivity and real-time features
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Connection Status & Controls */}
          <div className="space-y-6">
            {/* Connection Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  {isConnected ? (
                    <Wifi className="h-5 w-5 text-green-500" />
                  ) : (
                    <WifiOff className="h-5 w-5 text-red-500" />
                  )}
                  <span>WebSocket Connection</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status:</span>
                  <Badge 
                    variant={isConnected ? "default" : error ? "destructive" : "secondary"}
                    className="flex items-center space-x-1"
                  >
                    {isConnecting && <RefreshCw className="h-3 w-3 animate-spin" />}
                    <span>
                      {isConnecting ? 'Connecting...' : 
                       isConnected ? 'Connected' : 
                       error ? 'Error' : 'Disconnected'}
                    </span>
                  </Badge>
                </div>

                {error && (
                  <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
                  </div>
                )}

                <div className="flex space-x-2">
                  {!isConnected ? (
                    <Button onClick={handleConnect} disabled={isConnecting}>
                      <Wifi className="h-4 w-4 mr-2" />
                      Connect
                    </Button>
                  ) : (
                    <Button variant="outline" onClick={handleDisconnect}>
                      <WifiOff className="h-4 w-4 mr-2" />
                      Disconnect
                    </Button>
                  )}
                  
                  <Button 
                    variant="outline" 
                    onClick={handlePing}
                    disabled={!isConnected}
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Ping
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Test Controls */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TestTube className="h-5 w-5" />
                  <span>Test Controls</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Notification Title</Label>
                  <Input
                    id="title"
                    value={testNotification.title}
                    onChange={(e) => setTestNotification(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter notification title"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Notification Message</Label>
                  <Textarea
                    id="message"
                    value={testNotification.message}
                    onChange={(e) => setTestNotification(prev => ({ ...prev, message: e.target.value }))}
                    placeholder="Enter notification message"
                    rows={3}
                  />
                </div>

                <Button 
                  onClick={handleSendTestNotification}
                  disabled={!isConnected}
                  className="w-full"
                >
                  <Send className="h-4 w-4 mr-2" />
                  Send Test Notification
                </Button>

                <div className="text-xs text-muted-foreground">
                  Note: In production, notifications are sent from the backend when events occur (medication reminders, adherence updates, achievements, etc.)
                </div>
              </CardContent>
            </Card>

            {/* Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>Real-time Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full mx-auto mb-2">
                      <Bell className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <p className="text-2xl font-bold">{notifications.length}</p>
                    <p className="text-xs text-muted-foreground">Notifications</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="flex items-center justify-center w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full mx-auto mb-2">
                      <Clock className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                    </div>
                    <p className="text-2xl font-bold">{reminders.length}</p>
                    <p className="text-xs text-muted-foreground">Reminders</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full mx-auto mb-2">
                      <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                    <p className="text-2xl font-bold">{adherenceUpdates.length}</p>
                    <p className="text-xs text-muted-foreground">Adherence</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full mx-auto mb-2">
                      <Trophy className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <p className="text-2xl font-bold">{achievements.length}</p>
                    <p className="text-xs text-muted-foreground">Achievements</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Real-time Feed */}
          <div>
            <NotificationFeed />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
