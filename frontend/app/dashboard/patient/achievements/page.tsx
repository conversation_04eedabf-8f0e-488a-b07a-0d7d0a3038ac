'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { AchievementBadge } from '@/components/gamification/achievement-badge';
import { StatsCard } from '@/components/ui/stats-card';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useGamificationStore } from '@/lib/store';
import { mockAchievements } from '@/lib/mock-data';
import { Trophy, Star, Target, TrendingUp, Flame, Crown, Sparkles, Award, Zap } from 'lucide-react';

export default function AchievementsPage() {
  const { stats } = useGamificationStore();

  // Mock user achievements
  const userAchievements = [
    {
      id: '1',
      userId: '1',
      achievementId: '1',
      unlockedAt: new Date('2024-01-10'),
      achievement: mockAchievements[0],
    },
    {
      id: '2',
      userId: '1',
      achievementId: '2',
      unlockedAt: new Date('2024-01-15'),
      achievement: mockAchievements[1],
    },
  ];

  // Calculate progress for locked achievements
  const getAchievementProgress = (achievement: typeof mockAchievements[0]) => {
    switch (achievement.category) {
      case 'streak':
        return stats.currentStreak;
      case 'consistency':
        return stats.completionRate;
      default:
        return 0;
    }
  };

  const unlockedCount = userAchievements.length;
  const totalCount = mockAchievements.length;
  const completionRate = Math.round((unlockedCount / totalCount) * 100);

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/patient' },
            { label: 'Achievements', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Trophy className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Achievements</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Track your progress and unlock rewards
              </p>
            </div>
          </div>
          <Badge variant="outline" className="text-lg px-6 py-3 modern-badge bg-gradient-to-r from-yellow-50 to-orange-50 text-yellow-700 border-yellow-200">
            <Trophy className="h-5 w-5 mr-2" />
            {stats.totalPoints} Points
          </Badge>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                <Trophy className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{unlockedCount}/{totalCount}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Achievements Unlocked</p>
                <div className="flex items-center mt-1">
                  <Award className="h-3 w-3 text-yellow-500 mr-1" />
                  <span className="text-xs text-yellow-600 font-medium">{completionRate}% complete</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                <Flame className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{stats.currentStreak}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Current Streak</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600 font-medium">Best: {stats.longestStreak} days</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Star className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{stats.totalPoints}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Points</p>
                <div className="flex items-center mt-1">
                  <Zap className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">+50 this week</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <Target className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{stats.completionRate}%</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Completion Rate</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">+3% this week</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Achievement Progress */}
        <Card className="modern-card">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <div className="p-2 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-lg">
                <Crown className="h-5 w-5 text-white" />
              </div>
              <span>Achievement Progress</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Overall Progress</span>
                <span className="font-medium">{unlockedCount}/{totalCount} unlocked</span>
              </div>
              <div className="relative">
                <Progress value={completionRate} className="h-4" />
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-full" />
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
                <div className="p-4 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-xl border border-yellow-200 dark:border-yellow-800">
                  <div className="text-2xl font-bold text-yellow-600">{unlockedCount}</div>
                  <div className="text-xs text-yellow-700 dark:text-yellow-300">Unlocked</div>
                </div>
                <div className="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                  <div className="text-2xl font-bold text-blue-600">{totalCount - unlockedCount}</div>
                  <div className="text-xs text-blue-700 dark:text-blue-300">Remaining</div>
                </div>
                <div className="p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl border border-purple-200 dark:border-purple-800">
                  <div className="text-2xl font-bold text-purple-600">{stats.totalPoints}</div>
                  <div className="text-xs text-purple-700 dark:text-purple-300">Points</div>
                </div>
                <div className="p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-200 dark:border-green-800">
                  <div className="text-2xl font-bold text-green-600">{completionRate}%</div>
                  <div className="text-xs text-green-700 dark:text-green-300">Complete</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Achievements Grid */}
        <div>
          <div className="flex items-center space-x-3 mb-6">
            <div className="h-8 w-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Sparkles className="h-4 w-4 text-white" />
            </div>
            <h2 className="text-2xl font-semibold text-foreground">All Achievements</h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockAchievements.map((achievement) => {
              const userAchievement = userAchievements.find(
                ua => ua.achievementId === achievement.id
              );
              const progress = getAchievementProgress(achievement);
              
              return (
                <div key={achievement.id} className="modern-card hover:scale-105 transition-all duration-300">
                  <AchievementBadge
                    achievement={achievement}
                    userAchievement={userAchievement}
                    progress={progress}
                  />
                </div>
              );
            })}
          </div>
        </div>

        {/* Recent Achievements */}
        {userAchievements.length > 0 && (
          <Card className="modern-card">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-white" />
                </div>
                <span>Recent Achievements</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {userAchievements
                  .sort((a, b) => b.unlockedAt.getTime() - a.unlockedAt.getTime())
                  .slice(0, 5)
                  .map((userAchievement) => (
                    <div key={userAchievement.id} className="flex items-center space-x-4 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-xl border border-yellow-200 dark:border-yellow-800 hover:shadow-lg transition-all duration-300">
                      <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl shadow-lg">
                        <Trophy className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-yellow-900 dark:text-yellow-100">{userAchievement.achievement.name}</h3>
                        <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                          {userAchievement.achievement.description}
                        </p>
                        <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                          Unlocked {userAchievement.unlockedAt.toLocaleDateString()}
                        </p>
                      </div>
                      <Badge variant="default" className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0">
                        +{userAchievement.achievement.points} pts
                      </Badge>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}