@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 221 83% 53%;
    --primary-foreground: 0 0% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222 84% 5%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    --accent: 210 40% 96%;
    --accent-foreground: 222 84% 5%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 221 83% 53%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.75rem;
  }
  
  .dark {
    --background: 222 84% 5%;
    --foreground: 210 40% 98%;
    --card: 222 84% 5%;
    --card-foreground: 210 40% 98%;
    --popover: 222 84% 5%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 92% 76%;
    --primary-foreground: 222 84% 5%;
    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;
    --accent: 217 33% 17%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --border: 217 33% 17%;
    --input: 217 33% 17%;
    --ring: 217 92% 76%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Modern glassmorphism effects */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-card {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced gradient backgrounds */
.gradient-mesh {
  background: 
    radial-gradient(at 40% 20%, hsla(228, 100%, 74%, 0.3) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 0.3) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(355, 100%, 93%, 0.3) 0px, transparent 50%),
    radial-gradient(at 80% 50%, hsla(340, 100%, 76%, 0.3) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(22, 100%, 77%, 0.3) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(242, 100%, 70%, 0.3) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343, 100%, 76%, 0.3) 0px, transparent 50%);
}

.dark .gradient-mesh {
  background: 
    radial-gradient(at 40% 20%, hsla(228, 100%, 74%, 0.1) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 0.1) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(355, 100%, 93%, 0.1) 0px, transparent 50%),
    radial-gradient(at 80% 50%, hsla(340, 100%, 76%, 0.1) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(22, 100%, 77%, 0.1) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(242, 100%, 70%, 0.1) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343, 100%, 76%, 0.1) 0px, transparent 50%);
}

/* Modern card hover effects */
.modern-card {
  @apply transition-all duration-300 ease-out;
  @apply hover:shadow-2xl hover:shadow-blue-500/10;
  @apply hover:-translate-y-1;
  @apply border border-border/50;
  @apply bg-card/80 backdrop-blur-xl;
}

.dark .modern-card {
  @apply hover:shadow-2xl hover:shadow-blue-400/20;
  @apply bg-card/50;
}

/* Enhanced button styles */
.btn-primary {
  @apply bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700;
  @apply hover:from-blue-700 hover:via-blue-800 hover:to-indigo-800;
  @apply shadow-lg shadow-blue-500/25;
  @apply hover:shadow-xl hover:shadow-blue-500/40;
  @apply transition-all duration-300;
  @apply border-0;
}

.dark .btn-primary {
  @apply shadow-blue-400/20;
  @apply hover:shadow-blue-400/30;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus states for accessibility */
button:focus-visible,
a:focus-visible {
  @apply outline-2 outline-offset-2 outline-primary;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Mobile-specific improvements */
@media (max-width: 640px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  body {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
  
  body {
    overflow-x: hidden;
  }
}

/* Line clamp utility for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
  transition: background 0.2s;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Status indicator animations */
.status-indicator {
  @apply relative;
}

.status-indicator::before {
  content: '';
  @apply absolute -inset-1 rounded-full;
  background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  animation: spin 2s linear infinite;
  z-index: -1;
}

/* Modern grid pattern */
.grid-pattern {
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

.dark .grid-pattern {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
}

/* Improved typography */
.text-gradient {
  @apply bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent;
}

.dark .text-gradient {
  @apply from-blue-400 via-purple-400 to-indigo-400;
}

/* Enhanced shadows */
.shadow-glow {
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.05),
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 0 20px rgba(59, 130, 246, 0.1);
}

.dark .shadow-glow {
  box-shadow: 
    0 0 0 1px rgba(255, 255, 255, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.3),
    0 4px 6px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(59, 130, 246, 0.2);
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

.dark .loading-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Interactive elements */
.interactive-element {
  @apply transition-all duration-200 ease-out;
  @apply hover:scale-105 active:scale-95;
  @apply cursor-pointer;
}

/* Modern badge styles */
.modern-badge {
  @apply px-3 py-1 rounded-full text-xs font-medium;
  @apply bg-gradient-to-r from-blue-50 to-indigo-50;
  @apply text-blue-700 border border-blue-200;
  @apply shadow-sm;
}

.dark .modern-badge {
  @apply from-blue-900/20 to-indigo-900/20;
  @apply text-blue-300 border-blue-800;
}