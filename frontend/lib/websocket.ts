'use client';

import { io, Socket } from 'socket.io-client';
import { TokenManager } from './api-client';

// WebSocket Event Types
export interface NotificationEvent {
  id: string;
  type: string;
  title: string;
  message: string;
  data?: any;
  timestamp: Date;
}

export interface ReminderEvent {
  reminderId: string;
  medicineName: string;
  type: 'sms' | 'call' | 'push';
  timestamp: Date;
}

export interface AdherenceUpdateEvent {
  userId: string;
  medicineId: string;
  adherenceRate: number;
  streakCount: number;
  timestamp: Date;
}

export interface AchievementEvent {
  achievementId: string;
  title: string;
  description: string;
  points: number;
  timestamp: Date;
}

export interface DashboardUpdateEvent {
  type: string;
  data: any;
  timestamp: Date;
}

// WebSocket Service Class
export class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private eventListeners: Map<string, Set<Function>> = new Map();

  constructor() {
    // Don't auto-connect in constructor - let the hook handle connection when authenticated
  }

  // Connection Management
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected || this.isConnecting) {
        resolve();
        return;
      }

      this.isConnecting = true;
      const token = TokenManager.getToken();
      
      if (!token) {
        console.warn('No authentication token available for WebSocket connection');
        this.isConnecting = false;
        reject(new Error('No authentication token'));
        return;
      }

      // Use WebSocket URL or derive from API URL by removing /api/v1 path
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL;
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
      const serverUrl = wsUrl || apiUrl.replace('/api/v1', '') || 'http://localhost:3001';

      console.log('WebSocket connecting to:', serverUrl);
      
      this.socket = io(serverUrl, {
        auth: {
          token: token,
        },
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
        forceNew: true,
        autoConnect: true,
      });

      // Connection event handlers
      this.socket.on('connect', () => {
        console.log('WebSocket connected:', this.socket?.id);
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        this.isConnecting = false;
        this.reconnectAttempts++;
        
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          reject(error);
        }
      });

      this.socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        this.isConnecting = false;
      });

      // Setup event listeners
      this.setupEventListeners();
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  // Event Listeners Setup
  private setupEventListeners(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('connected', (data) => {
      console.log('WebSocket authenticated:', data);
      this.emit('connected', data);
    });

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.emit('error', error);
    });

    // Notification events
    this.socket.on('notification', (data: NotificationEvent) => {
      console.log('Received notification:', data);
      this.emit('notification', data);
    });

    // Reminder events
    this.socket.on('reminder-sent', (data: ReminderEvent) => {
      console.log('Reminder sent:', data);
      this.emit('reminder-sent', data);
    });

    this.socket.on('reminder-failed', (data: ReminderEvent & { error: string }) => {
      console.log('Reminder failed:', data);
      this.emit('reminder-failed', data);
    });

    // Adherence events
    this.socket.on('adherence-updated', (data: AdherenceUpdateEvent) => {
      console.log('Adherence updated:', data);
      this.emit('adherence-updated', data);
    });

    // Achievement events
    this.socket.on('achievement-unlocked', (data: AchievementEvent) => {
      console.log('Achievement unlocked:', data);
      this.emit('achievement-unlocked', data);
    });

    // Dashboard events
    this.socket.on('dashboard-update', (data: DashboardUpdateEvent) => {
      console.log('Dashboard update:', data);
      this.emit('dashboard-update', data);
    });

    // Ping/Pong for connection health
    this.socket.on('pong', (data) => {
      this.emit('pong', data);
    });
  }

  // Event Management
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // Utility Methods
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  ping(): void {
    if (this.socket?.connected) {
      this.socket.emit('ping');
    }
  }

  // Room Management
  joinRoom(room: string): void {
    if (this.socket?.connected) {
      this.socket.emit('join-room', { room });
    }
  }

  leaveRoom(room: string): void {
    if (this.socket?.connected) {
      this.socket.emit('leave-room', { room });
    }
  }

  // Send Notification (for authorized roles)
  sendNotification(data: {
    userId: string;
    userRole: string;
    type: string;
    title: string;
    message: string;
    data?: any;
  }): void {
    if (this.socket?.connected) {
      this.socket.emit('send-notification', data);
    }
  }
}

// Singleton instance
export const webSocketService = new WebSocketService();
