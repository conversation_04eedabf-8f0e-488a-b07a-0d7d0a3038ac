import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  InsuranceService, 
  PolicyHolder, 
  InsuranceAdherenceReport, 
  BulkAdherenceReport, 
  RiskAssessmentReport,
  PolicyHolderQueryDto,
  AdherenceReportQueryDto,
  BulkAdherenceQueryDto,
  RiskAssessmentQueryDto
} from '../api/insurance';

// Insurance Store State Interface
interface InsuranceState {
  // Policy Holders
  policyHolders: PolicyHolder[];
  policyHoldersTotal: number;
  policyHoldersPage: number;
  policyHoldersLimit: number;
  policyHoldersLoading: boolean;
  policyHoldersError: string | null;

  // Dashboard Summary
  dashboardSummary: {
    total_policy_holders: number;
    average_adherence_rate: number;
    high_risk_patients: number;
    low_adherence_patients: number;
    total_claims_this_month: number;
    cost_savings_estimate: number;
    top_performing_hospitals: Array<{
      hospital_name: string;
      adherence_rate: number;
      patient_count: number;
    }>;
    recent_alerts: Array<{
      type: 'high_risk' | 'low_adherence' | 'missed_appointment';
      patient_name: string;
      message: string;
      timestamp: Date;
      priority: 'low' | 'medium' | 'high';
    }>;
  } | null;
  dashboardLoading: boolean;
  dashboardError: string | null;

  // Adherence Reports
  adherenceReports: Record<string, InsuranceAdherenceReport>;
  adherenceReportsLoading: Record<string, boolean>;
  adherenceReportsError: Record<string, string | null>;

  // Bulk Reports
  bulkAdherenceReport: BulkAdherenceReport | null;
  bulkReportLoading: boolean;
  bulkReportError: string | null;

  // Risk Assessment
  riskAssessmentReport: RiskAssessmentReport | null;
  riskAssessmentLoading: boolean;
  riskAssessmentError: string | null;

  // Hospital Performance
  hospitalPerformance: Array<{
    hospital_id: string;
    hospital_name: string;
    location: string;
    policy_holders: number;
    doctors: number;
    adherence_rate: number;
    risk_score: number;
    cost_efficiency: number;
  }>;
  hospitalPerformanceLoading: boolean;
  hospitalPerformanceError: string | null;

  // Analytics
  adherenceAnalytics: {
    overall_metrics: {
      total_patients: number;
      average_adherence: number;
      improvement_rate: number;
      cost_impact: number;
    };
    condition_breakdown: Array<{
      condition: string;
      patient_count: number;
      adherence_rate: number;
      risk_level: string;
    }>;
    hospital_performance: Array<{
      hospital_name: string;
      adherence_rate: number;
      patient_count: number;
      cost_per_patient: number;
    }>;
    trends: {
      weekly_adherence: number[];
      monthly_costs: number[];
      risk_distribution: {
        low: number;
        medium: number;
        high: number;
        critical: number;
      };
    };
  } | null;
  analyticsLoading: boolean;
  analyticsError: string | null;

  // Actions
  fetchPolicyHolders: (query?: PolicyHolderQueryDto) => Promise<void>;
  fetchDashboardSummary: () => Promise<void>;
  fetchPatientAdherenceReport: (patientId: string, query?: AdherenceReportQueryDto) => Promise<void>;
  fetchBulkAdherenceReport: (query: BulkAdherenceQueryDto) => Promise<void>;
  fetchRiskAssessmentReport: (query?: RiskAssessmentQueryDto) => Promise<void>;
  fetchHospitalPerformance: () => Promise<void>;
  fetchAdherenceAnalytics: (params?: { days?: number; group_by?: 'condition' | 'hospital' | 'doctor' }) => Promise<void>;
  
  // Utility actions
  clearErrors: () => void;
  reset: () => void;
  refreshAll: () => Promise<void>;
}

// Initial state
const initialState = {
  policyHolders: [],
  policyHoldersTotal: 0,
  policyHoldersPage: 1,
  policyHoldersLimit: 20,
  policyHoldersLoading: false,
  policyHoldersError: null,

  dashboardSummary: null,
  dashboardLoading: false,
  dashboardError: null,

  adherenceReports: {},
  adherenceReportsLoading: {},
  adherenceReportsError: {},

  bulkAdherenceReport: null,
  bulkReportLoading: false,
  bulkReportError: null,

  riskAssessmentReport: null,
  riskAssessmentLoading: false,
  riskAssessmentError: null,

  hospitalPerformance: [],
  hospitalPerformanceLoading: false,
  hospitalPerformanceError: null,

  adherenceAnalytics: null,
  analyticsLoading: false,
  analyticsError: null,
};

// Create the store
export const useInsuranceStore = create<InsuranceState>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Fetch policy holders
      fetchPolicyHolders: async (query?: PolicyHolderQueryDto) => {
        set({ policyHoldersLoading: true, policyHoldersError: null });
        try {
          const response = await InsuranceService.getPolicyHolders(query);
          set({
            policyHolders: response.data,
            policyHoldersTotal: response.total,
            policyHoldersPage: response.page,
            policyHoldersLimit: response.limit,
            policyHoldersLoading: false,
          });
        } catch (error: any) {
          set({
            policyHoldersError: error.message || 'Failed to fetch policy holders',
            policyHoldersLoading: false,
          });
        }
      },

      // Fetch dashboard summary
      fetchDashboardSummary: async () => {
        set({ dashboardLoading: true, dashboardError: null });
        try {
          const summary = await InsuranceService.getDashboardSummary();
          set({
            dashboardSummary: summary,
            dashboardLoading: false,
          });
        } catch (error: any) {
          set({
            dashboardError: error.message || 'Failed to fetch dashboard summary',
            dashboardLoading: false,
          });
        }
      },

      // Fetch patient adherence report
      fetchPatientAdherenceReport: async (patientId: string, query?: AdherenceReportQueryDto) => {
        set(state => ({
          adherenceReportsLoading: { ...state.adherenceReportsLoading, [patientId]: true },
          adherenceReportsError: { ...state.adherenceReportsError, [patientId]: null },
        }));
        try {
          const report = await InsuranceService.getPatientAdherenceReport(patientId, query);
          set(state => ({
            adherenceReports: { ...state.adherenceReports, [patientId]: report },
            adherenceReportsLoading: { ...state.adherenceReportsLoading, [patientId]: false },
          }));
        } catch (error: any) {
          set(state => ({
            adherenceReportsError: { 
              ...state.adherenceReportsError, 
              [patientId]: error.message || 'Failed to fetch adherence report' 
            },
            adherenceReportsLoading: { ...state.adherenceReportsLoading, [patientId]: false },
          }));
        }
      },

      // Fetch bulk adherence report
      fetchBulkAdherenceReport: async (query: BulkAdherenceQueryDto) => {
        set({ bulkReportLoading: true, bulkReportError: null });
        try {
          const report = await InsuranceService.getBulkAdherenceReport(query);
          set({
            bulkAdherenceReport: report,
            bulkReportLoading: false,
          });
        } catch (error: any) {
          set({
            bulkReportError: error.message || 'Failed to fetch bulk adherence report',
            bulkReportLoading: false,
          });
        }
      },

      // Fetch risk assessment report
      fetchRiskAssessmentReport: async (query?: RiskAssessmentQueryDto) => {
        set({ riskAssessmentLoading: true, riskAssessmentError: null });
        try {
          const report = await InsuranceService.getRiskAssessmentReport(query);
          set({
            riskAssessmentReport: report,
            riskAssessmentLoading: false,
          });
        } catch (error: any) {
          set({
            riskAssessmentError: error.message || 'Failed to fetch risk assessment report',
            riskAssessmentLoading: false,
          });
        }
      },

      // Fetch hospital performance
      fetchHospitalPerformance: async () => {
        set({ hospitalPerformanceLoading: true, hospitalPerformanceError: null });
        try {
          const performance = await InsuranceService.getHospitalPerformance();
          set({
            hospitalPerformance: performance,
            hospitalPerformanceLoading: false,
          });
        } catch (error: any) {
          set({
            hospitalPerformanceError: error.message || 'Failed to fetch hospital performance',
            hospitalPerformanceLoading: false,
          });
        }
      },

      // Fetch adherence analytics
      fetchAdherenceAnalytics: async (params?: { days?: number; group_by?: 'condition' | 'hospital' | 'doctor' }) => {
        set({ analyticsLoading: true, analyticsError: null });
        try {
          const analytics = await InsuranceService.getAdherenceAnalytics(params);
          set({
            adherenceAnalytics: analytics,
            analyticsLoading: false,
          });
        } catch (error: any) {
          set({
            analyticsError: error.message || 'Failed to fetch adherence analytics',
            analyticsLoading: false,
          });
        }
      },

      // Clear all errors
      clearErrors: () => {
        set({
          policyHoldersError: null,
          dashboardError: null,
          adherenceReportsError: {},
          bulkReportError: null,
          riskAssessmentError: null,
          hospitalPerformanceError: null,
          analyticsError: null,
        });
      },

      // Reset store to initial state
      reset: () => {
        set(initialState);
      },

      // Refresh all data
      refreshAll: async () => {
        const { 
          fetchPolicyHolders, 
          fetchDashboardSummary, 
          fetchHospitalPerformance, 
          fetchAdherenceAnalytics 
        } = get();
        
        await Promise.all([
          fetchPolicyHolders(),
          fetchDashboardSummary(),
          fetchHospitalPerformance(),
          fetchAdherenceAnalytics(),
        ]);
      },
    }),
    {
      name: 'insurance-storage',
      partialize: (state) => ({
        // Only persist non-loading states and non-error states
        policyHolders: state.policyHolders,
        dashboardSummary: state.dashboardSummary,
        adherenceReports: state.adherenceReports,
        bulkAdherenceReport: state.bulkAdherenceReport,
        riskAssessmentReport: state.riskAssessmentReport,
        hospitalPerformance: state.hospitalPerformance,
        adherenceAnalytics: state.adherenceAnalytics,
      }),
    }
  )
);
