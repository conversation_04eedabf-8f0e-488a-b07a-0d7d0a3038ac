import type { Medicine, Prescription, Achievement, UserAchievement, Claim, Policy } from '@/types';

// Mock users removed - now using real API data

export const mockMedicines: Medicine[] = [
  {
    id: '1',
    name: 'Metformin',
    dosage: '850mg',
    frequency: 'Twice daily',
    duration: 30,
    instructions: 'Take with meals',
    sideEffects: 'Nausea, diarrhea',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-31'),
    prescriptionId: '1',
  },
  {
    id: '2',
    name: 'Lisinopril',
    dosage: '10mg',
    frequency: 'Once daily',
    duration: 30,
    instructions: 'Take in the morning',
    sideEffects: 'Dry cough, dizziness',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-31'),
    prescriptionId: '1',
  },
  {
    id: '3',
    name: 'Omeprazole',
    dosage: '20mg',
    frequency: 'Once daily',
    duration: 14,
    instructions: 'Take before breakfast',
    sideEffects: 'Headache, nausea',
    startDate: new Date('2024-01-15'),
    endDate: new Date('2024-01-29'),
    prescriptionId: '2',
  },
];

export const mockPrescriptions: Prescription[] = [
  {
    id: '1',
    patientId: '1',
    doctorId: '2',
    uploadedAt: new Date('2024-01-01'),
    filename: 'prescription-jan-2024.pdf',
    fileUrl: '/uploads/prescription-jan-2024.pdf',
    status: 'completed',
    medicines: mockMedicines.slice(0, 2),
    extractedText: 'Metformin 850mg twice daily, Lisinopril 10mg once daily',
  },
  {
    id: '2',
    patientId: '1',
    uploadedAt: new Date('2024-01-15'),
    filename: 'prescription-gastro.pdf',
    fileUrl: '/uploads/prescription-gastro.pdf',
    status: 'completed',
    medicines: mockMedicines.slice(2),
    extractedText: 'Omeprazole 20mg once daily before breakfast',
  },
];

export const mockAchievements: Achievement[] = [
  {
    id: '1',
    name: 'First Step',
    description: 'Complete your first medication dose',
    icon: 'Trophy',
    category: 'milestone',
    requirement: 1,
    points: 10,
  },
  {
    id: '2',
    name: 'Week Warrior',
    description: 'Maintain a 7-day streak',
    icon: 'Flame',
    category: 'streak',
    requirement: 7,
    points: 50,
  },
  {
    id: '3',
    name: 'Perfect Week',
    description: 'Complete all medications for a week',
    icon: 'Star',
    category: 'consistency',
    requirement: 100,
    points: 75,
  },
  {
    id: '4',
    name: 'Month Master',
    description: 'Maintain a 30-day streak',
    icon: 'Crown',
    category: 'streak',
    requirement: 30,
    points: 200,
  },
  {
    id: '5',
    name: 'Consistency Champion',
    description: 'Achieve 95% adherence rate',
    icon: 'Medal',
    category: 'consistency',
    requirement: 95,
    points: 150,
  },
];

// Insurance-related mock data
export const mockClaims: Claim[] = [
  {
    id: '1',
    patientId: '1',
    insuranceId: '5',
    hospitalId: '3',
    doctorId: '2',
    claimNumber: 'CLM-2024-001',
    submissionDate: new Date('2024-01-10'),
    serviceDate: new Date('2024-01-05'),
    amount: 1250.00,
    status: 'approved',
    type: 'medication',
    description: 'Diabetes medication coverage',
    documents: ['prescription.pdf', 'receipt.pdf'],
    approvedAmount: 1100.00,
    approvalDate: new Date('2024-01-15')
  },
  {
    id: '2',
    patientId: '1',
    insuranceId: '5',
    hospitalId: '3',
    doctorId: '2',
    claimNumber: 'CLM-2024-002',
    submissionDate: new Date('2024-01-20'),
    serviceDate: new Date('2024-01-18'),
    amount: 350.00,
    status: 'pending',
    type: 'consultation',
    description: 'Cardiology consultation',
    documents: ['invoice.pdf']
  },
  {
    id: '3',
    patientId: '1',
    insuranceId: '5',
    hospitalId: '3',
    doctorId: '2',
    claimNumber: 'CLM-2024-003',
    submissionDate: new Date('2024-01-25'),
    serviceDate: new Date('2024-01-22'),
    amount: 2500.00,
    status: 'review',
    type: 'procedure',
    description: 'Diagnostic tests for heart condition',
    documents: ['medical_report.pdf', 'test_results.pdf', 'invoice.pdf']
  },
  {
    id: '4',
    patientId: '1',
    insuranceId: '5',
    hospitalId: '3',
    doctorId: '2',
    claimNumber: 'CLM-2024-004',
    submissionDate: new Date('2024-01-05'),
    serviceDate: new Date('2023-12-28'),
    amount: 5000.00,
    status: 'rejected',
    type: 'hospitalization',
    description: 'Emergency room visit',
    documents: ['hospital_bill.pdf', 'medical_report.pdf'],
    rejectionReason: 'Service date outside of policy coverage period'
  }
];

export const mockPolicies: Policy[] = [
  {
    id: '1',
    insuranceId: '5',
    patientId: '1',
    policyNumber: 'POL-2024-001',
    policyType: 'Premium Health',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2025-01-01'),
    coverageAmount: 500000.00,
    premium: 450.00,
    status: 'active',
    benefits: ['Hospitalization', 'Prescription Drugs', 'Specialist Consultations', 'Preventive Care'],
    deductible: 1000.00,
    copay: 25.00,
    medications: {
      covered: true,
      coveragePercentage: 80,
      maxAmount: 5000.00,
      excludedCategories: ['Cosmetic', 'Experimental']
    }
  },
  {
    id: '2',
    insuranceId: '5',
    patientId: '1',
    policyNumber: 'POL-2024-002',
    policyType: 'Dental Care',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2025-01-01'),
    coverageAmount: 50000.00,
    premium: 75.00,
    status: 'active',
    benefits: ['Routine Checkups', 'Fillings', 'Root Canals', 'Orthodontics'],
    deductible: 500.00,
    copay: 15.00,
    medications: {
      covered: true,
      coveragePercentage: 70,
      maxAmount: 1000.00
    }
  },
  {
    id: '3',
    insuranceId: '5',
    patientId: '1',
    policyNumber: 'POL-2023-003',
    policyType: 'Vision Care',
    startDate: new Date('2023-01-01'),
    endDate: new Date('2024-01-01'),
    coverageAmount: 25000.00,
    premium: 50.00,
    status: 'expired',
    benefits: ['Eye Exams', 'Prescription Glasses', 'Contact Lenses'],
    deductible: 250.00,
    copay: 10.00,
    medications: {
      covered: true,
      coveragePercentage: 60,
      maxAmount: 500.00
    }
  }
];

// Mock API functions
export const mockOCRExtraction = async (file: File): Promise<string> => {
  // Simulate AWS Textract processing
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  const mockResults = [
    'Metformin 850mg - Take twice daily with meals for 30 days',
    'Lisinopril 10mg - Take once daily in the morning for 30 days',
    'Omeprazole 20mg - Take once daily before breakfast for 14 days',
    'Amoxicillin 500mg - Take three times daily for 7 days',
  ];
  
  return mockResults[Math.floor(Math.random() * mockResults.length)];
};

export const mockCallAPI = async (patientId: string, message: string): Promise<boolean> => {
  // Simulate ElevenLabs Conversational AI call
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Simulate success/failure
  return Math.random() > 0.1; // 90% success rate
};

export const mockSMSAPI = async (phone: string, message: string): Promise<boolean> => {
  // Simulate SMS sending
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return Math.random() > 0.05; // 95% success rate
};