import { apiClient } from '../api-client';
import type {
  ApiAchievement,
  ApiUserAchievement,
  ApiGamificationStats,
  PaginatedResponse,
  SearchParams,
} from '../../types/api';

export class GamificationService {
  /**
   * Get user's gamification stats
   */
  static async getGamificationStats(userId?: string): Promise<ApiGamificationStats> {
    const endpoint = userId ? `/gamification/stats/${userId}` : '/gamification/stats';
    return apiClient.get<ApiGamificationStats>(endpoint);
  }

  /**
   * Get comprehensive gamification dashboard
   */
  static async getGamificationDashboard(patientId: string): Promise<{
    patient_id: string;
    current_stats: {
      total_points: number;
      current_streak: number;
      longest_streak: number;
      completion_rate: number;
      level: {
        level: number;
        name: string;
        min_points: number;
        max_points: number;
        benefits: string[];
      };
      points_to_next_level: number;
    };
    recent_achievements: Array<{
      achievement_id: string;
      achievement_name: string;
      points_earned: number;
      unlocked_at: Date;
    }>;
    weekly_progress: {
      current_week: number[];
      previous_week: number[];
      improvement: number;
    };
    monthly_progress: {
      current_month: number[];
      previous_month: number[];
      improvement: number;
    };
    next_milestones: Array<{
      type: 'streak' | 'points' | 'completion';
      target: number;
      current: number;
      progress_percentage: number;
      estimated_days: number;
    }>;
  }> {
    return apiClient.get(`/gamification/dashboard/${patientId}`);
  }

  /**
   * Get all available achievements
   */
  static async getAchievements(params?: SearchParams): Promise<PaginatedResponse<ApiAchievement>> {
    return apiClient.get<PaginatedResponse<ApiAchievement>>('/gamification/achievements', { params: params as Record<string, string | number | boolean> });
  }

  /**
   * Get user's unlocked achievements
   */
  static async getUserAchievements(userId?: string): Promise<ApiUserAchievement[]> {
    const endpoint = userId ? `/gamification/achievements/user/${userId}` : '/gamification/achievements/user';
    return apiClient.get<ApiUserAchievement[]>(endpoint);
  }

  /**
   * Get achievement by ID
   */
  static async getAchievement(id: string): Promise<ApiAchievement> {
    return apiClient.get<ApiAchievement>(`/gamification/achievements/${id}`);
  }

  /**
   * Check for new achievements
   */
  static async checkAchievements(): Promise<{
    newAchievements: ApiUserAchievement[];
    pointsEarned: number;
    levelUp: boolean;
    newLevel?: number;
  }> {
    return apiClient.post('/gamification/check-achievements', {}, {
      showSuccessToast: false, // We'll handle achievement notifications separately
    });
  }

  /**
   * Get leaderboard
   */
  static async getLeaderboard(params?: {
    period: 'daily' | 'weekly' | 'monthly' | 'all-time';
    category: 'points' | 'streak' | 'adherence';
    limit?: number;
  }): Promise<Array<{
    rank: number;
    userId: string;
    userName: string;
    avatar?: string;
    score: number;
    change: number;
    isCurrentUser: boolean;
  }>> {
    return apiClient.get('/gamification/leaderboard', { params });
  }

  /**
   * Get user's rank
   */
  static async getUserRank(params?: {
    category: 'points' | 'streak' | 'adherence';
    period: 'daily' | 'weekly' | 'monthly' | 'all-time';
  }): Promise<{
    rank: number;
    totalUsers: number;
    percentile: number;
    score: number;
    category: string;
    period: string;
  }> {
    return apiClient.get('/gamification/rank', { params });
  }

  /**
   * Get points history
   */
  static async getPointsHistory(params?: {
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<{
    id: string;
    points: number;
    reason: string;
    category: 'adherence' | 'streak' | 'achievement' | 'bonus';
    createdAt: string;
    metadata?: any;
  }>> {
    return apiClient.get('/gamification/points/history', { params });
  }

  /**
   * Award points manually (for testing or admin purposes)
   */
  static async awardPoints(data: {
    userId?: string;
    points: number;
    reason: string;
    category: 'adherence' | 'streak' | 'achievement' | 'bonus';
    metadata?: any;
  }): Promise<{
    pointsAwarded: number;
    totalPoints: number;
    levelUp: boolean;
    newLevel?: number;
  }> {
    return apiClient.post('/gamification/points/award', data, {
      showSuccessToast: true,
      successMessage: `${data.points} points awarded!`,
    });
  }

  /**
   * Get streak information
   */
  static async getStreakInfo(userId?: string): Promise<{
    currentStreak: number;
    longestStreak: number;
    streakStartDate: string;
    lastActivityDate: string;
    streakType: 'daily' | 'weekly';
    isActive: boolean;
    nextMilestone: {
      days: number;
      reward: number;
    };
  }> {
    const endpoint = userId ? `/gamification/streak/${userId}` : '/gamification/streak';
    return apiClient.get(endpoint);
  }

  /**
   * Update streak (usually called automatically)
   */
  static async updateStreak(data?: {
    activityDate?: string;
    activityType?: 'medicine_taken' | 'reminder_completed' | 'goal_achieved';
  }): Promise<{
    currentStreak: number;
    streakIncreased: boolean;
    pointsEarned: number;
    milestoneReached: boolean;
    milestoneReward?: number;
  }> {
    return apiClient.post('/gamification/streak/update', data);
  }

  /**
   * Get level information
   */
  static async getLevelInfo(userId?: string): Promise<{
    currentLevel: number;
    currentPoints: number;
    pointsForCurrentLevel: number;
    pointsForNextLevel: number;
    progressToNextLevel: number;
    levelName: string;
    levelBenefits: string[];
    nextLevelName: string;
    nextLevelBenefits: string[];
  }> {
    const endpoint = userId ? `/gamification/level/${userId}` : '/gamification/level';
    return apiClient.get(endpoint);
  }

  /**
   * Get challenges
   */
  static async getChallenges(params?: {
    status?: 'active' | 'completed' | 'expired';
    category?: 'daily' | 'weekly' | 'monthly' | 'special';
  }): Promise<Array<{
    id: string;
    title: string;
    description: string;
    category: 'daily' | 'weekly' | 'monthly' | 'special';
    requirement: number;
    progress: number;
    reward: number;
    status: 'active' | 'completed' | 'expired';
    startDate: string;
    endDate: string;
    completedAt?: string;
  }>> {
    return apiClient.get('/gamification/challenges', { params });
  }

  /**
   * Join a challenge
   */
  static async joinChallenge(challengeId: string): Promise<{
    success: boolean;
    challenge: {
      id: string;
      title: string;
      description: string;
      progress: number;
      requirement: number;
    };
  }> {
    return apiClient.post(`/gamification/challenges/${challengeId}/join`, {}, {
      showSuccessToast: true,
      successMessage: 'Challenge joined successfully!',
    });
  }

  /**
   * Get challenge progress
   */
  static async getChallengeProgress(challengeId: string): Promise<{
    challengeId: string;
    progress: number;
    requirement: number;
    completed: boolean;
    completedAt?: string;
    reward: number;
    timeRemaining: number;
  }> {
    return apiClient.get(`/gamification/challenges/${challengeId}/progress`);
  }

  /**
   * Get badges
   */
  static async getBadges(userId?: string): Promise<Array<{
    id: string;
    name: string;
    description: string;
    icon: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
    unlockedAt?: string;
    progress?: number;
    requirement?: number;
  }>> {
    const endpoint = userId ? `/gamification/badges/${userId}` : '/gamification/badges';
    return apiClient.get(endpoint);
  }

  /**
   * Get social features data
   */
  static async getSocialData(): Promise<{
    friends: Array<{
      id: string;
      name: string;
      avatar?: string;
      level: number;
      points: number;
      streak: number;
      lastActive: string;
    }>;
    friendRequests: Array<{
      id: string;
      from: {
        id: string;
        name: string;
        avatar?: string;
      };
      sentAt: string;
    }>;
    recentActivities: Array<{
      id: string;
      userId: string;
      userName: string;
      activity: string;
      points: number;
      timestamp: string;
    }>;
  }> {
    return apiClient.get('/gamification/social');
  }

  /**
   * Send friend request
   */
  static async sendFriendRequest(userId: string): Promise<void> {
    await apiClient.post(`/gamification/friends/request/${userId}`, {}, {
      showSuccessToast: true,
      successMessage: 'Friend request sent!',
    });
  }

  /**
   * Accept friend request
   */
  static async acceptFriendRequest(requestId: string): Promise<void> {
    await apiClient.post(`/gamification/friends/accept/${requestId}`, {}, {
      showSuccessToast: true,
      successMessage: 'Friend request accepted!',
    });
  }

  /**
   * Get gamification insights
   */
  static async getGamificationInsights(params?: {
    period: 'week' | 'month' | 'quarter';
  }): Promise<{
    totalPointsEarned: number;
    averageDailyPoints: number;
    mostActiveDay: string;
    favoriteActivity: string;
    improvementAreas: string[];
    achievements: {
      unlocked: number;
      available: number;
      nextToUnlock: ApiAchievement[];
    };
    comparison: {
      vsLastPeriod: number;
      vsPeers: number;
    };
  }> {
    return apiClient.get('/gamification/insights', { params });
  }

  /**
   * Get personalized recommendations
   */
  static async getRecommendations(): Promise<Array<{
    type: 'achievement' | 'challenge' | 'streak' | 'social';
    title: string;
    description: string;
    action: string;
    priority: 'low' | 'medium' | 'high';
    estimatedPoints: number;
  }>> {
    return apiClient.get('/gamification/recommendations');
  }

  /**
   * Update gamification preferences
   */
  static async updatePreferences(preferences: {
    enableNotifications: boolean;
    enableLeaderboard: boolean;
    enableSocialFeatures: boolean;
    preferredChallengeTypes: string[];
    notificationFrequency: 'immediate' | 'daily' | 'weekly';
  }): Promise<void> {
    await apiClient.patch('/gamification/preferences', preferences, {
      showSuccessToast: true,
      successMessage: 'Preferences updated successfully',
    });
  }
}

// Export both the class and an instance
export const gamificationService = GamificationService;
export default GamificationService;
