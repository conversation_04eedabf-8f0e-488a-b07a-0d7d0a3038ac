import { apiClient } from '../api-client';
import type {
  ApiMedicine,
  CreateMedicineRequest,
  PaginatedResponse,
  SearchParams,
} from '../../types/api';

export class MedicineService {
  /**
   * Get all medicines for the current user
   */
  static async getMedicines(params?: SearchParams): Promise<PaginatedResponse<ApiMedicine>> {
    return apiClient.get<PaginatedResponse<ApiMedicine>>('/medicines', { params });
  }

  /**
   * Get medicines for a specific patient (for doctors/hospitals)
   */
  static async getPatientMedicines(
    patientId: string,
    params?: SearchParams
  ): Promise<PaginatedResponse<ApiMedicine>> {
    return apiClient.get<PaginatedResponse<ApiMedicine>>(`/medicines/patient/${patientId}`, { params });
  }

  /**
   * Get a specific medicine by ID
   */
  static async getMedicine(id: string): Promise<ApiMedicine> {
    return apiClient.get<ApiMedicine>(`/medicines/${id}`);
  }

  /**
   * Create a new medicine
   */
  static async createMedicine(data: CreateMedicineRequest): Promise<ApiMedicine> {
    return apiClient.post<ApiMedicine>('/medicines', data, {
      showSuccessToast: true,
      successMessage: 'Medicine added successfully',
    });
  }

  /**
   * Update a medicine
   */
  static async updateMedicine(
    id: string,
    data: Partial<CreateMedicineRequest>
  ): Promise<ApiMedicine> {
    return apiClient.patch<ApiMedicine>(`/medicines/${id}`, data, {
      showSuccessToast: true,
      successMessage: 'Medicine updated successfully',
    });
  }

  /**
   * Delete a medicine
   */
  static async deleteMedicine(id: string): Promise<void> {
    await apiClient.delete(`/medicines/${id}`, {
      showSuccessToast: true,
      successMessage: 'Medicine deleted successfully',
    });
  }

  /**
   * Get today's medicines for the current user
   */
  static async getTodaysMedicines(): Promise<ApiMedicine[]> {
    return apiClient.get<ApiMedicine[]>('/medicines/today');
  }

  /**
   * Get upcoming medicines for the current user
   */
  static async getUpcomingMedicines(days: number = 7): Promise<ApiMedicine[]> {
    return apiClient.get<ApiMedicine[]>('/medicines/upcoming', {
      params: { days },
    });
  }

  /**
   * Mark medicine as taken
   */
  static async markMedicineAsTaken(
    medicineId: string,
    data?: {
      takenTime?: string;
      notes?: string;
    }
  ): Promise<void> {
    await apiClient.post(`/medicines/${medicineId}/take`, data, {
      showSuccessToast: true,
      successMessage: 'Medicine marked as taken',
    });
  }

  /**
   * Mark medicine as missed
   */
  static async markMedicineAsMissed(
    medicineId: string,
    data?: {
      reason?: string;
      notes?: string;
    }
  ): Promise<void> {
    await apiClient.post(`/medicines/${medicineId}/miss`, data, {
      showSuccessToast: true,
      successMessage: 'Medicine marked as missed',
    });
  }

  /**
   * Skip medicine dose
   */
  static async skipMedicine(
    medicineId: string,
    data?: {
      reason?: string;
      notes?: string;
    }
  ): Promise<void> {
    await apiClient.post(`/medicines/${medicineId}/skip`, data, {
      showSuccessToast: true,
      successMessage: 'Medicine dose skipped',
    });
  }

  /**
   * Get medicine statistics
   */
  static async getMedicineStats(medicineId: string, days: number = 30): Promise<{
    totalDoses: number;
    takenDoses: number;
    missedDoses: number;
    skippedDoses: number;
    adherenceRate: number;
    weeklyStats: Array<{
      date: string;
      taken: number;
      missed: number;
      skipped: number;
    }>;
  }> {
    return apiClient.get(`/medicines/${medicineId}/stats`, {
      params: { days },
    });
  }

  /**
   * Search medicines by name
   */
  static async searchMedicines(query: string): Promise<ApiMedicine[]> {
    return apiClient.get<ApiMedicine[]>('/medicines/search', {
      params: { q: query },
    });
  }

  /**
   * Get medicine interaction warnings
   */
  static async getMedicineInteractions(medicineIds: string[]): Promise<{
    interactions: Array<{
      medicine1: string;
      medicine2: string;
      severity: 'low' | 'medium' | 'high';
      description: string;
    }>;
    warnings: string[];
  }> {
    return apiClient.post('/medicines/interactions', { medicineIds });
  }

  /**
   * Get medicine side effects
   */
  static async getMedicineSideEffects(medicineId: string): Promise<{
    common: string[];
    serious: string[];
    rare: string[];
  }> {
    return apiClient.get(`/medicines/${medicineId}/side-effects`);
  }

  /**
   * Set medicine reminder preferences
   */
  static async setReminderPreferences(
    medicineId: string,
    preferences: {
      enableReminders: boolean;
      reminderTimes: string[];
      reminderTypes: ('notification' | 'sms' | 'call')[];
      snoozeEnabled: boolean;
      snoozeDuration: number;
    }
  ): Promise<void> {
    await apiClient.patch(`/medicines/${medicineId}/reminders`, preferences, {
      showSuccessToast: true,
      successMessage: 'Reminder preferences updated',
    });
  }

  /**
   * Get medicine history
   */
  static async getMedicineHistory(
    medicineId: string,
    params?: {
      startDate?: string;
      endDate?: string;
      page?: number;
      limit?: number;
    }
  ): Promise<PaginatedResponse<{
    id: string;
    medicineId: string;
    scheduledTime: string;
    takenTime?: string;
    status: 'taken' | 'missed' | 'skipped';
    notes?: string;
    createdAt: string;
  }>> {
    return apiClient.get(`/medicines/${medicineId}/history`, { params });
  }
}

// Export both the class and an instance
export const medicinesService = MedicineService;
export default MedicineService;
