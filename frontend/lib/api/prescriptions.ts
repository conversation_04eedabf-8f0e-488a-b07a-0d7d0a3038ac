import { apiClient } from '../api-client';
import type {
  ApiPrescription,
  CreatePrescriptionRequest,
  PaginatedResponse,
  SearchParams,
} from '../../types/api';

export class PrescriptionService {
  /**
   * Get all prescriptions for the current user
   */
  static async getPrescriptions(params?: SearchParams): Promise<PaginatedResponse<ApiPrescription>> {
    return apiClient.get<PaginatedResponse<ApiPrescription>>('/prescriptions', { params });
  }

  /**
   * Get prescriptions for a specific patient (for doctors/hospitals)
   */
  static async getPatientPrescriptions(
    patientId: string,
    params?: SearchParams
  ): Promise<PaginatedResponse<ApiPrescription>> {
    return apiClient.get<PaginatedResponse<ApiPrescription>>(`/prescriptions/patient/${patientId}`, { params });
  }

  /**
   * Get a specific prescription by ID
   */
  static async getPrescription(id: string): Promise<ApiPrescription> {
    return apiClient.get<ApiPrescription>(`/prescriptions/${id}`);
  }

  /**
   * Upload a new prescription
   */
  static async uploadPrescription(data: CreatePrescriptionRequest): Promise<ApiPrescription> {
    const formData = new FormData();
    formData.append('file', data.file);
    if (data.patientId) {
      formData.append('patient_id', data.patientId); // Use backend expected parameter name
    }

    return apiClient.post<ApiPrescription>('/prescriptions/upload', formData, {
      showSuccessToast: true,
      successMessage: 'Prescription uploaded successfully',
    });
  }

  /**
   * Update prescription details
   */
  static async updatePrescription(
    id: string,
    data: {
      extractedText?: string;
      status?: 'processing' | 'completed' | 'failed';
    }
  ): Promise<ApiPrescription> {
    return apiClient.patch<ApiPrescription>(`/prescriptions/${id}`, data, {
      showSuccessToast: true,
      successMessage: 'Prescription updated successfully',
    });
  }

  /**
   * Delete a prescription
   */
  static async deletePrescription(id: string): Promise<void> {
    await apiClient.delete(`/prescriptions/${id}`, {
      showSuccessToast: true,
      successMessage: 'Prescription deleted successfully',
    });
  }

  /**
   * Process prescription with AWS Textract
   */
  static async processPrescription(id: string): Promise<{
    extractedText: string;
    medicines: Array<{
      name: string;
      dosage: string;
      frequency: string;
      duration: number;
      instructions: string;
    }>;
  }> {
    return apiClient.post(`/prescriptions/${id}/process`, {}, {
      showSuccessToast: true,
      successMessage: 'Prescription processed successfully',
    });
  }

  /**
   * Get prescription processing status
   */
  static async getPrescriptionStatus(id: string): Promise<{
    status: 'processing' | 'completed' | 'failed';
    progress?: number;
    error?: string;
  }> {
    return apiClient.get(`/prescriptions/${id}/status`);
  }

  /**
   * Download prescription file
   */
  static async downloadPrescription(id: string): Promise<Blob> {
    const response = await fetch(`${apiClient['baseURL']}/prescriptions/${id}/download`, {
      headers: {
        Authorization: `Bearer ${localStorage.getItem('auth-token')}`,
      },
    });

    if (!response.ok) {
      throw new Error('Failed to download prescription');
    }

    return response.blob();
  }

  /**
   * Get prescription analytics
   */
  static async getPrescriptionAnalytics(params?: {
    startDate?: string;
    endDate?: string;
    patientId?: string;
  }): Promise<{
    totalPrescriptions: number;
    processingCount: number;
    completedCount: number;
    failedCount: number;
    averageProcessingTime: number;
    monthlyStats: Array<{
      month: string;
      count: number;
    }>;
  }> {
    return apiClient.get('/prescriptions/analytics', { params });
  }

  /**
   * Search prescriptions
   */
  static async searchPrescriptions(query: string, params?: SearchParams): Promise<PaginatedResponse<ApiPrescription>> {
    return apiClient.get<PaginatedResponse<ApiPrescription>>('/prescriptions/search', {
      params: { q: query, ...params },
    });
  }

  /**
   * Get recent prescriptions
   */
  static async getRecentPrescriptions(limit: number = 10): Promise<ApiPrescription[]> {
    return apiClient.get<ApiPrescription[]>('/prescriptions/recent', {
      params: { limit },
    });
  }

  /**
   * Share prescription with doctor/hospital
   */
  static async sharePrescription(
    id: string,
    data: {
      recipientId: string;
      recipientType: 'doctor' | 'hospital';
      message?: string;
    }
  ): Promise<void> {
    await apiClient.post(`/prescriptions/${id}/share`, data, {
      showSuccessToast: true,
      successMessage: 'Prescription shared successfully',
    });
  }

  /**
   * Get shared prescriptions
   */
  static async getSharedPrescriptions(params?: SearchParams): Promise<PaginatedResponse<ApiPrescription & {
    sharedBy: {
      id: string;
      name: string;
      email: string;
    };
    sharedAt: string;
    message?: string;
  }>> {
    return apiClient.get('/prescriptions/shared', { params });
  }

  /**
   * Validate prescription data
   */
  static async validatePrescription(data: {
    medicines: Array<{
      name: string;
      dosage: string;
      frequency: string;
      duration: number;
    }>;
  }): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
    suggestions: Array<{
      field: string;
      suggestion: string;
    }>;
  }> {
    return apiClient.post('/prescriptions/validate', data);
  }

  /**
   * Get prescription templates (for doctors)
   */
  static async getPrescriptionTemplates(): Promise<Array<{
    id: string;
    name: string;
    description: string;
    medicines: Array<{
      name: string;
      dosage: string;
      frequency: string;
      duration: number;
      instructions: string;
    }>;
  }>> {
    return apiClient.get('/prescriptions/templates');
  }

  /**
   * Create prescription from template (for doctors)
   */
  static async createFromTemplate(
    templateId: string,
    data: {
      patientId: string;
      customizations?: Array<{
        medicineIndex: number;
        changes: {
          dosage?: string;
          frequency?: string;
          duration?: number;
          instructions?: string;
        };
      }>;
    }
  ): Promise<ApiPrescription> {
    return apiClient.post(`/prescriptions/templates/${templateId}/create`, data, {
      showSuccessToast: true,
      successMessage: 'Prescription created from template',
    });
  }
}

export default PrescriptionService;
