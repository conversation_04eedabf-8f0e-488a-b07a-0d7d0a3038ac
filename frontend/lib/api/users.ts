import { apiClient } from '@/lib/api-client';
import type { User, UserRole } from '@/types';

export interface UserListQuery {
  role?: UserRole;
  limit?: number;
  offset?: number;
  search?: string;
  sort_by?: 'name' | 'email' | 'created_at' | 'last_login';
  sort_order?: 'asc' | 'desc';
}

export interface UserStats {
  total: number;
  active: number;
  suspended: number;
  pending: number;
  doctors: number;
  patients: number;
  hospitals: number;
  insurance: number;
  admin: number;
}

export interface CreateUserRequest {
  email: string;
  name: string;
  role: UserRole;
  password: string;
}

export interface UpdateUserRequest {
  name?: string;
  email?: string;
  status?: 'active' | 'suspended' | 'pending';
}

export interface UserDetails extends User {
  status: 'active' | 'suspended' | 'pending';
  lastLogin?: Date;
  organization?: string;
  specialization?: string;
  patients?: number;
  adherenceRate?: number;
  location?: string;
  phone?: string;
  verified: boolean;
  subscription?: string;
}

/**
 * Users API Service
 * Handles user management operations for admin dashboard
 */
export class UsersService {
  /**
   * Get all users with optional filtering and pagination
   */
  static async getUsers(query: UserListQuery = {}): Promise<UserDetails[]> {
    const response = await apiClient.get<UserDetails[]>('/users', {
      params: query,
      showErrorToast: true,
    });
    return response;
  }

  /**
   * Get user statistics for admin dashboard
   */
  static async getUserStats(): Promise<UserStats> {
    const response = await apiClient.get<UserStats>('/users/stats', {
      showErrorToast: true,
    });
    return response;
  }

  /**
   * Get a specific user by ID
   */
  static async getUser(id: string): Promise<UserDetails> {
    const response = await apiClient.get<UserDetails>(`/users/${id}`, {
      showErrorToast: true,
    });
    return response;
  }

  /**
   * Create a new user
   */
  static async createUser(userData: CreateUserRequest): Promise<UserDetails> {
    const response = await apiClient.post<UserDetails>('/users', userData, {
      showSuccessToast: true,
      successMessage: 'User created successfully!',
      showErrorToast: true,
    });
    return response;
  }

  /**
   * Update a user
   */
  static async updateUser(id: string, userData: UpdateUserRequest): Promise<UserDetails> {
    const response = await apiClient.patch<UserDetails>(`/users/${id}`, userData, {
      showSuccessToast: true,
      successMessage: 'User updated successfully!',
      showErrorToast: true,
    });
    return response;
  }

  /**
   * Delete a user
   */
  static async deleteUser(id: string): Promise<void> {
    await apiClient.delete(`/users/${id}`, {
      showSuccessToast: true,
      successMessage: 'User deleted successfully!',
      showErrorToast: true,
    });
  }

  /**
   * Suspend a user
   */
  static async suspendUser(id: string): Promise<UserDetails> {
    const response = await apiClient.patch<UserDetails>(`/users/${id}/suspend`, {}, {
      showSuccessToast: true,
      successMessage: 'User suspended successfully!',
      showErrorToast: true,
    });
    return response;
  }

  /**
   * Activate a user
   */
  static async activateUser(id: string): Promise<UserDetails> {
    const response = await apiClient.patch<UserDetails>(`/users/${id}/activate`, {}, {
      showSuccessToast: true,
      successMessage: 'User activated successfully!',
      showErrorToast: true,
    });
    return response;
  }

  /**
   * Reset user password
   */
  static async resetUserPassword(id: string): Promise<void> {
    await apiClient.post(`/users/${id}/reset-password`, {}, {
      showSuccessToast: true,
      successMessage: 'Password reset email sent!',
      showErrorToast: true,
    });
  }
}

// Export singleton instance
export const usersService = new UsersService();
