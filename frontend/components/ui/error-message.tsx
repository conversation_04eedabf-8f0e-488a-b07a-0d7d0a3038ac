import { cn } from '@/lib/utils';
import { AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from './button';

interface ErrorMessageProps {
  message: string;
  onRetry?: () => void;
  className?: string;
  variant?: 'default' | 'destructive' | 'warning';
}

const variantClasses = {
  default: 'text-muted-foreground',
  destructive: 'text-red-600 dark:text-red-400',
  warning: 'text-yellow-600 dark:text-yellow-400',
};

export function ErrorMessage({ 
  message, 
  onRetry, 
  className, 
  variant = 'default' 
}: ErrorMessageProps) {
  return (
    <div className={cn('flex flex-col items-center justify-center space-y-4 p-6', className)}>
      <div className="flex items-center space-x-2">
        <AlertCircle className={cn('h-5 w-5', variantClasses[variant])} />
        <p className={cn('text-sm font-medium', variantClasses[variant])}>
          {message}
        </p>
      </div>
      {onRetry && (
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onRetry}
          className="flex items-center space-x-2"
        >
          <RefreshCw className="h-4 w-4" />
          <span>Try Again</span>
        </Button>
      )}
    </div>
  );
}
