'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { LucideIcon } from 'lucide-react';

interface StatsCardProps {
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon?: LucideIcon;
  iconColor?: string;
  className?: string;
}

export function StatsCard({
  title,
  value,
  change,
  changeType = 'neutral',
  icon: Icon,
  iconColor = 'text-blue-600',
  className
}: StatsCardProps) {
  const getTrendIcon = () => {
    switch (changeType) {
      case 'positive':
        return <TrendingUp className="h-3 w-3 text-green-500" />;
      case 'negative':
        return <TrendingDown className="h-3 w-3 text-red-500" />;
      default:
        return <Minus className="h-3 w-3 text-gray-500" />;
    }
  };

  const getChangeColor = () => {
    switch (changeType) {
      case 'positive':
        return 'text-green-600';
      case 'negative':
        return 'text-red-600';
      default:
        return 'text-muted-foreground';
    }
  };

  return (
    <Card className={cn('modern-card group hover:scale-105 transition-all duration-300', className)}>
      <CardContent className="p-4 sm:p-6">
        <div className="flex items-center space-x-3">
          {Icon && (
            <div className={cn(
              'p-3 rounded-xl shadow-lg transition-all duration-300',
              'bg-gradient-to-br from-blue-500 to-indigo-600 group-hover:shadow-blue-500/25',
              iconColor.includes('green') && 'from-green-500 to-emerald-600 group-hover:shadow-green-500/25',
              iconColor.includes('orange') && 'from-orange-500 to-red-500 group-hover:shadow-orange-500/25',
              iconColor.includes('purple') && 'from-purple-500 to-pink-600 group-hover:shadow-purple-500/25'
            )}>
              <Icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
            </div>
          )}
          <div className="min-w-0 flex-1">
            <p className="text-2xl sm:text-3xl font-bold text-foreground">{value}</p>
            <p className="text-xs sm:text-sm text-muted-foreground">{title}</p>
            {change && (
              <div className="flex items-center mt-1">
                {getTrendIcon()}
                <span className={cn('text-xs font-medium ml-1', getChangeColor())}>
                  {change}
                </span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}