'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/store';
import type { UserRole } from '@/types';

interface RoleGuardProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
  fallbackPath?: string;
  showUnauthorized?: boolean;
}

/**
 * RoleGuard component that restricts access based on user roles
 * Redirects unauthorized users to their appropriate dashboard or a fallback path
 */
export function RoleGuard({ 
  children, 
  allowedRoles, 
  fallbackPath,
  showUnauthorized = false 
}: RoleGuardProps) {
  const router = useRouter();
  const { user, isAuthenticated, hasHydrated } = useAuthStore();

  useEffect(() => {
    if (hasHydrated && isAuthenticated && user) {
      const userRole = user.role;
      const hasPermission = allowedRoles.includes(userRole);

      if (!hasPermission) {
        console.warn(`Access denied: User with role '${userRole}' attempted to access page requiring roles: ${allowedRoles.join(', ')}`);
        
        // Redirect to fallback path or user's dashboard
        const redirectPath = fallbackPath || `/dashboard/${userRole}`;
        router.push(redirectPath);
      }
    }
  }, [user, isAuthenticated, hasHydrated, allowedRoles, fallbackPath, router]);

  // Show loading while hydrating
  if (!hasHydrated) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show nothing if not authenticated (AuthProvider will handle redirect)
  if (!isAuthenticated) {
    return null;
  }

  // Check if user has permission
  if (user && !allowedRoles.includes(user.role)) {
    if (showUnauthorized) {
      return (
        <div className="h-screen flex items-center justify-center bg-background">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-destructive mb-2">Access Denied</h1>
            <p className="text-muted-foreground mb-4">
              You don't have permission to access this page.
            </p>
            <button
              onClick={() => router.push(`/dashboard/${user.role}`)}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      );
    }
    return null; // Will redirect via useEffect
  }

  return <>{children}</>;
}

/**
 * Higher-order component for role-based protection
 */
export function withRoleGuard<P extends object>(
  Component: React.ComponentType<P>,
  allowedRoles: UserRole[],
  options?: {
    fallbackPath?: string;
    showUnauthorized?: boolean;
  }
) {
  return function ProtectedComponent(props: P) {
    return (
      <RoleGuard 
        allowedRoles={allowedRoles}
        fallbackPath={options?.fallbackPath}
        showUnauthorized={options?.showUnauthorized}
      >
        <Component {...props} />
      </RoleGuard>
    );
  };
}
