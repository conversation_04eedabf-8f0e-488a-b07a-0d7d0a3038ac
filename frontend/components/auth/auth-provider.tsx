'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/lib/store';
import { TokenManager } from '@/lib/api-client';

/**
 * AuthProvider component that handles authentication initialization
 * This component should be placed at the root of the app to ensure
 * authentication state is properly initialized on app startup
 */
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { checkAuth, hasHydrated, isAuthenticated } = useAuthStore();

  useEffect(() => {
    // Only check auth after hydration is complete and if we have a token
    if (hasHydrated && !isAuthenticated) {
      const token = TokenManager.getToken();
      if (token && !TokenManager.isTokenExpired(token)) {
        // We have a valid token, check authentication
        checkAuth().catch((error) => {
          console.error('Auth check failed:', error);
          // Clear invalid tokens
          TokenManager.clearTokens();
        });
      }
    }
  }, [hasHydrated, isAuthenticated, checkAuth]);

  return <>{children}</>;
}
