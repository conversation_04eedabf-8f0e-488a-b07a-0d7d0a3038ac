'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Trophy, Star, Flame, Crown, Medal } from 'lucide-react';
import type { Achievement, UserAchievement } from '@/types';

interface AchievementBadgeProps {
  achievement: Achievement;
  userAchievement?: UserAchievement;
  progress?: number;
}

const iconMap = {
  Trophy,
  Star,
  Flame,
  Crown,
  Medal,
};

export function AchievementBadge({ 
  achievement, 
  userAchievement, 
  progress = 0 
}: AchievementBadgeProps) {
  const Icon = iconMap[achievement.icon as keyof typeof iconMap] || Trophy;
  const isUnlocked = !!userAchievement;
  
  return (
    <Card className={`relative overflow-hidden ${isUnlocked ? 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200' : 'bg-gray-50'}`}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className={`p-3 rounded-full ${isUnlocked ? 'bg-yellow-100' : 'bg-gray-200'}`}>
            <Icon className={`h-6 w-6 ${isUnlocked ? 'text-yellow-600' : 'text-gray-400'}`} />
          </div>
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <h3 className={`font-semibold ${isUnlocked ? 'text-gray-900' : 'text-gray-500'}`}>
                {achievement.name}
              </h3>
              <Badge variant={isUnlocked ? 'default' : 'secondary'}>
                {achievement.points} pts
              </Badge>
            </div>
            <p className={`text-sm ${isUnlocked ? 'text-gray-600' : 'text-gray-400'}`}>
              {achievement.description}
            </p>
            {!isUnlocked && progress > 0 && (
              <div className="mt-2">
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Progress</span>
                  <span>{progress}/{achievement.requirement}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                  <div 
                    className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(100, (progress / achievement.requirement) * 100)}%` }}
                  />
                </div>
              </div>
            )}
            {userAchievement && (
              <p className="text-xs text-gray-500 mt-1">
                Unlocked {userAchievement.unlockedAt.toLocaleDateString()}
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}