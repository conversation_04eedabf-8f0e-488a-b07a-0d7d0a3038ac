'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Flame, Calendar, Target, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StreakCounterProps {
  currentStreak: number;
  longestStreak: number;
  weeklyProgress: number[];
}

export function StreakCounter({ 
  currentStreak, 
  longestStreak, 
  weeklyProgress 
}: StreakCounterProps) {
  const days = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
  const today = new Date().getDay();

  return (
    <Card className="modern-card">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <div className="p-2 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg">
            <Flame className="h-5 w-5 text-white" />
          </div>
          <span>Streak Counter</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current Streak */}
        <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl border border-orange-200 dark:border-orange-800">
          <div className="relative">
            <div className="text-4xl font-bold text-gradient bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              {currentStreak}
            </div>
            <div className="absolute -top-2 -right-2">
              <div className="h-6 w-6 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center animate-pulse">
                <Flame className="h-3 w-3 text-white" />
              </div>
            </div>
          </div>
          <p className="text-sm text-orange-700 dark:text-orange-300 font-medium mt-2">Current Streak</p>
          <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">Keep it going! 🔥</p>
        </div>

        {/* Weekly Progress */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <span className="text-sm text-muted-foreground flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              This Week
            </span>
            <Badge variant="outline" className="modern-badge">
              {weeklyProgress.filter(p => p > 0).length}/7 days
            </Badge>
          </div>
          <div className="grid grid-cols-7 gap-2">
            {days.map((day, index) => (
              <div
                key={index}
                className={cn(
                  "aspect-square rounded-xl flex items-center justify-center text-xs font-medium transition-all duration-300 hover:scale-110",
                  index === today
                    ? 'bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/25 ring-2 ring-blue-300'
                    : weeklyProgress[index] > 0
                    ? 'bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/25'
                    : 'bg-muted/50 text-muted-foreground hover:bg-muted'
                )}
              >
                {day}
              </div>
            ))}
          </div>
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              {weeklyProgress[today] > 0 ? 'Completed today!' : 'Complete today to continue your streak'}
            </p>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="p-3 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
            <div className="flex items-center space-x-2 mb-1">
              <Target className="h-3 w-3 text-purple-600" />
              <span className="text-xs text-purple-700 dark:text-purple-300">Best Streak</span>
            </div>
            <span className="text-lg font-bold text-purple-600">{longestStreak}</span>
            <span className="text-xs text-purple-500 ml-1">days</span>
          </div>
          
          <div className="p-3 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-800">
            <div className="flex items-center space-x-2 mb-1">
              <TrendingUp className="h-3 w-3 text-green-600" />
              <span className="text-xs text-green-700 dark:text-green-300">This Week</span>
            </div>
            <span className="text-lg font-bold text-green-600">
              {Math.round((weeklyProgress.filter(p => p > 0).length / 7) * 100)}
            </span>
            <span className="text-xs text-green-500 ml-1">%</span>
          </div>
        </div>

        {/* Motivation */}
        <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
          <div className="text-center">
            <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
              {currentStreak === 0 
                ? "Start your streak today! 💪" 
                : currentStreak < 7 
                ? "You're building momentum! 🚀"
                : currentStreak < 30
                ? "Amazing consistency! 🌟"
                : "You're a streak legend! 👑"
              }
            </p>
            <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
              {currentStreak === 0 
                ? "Take your first dose to begin"
                : `${30 - currentStreak} days to reach the 30-day milestone`
              }
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}