'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useWebSocket, useWebSocketNotifications, useWebSocketReminders, useWebSocketAdherence, useWebSocketAchievements, useWebSocketDashboard } from '@/hooks/use-websocket';
import { useAuthStore } from '@/lib/store';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Wifi, WifiOff, AlertCircle, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';

// WebSocket Context
interface WebSocketContextType {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  connect: () => Promise<void>;
  disconnect: () => void;
  ping: () => void;
  joinRoom: (room: string) => void;
  leaveRoom: (room: string) => void;
  sendNotification: (data: any) => void;
}

const WebSocketContext = createContext<WebSocketContextType | null>(null);

export function useWebSocketContext() {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocketContext must be used within a WebSocketProvider');
  }
  return context;
}

// Connection Status Component
function ConnectionStatus({ isConnected, isConnecting, error, connect }: {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  connect: () => Promise<void>;
}) {
  const [showStatus, setShowStatus] = useState(false);

  useEffect(() => {
    // Show status when there's an error or when connecting
    if (error || isConnecting || !isConnected) {
      setShowStatus(true);
      const timer = setTimeout(() => {
        if (isConnected && !error) {
          setShowStatus(false);
        }
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isConnected, isConnecting, error]);

  if (!showStatus && isConnected && !error) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <div className="bg-background border rounded-lg shadow-lg p-3 flex items-center space-x-3">
        {isConnecting ? (
          <>
            <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />
            <span className="text-sm">Connecting to real-time services...</span>
          </>
        ) : isConnected ? (
          <>
            <Wifi className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600">Real-time connected</span>
            <Badge variant="outline" className="text-xs">Live</Badge>
          </>
        ) : error ? (
          <>
            <AlertCircle className="h-4 w-4 text-red-500" />
            <div className="flex-1">
              <p className="text-sm text-red-600">Connection failed</p>
              <p className="text-xs text-muted-foreground">{error}</p>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={connect}
              className="text-xs"
            >
              Retry
            </Button>
          </>
        ) : (
          <>
            <WifiOff className="h-4 w-4 text-orange-500" />
            <span className="text-sm text-orange-600">Real-time disconnected</span>
            <Button
              size="sm"
              variant="outline"
              onClick={connect}
              className="text-xs"
            >
              Connect
            </Button>
          </>
        )}
      </div>
    </div>
  );
}

// WebSocket Provider Component
interface WebSocketProviderProps {
  children: React.ReactNode;
}

export function WebSocketProvider({ children }: WebSocketProviderProps) {
  const { isAuthenticated, user } = useAuthStore();
  const webSocket = useWebSocket();
  
  // Initialize all WebSocket hooks
  useWebSocketNotifications();
  useWebSocketReminders();
  useWebSocketAdherence();
  useWebSocketAchievements();
  useWebSocketDashboard();

  // Auto-join user-specific rooms when connected
  useEffect(() => {
    if (webSocket.isConnected && user) {
      // Join user-specific room
      webSocket.joinRoom(`user:${user.id}`);
      
      // Join role-specific room
      webSocket.joinRoom(`role:${user.role}`);
      
      // Join role-group rooms
      if (user.role === 'patient') {
        webSocket.joinRoom('patients');
      } else if (user.role === 'doctor' || user.role === 'hospital') {
        webSocket.joinRoom('healthcare');
      } else if (user.role === 'insurance') {
        webSocket.joinRoom('insurance');
      } else if (user.role === 'admin') {
        webSocket.joinRoom('admin');
      }

      console.log(`Joined WebSocket rooms for user ${user.id} (${user.role})`);
    }
  }, [webSocket.isConnected, user, webSocket.joinRoom]);

  // Ping connection every 30 seconds to keep it alive
  useEffect(() => {
    if (webSocket.isConnected) {
      const pingInterval = setInterval(() => {
        webSocket.ping();
      }, 30000);

      return () => clearInterval(pingInterval);
    }
  }, [webSocket.isConnected, webSocket.ping]);

  // Show connection status notifications
  useEffect(() => {
    if (webSocket.isConnected && isAuthenticated) {
      toast.success('Real-time features enabled', {
        description: 'You will receive live notifications and updates',
        duration: 3000,
      });
    }
  }, [webSocket.isConnected, isAuthenticated]);

  const contextValue: WebSocketContextType = {
    isConnected: webSocket.isConnected,
    isConnecting: webSocket.isConnecting,
    error: webSocket.error,
    connect: webSocket.connect,
    disconnect: webSocket.disconnect,
    ping: webSocket.ping,
    joinRoom: webSocket.joinRoom,
    leaveRoom: webSocket.leaveRoom,
    sendNotification: webSocket.sendNotification,
  };

  return (
    <WebSocketContext.Provider value={contextValue}>
      {children}
      {isAuthenticated && (
        <ConnectionStatus
          isConnected={webSocket.isConnected}
          isConnecting={webSocket.isConnecting}
          error={webSocket.error}
          connect={webSocket.connect}
        />
      )}
    </WebSocketContext.Provider>
  );
}

// Real-time Status Indicator Component (for use in headers/sidebars)
export function RealTimeStatusIndicator() {
  const { isConnected, isConnecting, error } = useWebSocketContext();

  if (isConnecting) {
    return (
      <div className="flex items-center space-x-1">
        <RefreshCw className="h-3 w-3 animate-spin text-blue-500" />
        <span className="text-xs text-muted-foreground">Connecting...</span>
      </div>
    );
  }

  if (isConnected) {
    return (
      <div className="flex items-center space-x-1">
        <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
        <span className="text-xs text-green-600">Live</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center space-x-1">
        <AlertCircle className="h-3 w-3 text-red-500" />
        <span className="text-xs text-red-600">Error</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-1">
      <WifiOff className="h-3 w-3 text-orange-500" />
      <span className="text-xs text-orange-600">Offline</span>
    </div>
  );
}
