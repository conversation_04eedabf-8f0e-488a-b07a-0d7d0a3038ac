'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuthStore, useAppStore } from '@/lib/store';
import { Sidebar } from './sidebar';
import { Header } from './header';
import { cn } from '@/lib/utils';
import type { UserRole } from '@/types';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, hasHydrated, user } = useAuthStore();
  const { sidebarCollapsed } = useAppStore();

  useEffect(() => {
    // Only redirect if hydration is complete and user is not authenticated
    if (hasHydrated && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    // Check role-based access control
    if (hasHydrated && isAuthenticated && user) {
      const currentPath = pathname;
      const userRole = user.role;

      // Extract the role from the current path (e.g., /dashboard/patient -> patient)
      const pathMatch = currentPath.match(/^\/dashboard\/([^\/]+)/);
      if (pathMatch) {
        const requiredRole = pathMatch[1] as UserRole;

        // Check if user has permission to access this role's dashboard
        // Allow access to settings pages for all roles
        if (userRole !== requiredRole && !pathname.includes('/settings')) {
          console.warn(`Access denied: User with role '${userRole}' attempted to access '${requiredRole}' dashboard`);
          // Redirect to user's appropriate dashboard
          router.push(`/dashboard/${userRole}`);
          return;
        }
      }
    }
  }, [isAuthenticated, hasHydrated, user, pathname, router]);

  // Show loading while hydrating
  if (!hasHydrated) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show nothing if not authenticated (will redirect)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="h-screen flex overflow-hidden bg-background">
      {/* Background gradient mesh */}
      <div className="fixed inset-0 gradient-mesh opacity-30 pointer-events-none" />
      <div className="fixed inset-0 grid-pattern opacity-50 pointer-events-none" />
      
      <Sidebar />
      
      <div 
        className={cn(
          'flex-1 flex flex-col min-w-0 transition-all duration-300 relative z-10',
          // Desktop sidebar spacing
          'lg:ml-64',
          sidebarCollapsed && 'lg:ml-16',
          // Mobile: no margin as sidebar is overlay
          'ml-0'
        )}
      >
        <Header />
        
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-4 sm:py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}