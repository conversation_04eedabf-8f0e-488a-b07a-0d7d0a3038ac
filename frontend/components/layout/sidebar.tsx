'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { useAuthStore, useAppStore } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Activity,
  Bell,
  Calendar,
  ChevronLeft,
  Home,
  LogOut,
  Menu,
  Pill,
  Settings,
  Users,
  Building2,
  BarChart3,
  FileText,
  Trophy,
  Phone,
  Stethoscope,
  Shield,
  X,
  Sparkles,
  HeartPulse,
  ClipboardCheck,
  DollarSign,
  Briefcase,
  FileCheck,
  Landmark
} from 'lucide-react';
import { useRouter } from 'next/navigation';

const navigation = {
  patient: [
    { name: 'Dashboard', href: '/dashboard/patient', icon: Home },
    { name: 'My Medicines', href: '/dashboard/patient/medicines', icon: Pill },
    { name: 'Prescriptions', href: '/dashboard/patient/prescriptions', icon: FileText },
    { name: 'Reminders', href: '/dashboard/patient/reminders', icon: Bell },
    { name: 'Progress', href: '/dashboard/patient/progress', icon: BarChart3 },
    { name: 'Achievements', href: '/dashboard/patient/achievements', icon: Trophy },
  ],
  doctor: [
    { name: 'Dashboard', href: '/dashboard/doctor', icon: Home },
    { name: 'Patients', href: '/dashboard/doctor/patients', icon: Users },
    { name: 'Prescriptions', href: '/dashboard/doctor/prescriptions', icon: FileText },
    { name: 'Analytics', href: '/dashboard/doctor/analytics', icon: BarChart3 },
    { name: 'Schedule', href: '/dashboard/doctor/schedule', icon: Calendar },
  ],
  hospital: [
    { name: 'Dashboard', href: '/dashboard/hospital', icon: Home },
    { name: 'Doctors', href: '/dashboard/hospital/doctors', icon: Stethoscope },
    { name: 'Patients', href: '/dashboard/hospital/patients', icon: Users },
    { name: 'Analytics', href: '/dashboard/hospital/analytics', icon: BarChart3 },
    { name: 'Reports', href: '/dashboard/hospital/reports', icon: FileText },
  ],
  admin: [
    { name: 'Dashboard', href: '/dashboard/admin', icon: Home },
    { name: 'Users', href: '/dashboard/admin/users', icon: Users },
    { name: 'Hospitals', href: '/dashboard/admin/hospitals', icon: Building2 },
    { name: 'Analytics', href: '/dashboard/admin/analytics', icon: BarChart3 },
    { name: 'System', href: '/dashboard/admin/system', icon: Settings },
  ],
  insurance: [
    { name: 'Dashboard', href: '/dashboard/insurance', icon: Home },
    { name: 'Users', href: '/dashboard/insurance/members', icon: Users },
    { name: 'Hospitals', href: '/dashboard/insurance/providers', icon: Building2 },
    { name: 'Reports', href: '/dashboard/insurance/analytics', icon: FileText },
    { name: 'Settings', href: '/dashboard/insurance/settings', icon: Settings },
  ],
};

export function Sidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const { user, logout } = useAuthStore();
  const { sidebarCollapsed, toggleSidebar } = useAppStore();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const userNavigation = user ? navigation[user.role] : [];

  // Close mobile menu when route changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [pathname]);

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById('mobile-sidebar');
      const menuButton = document.getElementById('mobile-menu-button');
      
      if (mobileMenuOpen && sidebar && menuButton && 
          !sidebar.contains(event.target as Node) && 
          !menuButton.contains(event.target as Node)) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [mobileMenuOpen]);

  const handleSettingsClick = () => {
    if (user) {
      if (user.role === 'admin') {
        router.push('/dashboard/admin/system');
      } else {
        router.push(`/dashboard/${user.role}/settings`);
      }
    }
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <Button
          id="mobile-menu-button"
          variant="outline"
          size="sm"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="modern-card shadow-glow"
        >
          {mobileMenuOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
        </Button>
      </div>

      {/* Mobile Overlay */}
      {mobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-40 bg-black/50 backdrop-blur-sm" />
      )}

      {/* Desktop Sidebar */}
      <div
        className={cn(
          'hidden lg:flex fixed inset-y-0 left-0 z-50 flex-col modern-card transition-all duration-300',
          'border-r border-border/50 bg-card/80 backdrop-blur-xl',
          sidebarCollapsed ? 'w-16' : 'w-64'
        )}
      >
        <SidebarContent 
          sidebarCollapsed={sidebarCollapsed}
          toggleSidebar={toggleSidebar}
          userNavigation={userNavigation}
          pathname={pathname}
          user={user}
          logout={logout}
          handleSettingsClick={handleSettingsClick}
        />
      </div>

      {/* Mobile Sidebar */}
      <div
        id="mobile-sidebar"
        className={cn(
          'lg:hidden fixed inset-y-0 left-0 z-50 w-64 modern-card transform transition-transform duration-300 ease-in-out',
          'border-r border-border/50 bg-card/95 backdrop-blur-xl',
          mobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        <SidebarContent 
          sidebarCollapsed={false}
          toggleSidebar={() => setMobileMenuOpen(false)}
          userNavigation={userNavigation}
          pathname={pathname}
          user={user}
          logout={logout}
          isMobile={true}
          handleSettingsClick={handleSettingsClick}
        />
      </div>
    </>
  );
}

interface SidebarContentProps {
  sidebarCollapsed: boolean;
  toggleSidebar: () => void;
  userNavigation: any[];
  pathname: string;
  user: any;
  logout: () => void;
  isMobile?: boolean;
  handleSettingsClick: () => void;
}

function SidebarContent({ 
  sidebarCollapsed, 
  toggleSidebar, 
  userNavigation, 
  pathname, 
  user, 
  logout,
  isMobile = false,
  handleSettingsClick
}: SidebarContentProps) {
  return (
    <>
      {/* Header */}
      <div className="flex h-16 items-center justify-between px-4 border-b border-border/50">
        <div className={cn('flex items-center space-x-3', sidebarCollapsed && !isMobile && 'hidden')}>
          <div className="h-8 w-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg animate-pulse-glow">
            <Pill className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-gradient">MedCare</h1>
            <p className="text-xs text-muted-foreground -mt-1">AI-Powered Healthcare</p>
          </div>
        </div>
        {!isMobile && (
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleSidebar}
            className="h-8 w-8 p-0 hover:bg-accent/50"
          >
            {sidebarCollapsed ? <Menu className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-3 py-4 space-y-2 overflow-y-auto">
        {userNavigation.map((item) => {
          // Check if the current path starts with the nav item's href
          // But make sure it's not just matching the base path for all items
          const isActive = item.href === '/dashboard/' + user?.role
            ? pathname === item.href // Exact match for dashboard
            : pathname.startsWith(item.href);
            
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'group relative flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-300',
                'hover:bg-accent/50 hover:shadow-md hover:scale-[1.02]',
                isActive
                  ? 'bg-gradient-to-r from-primary/15 via-primary/10 to-primary/5 text-primary shadow-lg shadow-primary/20 border border-primary/20'
                  : 'text-muted-foreground hover:text-foreground'
              )}
              title={sidebarCollapsed && !isMobile ? item.name : undefined}
            >
              {/* Active indicator line - only show when not collapsed */}
              {isActive && !sidebarCollapsed && (
                <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-primary to-primary/60 rounded-r-full shadow-lg shadow-primary/30" />
              )}
              
              <div className={cn(
                'flex items-center justify-center rounded-lg transition-all duration-300',
                isActive 
                  ? 'bg-primary/10 text-primary shadow-md' 
                  : 'text-muted-foreground group-hover:text-foreground group-hover:bg-accent/30',
                sidebarCollapsed && !isMobile ? 'w-8 h-8' : 'mr-3',
                'p-1.5' // Added fixed padding
              )}>
                <item.icon className="h-4 w-4 transition-all duration-300" />
                
                {/* Active glow effect */}
                {isActive && (
                  <div className="absolute inset-0 bg-primary/20 rounded-lg blur-sm -z-10" />
                )}
              </div>
              
              {(!sidebarCollapsed || isMobile) && (
                <span className="truncate font-medium">{item.name}</span>
              )}
              
              {isActive && (!sidebarCollapsed || isMobile) && (
                <div className="ml-auto flex items-center space-x-1">
                  <div className="h-1.5 w-1.5 bg-primary rounded-full animate-pulse" />
                  <Sparkles className="h-3 w-3 text-primary animate-pulse" />
                </div>
              )}
            </Link>
          );
        })}
      </nav>

      {/* User Profile */}
      {user && (
        <div className="border-t border-border/50 p-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className={cn(
                  'w-full justify-start p-3 h-auto hover:bg-accent/50 rounded-xl transition-all duration-300 hover:shadow-md',
                  sidebarCollapsed && !isMobile && 'justify-center'
                )}
              >
                <Avatar className="h-8 w-8 ring-2 ring-primary/20 shadow-lg">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white text-sm font-semibold">
                    {user.name.split(' ').map((n: string) => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                {(!sidebarCollapsed || isMobile) && (
                  <div className="ml-3 text-left min-w-0 flex-1">
                    <p className="text-sm font-medium text-foreground truncate">{user.name}</p>
                    <p className="text-xs text-muted-foreground capitalize">{user.role}</p>
                  </div>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 modern-card">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSettingsClick} className="hover:bg-accent/50 cursor-pointer">
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>
              <DropdownMenuItem onClick={logout} className="hover:bg-destructive/10 text-destructive cursor-pointer">
                <LogOut className="mr-2 h-4 w-4" />
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
    </>
  );
}