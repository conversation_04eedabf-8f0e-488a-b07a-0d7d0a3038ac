'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, Bar } from 'recharts';
import { TrendingUp, BarChart3 } from 'lucide-react';

interface AdherenceChartProps {
  data?: Array<{
    date: string;
    adherence: number;
    target: number;
  }>;
  type?: 'line' | 'bar';
  title?: string;
}

const defaultData = [
  { date: 'Mon', adherence: 95, target: 100 },
  { date: 'Tue', adherence: 100, target: 100 },
  { date: 'Wed', adherence: 85, target: 100 },
  { date: 'Thu', adherence: 100, target: 100 },
  { date: 'Fri', adherence: 90, target: 100 },
  { date: 'Sat', adherence: 100, target: 100 },
  { date: 'Sun', adherence: 95, target: 100 },
];

export function AdherenceChart({ 
  data = defaultData, 
  type = 'line',
  title = 'Weekly Adherence' 
}: AdherenceChartProps) {
  const averageAdherence = Math.round(data.reduce((sum, day) => sum + day.adherence, 0) / data.length);
  
  return (
    <Card className="modern-card">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
              {type === 'line' ? (
                <TrendingUp className="h-5 w-5 text-white" />
              ) : (
                <BarChart3 className="h-5 w-5 text-white" />
              )}
            </div>
            <span>{title}</span>
          </CardTitle>
          <div className="text-right">
            <p className="text-2xl font-bold text-gradient">{averageAdherence}%</p>
            <p className="text-xs text-muted-foreground">Average</p>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          {type === 'line' ? (
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
              <XAxis 
                dataKey="date" 
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
              />
              <YAxis 
                domain={[0, 100]} 
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
              />
              <Tooltip 
                formatter={(value: number) => [`${value}%`, 'Adherence']}
                labelFormatter={(label) => `Day: ${label}`}
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                }}
              />
              <Line 
                type="monotone" 
                dataKey="adherence" 
                stroke="url(#adherenceGradient)"
                strokeWidth={3}
                dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 5 }}
                activeDot={{ r: 7, fill: 'hsl(var(--primary))', strokeWidth: 2 }}
              />
              <Line 
                type="monotone" 
                dataKey="target" 
                stroke="hsl(var(--muted-foreground))" 
                strokeDasharray="5 5"
                strokeWidth={2}
                dot={false}
                opacity={0.5}
              />
              <defs>
                <linearGradient id="adherenceGradient" x1="0" y1="0" x2="1" y2="0">
                  <stop offset="0%" stopColor="hsl(var(--primary))" />
                  <stop offset="100%" stopColor="hsl(var(--primary))" stopOpacity={0.8} />
                </linearGradient>
              </defs>
            </LineChart>
          ) : (
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
              <XAxis 
                dataKey="date" 
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
              />
              <YAxis 
                domain={[0, 100]} 
                stroke="hsl(var(--muted-foreground))"
                fontSize={12}
              />
              <Tooltip 
                formatter={(value: number) => [`${value}%`, 'Adherence']}
                labelFormatter={(label) => `Day: ${label}`}
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                }}
              />
              <Bar 
                dataKey="adherence" 
                fill="url(#barGradient)"
                radius={[4, 4, 0, 0]}
              />
              <defs>
                <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="hsl(var(--primary))" />
                  <stop offset="100%" stopColor="hsl(var(--primary))" stopOpacity={0.6} />
                </linearGradient>
              </defs>
            </BarChart>
          )}
        </ResponsiveContainer>
        
        {/* Progress indicators */}
        <div className="grid grid-cols-3 gap-4 mt-6 pt-4 border-t border-border/50">
          <div className="text-center">
            <p className="text-xs text-muted-foreground">Best Day</p>
            <p className="text-sm font-semibold text-green-600">
              {Math.max(...data.map(d => d.adherence))}%
            </p>
          </div>
          <div className="text-center">
            <p className="text-xs text-muted-foreground">Consistency</p>
            <p className="text-sm font-semibold text-blue-600">
              {data.filter(d => d.adherence >= 90).length}/{data.length} days
            </p>
          </div>
          <div className="text-center">
            <p className="text-xs text-muted-foreground">Trend</p>
            <p className="text-sm font-semibold text-purple-600">
              {data[data.length - 1].adherence > data[0].adherence ? '↗️ Up' : '↘️ Down'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}