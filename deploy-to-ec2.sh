#!/bin/bash

# EC2 Deployment Script for Medical Adherence Platform
# Usage: ./deploy-to-ec2.sh <ec2-ip> <ssh-key-path>

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check arguments
if [ $# -ne 2 ]; then
    echo -e "${RED}Usage: $0 <ec2-ip> <ssh-key-path>${NC}"
    echo "Example: $0 ************ ~/.ssh/my-key.pem"
    exit 1
fi

EC2_IP=$1
SSH_KEY=$2
EC2_USER="ubuntu"  # Change if using different AMI
PROJECT_NAME="medcare-platform"
REMOTE_DIR="/home/<USER>/$PROJECT_NAME"

echo -e "${GREEN}🚀 Starting deployment to EC2...${NC}"

# Test SSH connection
echo -e "${YELLOW}Testing SSH connection...${NC}"
ssh -i "$SSH_KEY" -o ConnectTimeout=10 "$EC2_USER@$EC2_IP" "echo 'SSH connection successful'"

# Create project directory on EC2
echo -e "${YELLOW}Creating project directory...${NC}"
ssh -i "$SSH_KEY" "$EC2_USER@$EC2_IP" "mkdir -p $REMOTE_DIR"

# Copy project files (excluding node_modules and build artifacts)
echo -e "${YELLOW}Copying project files...${NC}"
rsync -avz --progress \
    --exclude 'node_modules' \
    --exclude '.next' \
    --exclude 'dist' \
    --exclude '.git' \
    --exclude '*.log' \
    -e "ssh -i $SSH_KEY" \
    ./ "$EC2_USER@$EC2_IP:$REMOTE_DIR/"

# Install dependencies and setup environment
echo -e "${YELLOW}Setting up environment on EC2...${NC}"
ssh -i "$SSH_KEY" "$EC2_USER@$EC2_IP" << 'ENDSSH'
    set -e
    
    # Update system
    sudo apt update
    
    # Install Node.js 20 if not present (required for NestJS 11+)
    if ! command -v node &> /dev/null; then
        echo "Installing Node.js 20..."
        curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
        sudo apt-get install -y nodejs
    else
        # Check if Node.js version is less than 20
        NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$NODE_VERSION" -lt 20 ]; then
            echo "Upgrading Node.js to version 20..."
            curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
            sudo apt-get install -y nodejs
        fi
    fi
    
    # Install PM2 globally if not present
    if ! command -v pm2 &> /dev/null; then
        echo "Installing PM2..."
        sudo npm install -g pm2
    fi
    
    # Navigate to project directory
    cd /home/<USER>/medcare-platform
    
    # Install backend dependencies
    echo "Installing backend dependencies..."
    cd backend
    npm install
    
    # Install frontend dependencies
    echo "Installing frontend dependencies..."
    cd ../frontend
    npm install
    
    echo "Dependencies installed successfully!"
ENDSSH

echo -e "${GREEN}✅ Deployment completed!${NC}"
echo -e "${YELLOW}Next steps:${NC}"
echo "1. SSH into your EC2 instance: ssh -i $SSH_KEY $EC2_USER@$EC2_IP"
echo "2. Configure environment variables"
echo "3. Run the start script: ./start-dev-servers.sh"
echo ""
echo -e "${GREEN}Your application will be available at:${NC}"
echo "Frontend: http://$EC2_IP:3000"
echo "Backend API: http://$EC2_IP:3001"
