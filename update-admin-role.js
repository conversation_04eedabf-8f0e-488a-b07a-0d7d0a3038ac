const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://ogpzoklypdukcurekbhe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ncHpva2x5cGR1a2N1cmVrYmhlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MTE4ODkzNCwiZXhwIjoyMDY2NzY0OTM0fQ.fEpTSh_u-PGTYt0Me-9w7SzgHMA247wHG-7wXqmTM1s';

async function updateUserRole() {
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  try {
    // Update user role to admin
    const { data, error } = await supabase
      .from('users')
      .update({ role: 'admin' })
      .eq('email', '<EMAIL>')
      .select();
    
    if (error) {
      console.error('Error updating user role:', error);
      return;
    }
    
    console.log('User role updated successfully:', data);
  } catch (error) {
    console.error('Failed to update user role:', error);
  }
}

updateUserRole();
