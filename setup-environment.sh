#!/bin/bash

# Environment Setup Script for EC2
# This script helps configure environment variables

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔧 Setting up environment variables...${NC}"

# Get EC2 public IP
EC2_PUBLIC_IP=$(curl -s ifconfig.me)
echo -e "${BLUE}Detected EC2 Public IP: $EC2_PUBLIC_IP${NC}"

# Backend environment setup
echo -e "${YELLOW}Setting up backend environment...${NC}"
if [ ! -f "backend/.env" ]; then
    cat > backend/.env << EOF
# Database Configuration
DATABASE_URL=your-supabase-connection-string
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Server Configuration
PORT=3001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d

# External Services
OPENAI_API_KEY=your-openai-api-key
ELEVENLABS_API_KEY=your-elevenlabs-api-key
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# AWS Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# CORS Configuration
CORS_ORIGIN=http://$EC2_PUBLIC_IP:3000
EOF
    echo -e "${GREEN}✅ Created backend/.env${NC}"
else
    echo -e "${YELLOW}⚠️  backend/.env already exists${NC}"
fi

# Frontend environment setup
echo -e "${YELLOW}Setting up frontend environment...${NC}"
if [ ! -f "frontend/.env.local" ]; then
    cat > frontend/.env.local << EOF
# API Configuration
NEXT_PUBLIC_API_URL=http://$EC2_PUBLIC_IP:3001

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# App Configuration
NEXT_PUBLIC_APP_NAME=Medical Adherence Platform
NEXT_PUBLIC_APP_VERSION=1.0.0
EOF
    echo -e "${GREEN}✅ Created frontend/.env.local${NC}"
else
    echo -e "${YELLOW}⚠️  frontend/.env.local already exists${NC}"
fi

echo ""
echo -e "${RED}🚨 IMPORTANT: You need to update the following values:${NC}"
echo -e "${YELLOW}Backend (.env):${NC}"
echo "  - DATABASE_URL"
echo "  - SUPABASE_URL"
echo "  - SUPABASE_ANON_KEY"
echo "  - SUPABASE_SERVICE_ROLE_KEY"
echo "  - JWT_SECRET"
echo "  - OPENAI_API_KEY"
echo "  - ELEVENLABS_API_KEY"
echo "  - TWILIO_ACCOUNT_SID"
echo "  - TWILIO_AUTH_TOKEN"
echo "  - TWILIO_PHONE_NUMBER"
echo "  - AWS credentials and S3 bucket"
echo ""
echo -e "${YELLOW}Frontend (.env.local):${NC}"
echo "  - NEXT_PUBLIC_SUPABASE_URL"
echo "  - NEXT_PUBLIC_SUPABASE_ANON_KEY"
echo ""
echo -e "${BLUE}Edit the files:${NC}"
echo "  nano backend/.env"
echo "  nano frontend/.env.local"
