# EC2 Security Group Configuration

## Required Ports for Development Mode

Your EC2 security group needs to allow the following inbound traffic:

### 1. SSH Access
- **Port:** 22
- **Protocol:** TCP
- **Source:** Your IP address (for security) or 0.0.0.0/0 (less secure)
- **Description:** SSH access for deployment and management

### 2. Frontend (Next.js Development Server)
- **Port:** 3000
- **Protocol:** TCP
- **Source:** 0.0.0.0/0 (to allow public access)
- **Description:** Next.js development server

### 3. Backend (NestJS API Server)
- **Port:** 3001
- **Protocol:** TCP
- **Source:** 0.0.0.0/0 (to allow public access)
- **Description:** NestJS API server

## AWS Console Steps

1. **Go to EC2 Dashboard**
   - Navigate to AWS Console → EC2

2. **Find Your Security Group**
   - Click "Security Groups" in the left sidebar
   - Find the security group attached to your EC2 instance

3. **Edit Inbound Rules**
   - Select your security group
   - Click "Edit inbound rules"
   - Add the following rules:

   | Type | Protocol | Port Range | Source | Description |
   |------|----------|------------|--------|-------------|
   | SSH | TCP | 22 | Your IP/0.0.0.0/0 | SSH access |
   | Custom TCP | TCP | 3000 | 0.0.0.0/0 | Frontend server |
   | Custom TCP | TCP | 3001 | 0.0.0.0/0 | Backend API |

4. **Save Rules**
   - Click "Save rules"

## CLI Method (Alternative)

If you prefer using AWS CLI:

```bash
# Get your security group ID
aws ec2 describe-instances --instance-ids YOUR_INSTANCE_ID

# Add rules (replace sg-xxxxxxxxx with your security group ID)
aws ec2 authorize-security-group-ingress \
    --group-id sg-xxxxxxxxx \
    --protocol tcp \
    --port 3000 \
    --cidr 0.0.0.0/0

aws ec2 authorize-security-group-ingress \
    --group-id sg-xxxxxxxxx \
    --protocol tcp \
    --port 3001 \
    --cidr 0.0.0.0/0
```

## Security Considerations

### For Development:
- ✅ Allow 0.0.0.0/0 for ports 3000 and 3001 (public access needed)
- ✅ Restrict SSH (port 22) to your IP address

### For Production:
- 🔒 Use Application Load Balancer
- 🔒 Restrict API access through proper authentication
- 🔒 Use HTTPS with SSL certificates
- 🔒 Consider VPC and private subnets

## Testing Connectivity

After setting up security groups, test connectivity:

```bash
# Test SSH
ssh -i your-key.pem ubuntu@your-ec2-ip

# Test ports (from your local machine)
telnet your-ec2-ip 3000
telnet your-ec2-ip 3001
```
