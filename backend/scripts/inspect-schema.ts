import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { SupabaseService } from '../src/config/supabase.service';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);
  
  const supabaseService = app.get(SupabaseService);
  const supabase = supabaseService.getAdminClient();

  console.log('🔍 Inspecting actual database schema...');

  try {
    // Get actual column information using a different approach
    // Try to select all columns from each table to see what exists
    
    console.log('\n🏆 Achievements table columns:');
    const { data: achievements, error: achievementsError } = await supabase
      .from('achievements')
      .select('*')
      .limit(0); // Get no rows, just column info

    if (achievementsError) {
      console.log('❌ Error:', achievementsError.message);
    } else {
      console.log('✅ Achievements table accessible');
    }

    console.log('\n📋 Prescriptions table columns:');
    const { data: prescriptions, error: prescriptionsError } = await supabase
      .from('prescriptions')
      .select('*')
      .limit(0);

    if (prescriptionsError) {
      console.log('❌ Error:', prescriptionsError.message);
    } else {
      console.log('✅ Prescriptions table accessible');
    }

    console.log('\n🎮 Gamification stats table columns:');
    const { data: gamificationStats, error: gamificationError } = await supabase
      .from('gamification_stats')
      .select('*')
      .limit(0);

    if (gamificationError) {
      console.log('❌ Error:', gamificationError.message);
    } else {
      console.log('✅ Gamification stats table accessible');
    }

    // Try to get some actual data to see the structure
    console.log('\n📊 Sample data structures:');
    
    const { data: sampleUsers } = await supabase
      .from('users')
      .select('*')
      .limit(1);
    
    if (sampleUsers && sampleUsers.length > 0) {
      console.log('👤 User columns:', Object.keys(sampleUsers[0]));
    }

    // Try a simple insert to see what columns are actually expected
    console.log('\n🧪 Testing minimal inserts...');
    
    // Test achievements with minimal data
    const { error: minAchievementError } = await supabase
      .from('achievements')
      .insert({
        name: 'Test',
        description: 'Test',
        icon: 'test',
        category: 'milestone',
        points: 10,
      });

    if (minAchievementError) {
      console.log('❌ Minimal achievement error:', minAchievementError.message);
    } else {
      console.log('✅ Minimal achievement insert successful');
      await supabase.from('achievements').delete().eq('name', 'Test');
    }

    // Test prescriptions with minimal data
    const { data: patients } = await supabase
      .from('users')
      .select('id')
      .eq('role', 'patient')
      .limit(1);

    if (patients && patients.length > 0) {
      const { error: minPrescriptionError } = await supabase
        .from('prescriptions')
        .insert({
          patient_id: patients[0].id,
          filename: 'test.pdf',
          file_url: '/test',
        });

      if (minPrescriptionError) {
        console.log('❌ Minimal prescription error:', minPrescriptionError.message);
      } else {
        console.log('✅ Minimal prescription insert successful');
        await supabase.from('prescriptions').delete().eq('filename', 'test.pdf');
      }
    }
    
  } catch (error) {
    console.error('❌ Error inspecting schema:', error);
  } finally {
    await app.close();
  }
}

bootstrap();
