const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function seedDatabase() {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Missing Supabase environment variables');
    console.log('Please ensure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    console.log('🌱 Starting database seeding...');

    // Read and execute schema
    const schemaPath = path.join(__dirname, '../src/database/schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    console.log('📋 Creating database schema...');
    const { error: schemaError } = await supabase.rpc('exec_sql', { sql: schema });
    
    if (schemaError) {
      console.log('⚠️  Schema creation had some issues (this is normal if tables already exist):', schemaError.message);
    } else {
      console.log('✅ Database schema created successfully');
    }

    // Read and execute sample data
    const sampleDataPath = path.join(__dirname, '../src/database/sample-data.sql');
    const sampleData = fs.readFileSync(sampleDataPath, 'utf8');
    
    console.log('📊 Inserting sample data...');
    const { error: dataError } = await supabase.rpc('exec_sql', { sql: sampleData });
    
    if (dataError) {
      console.log('⚠️  Sample data insertion had some issues (this is normal if data already exists):', dataError.message);
    } else {
      console.log('✅ Sample data inserted successfully');
    }

    console.log('🎉 Database seeding completed!');
    console.log('\n📋 Sample data created:');
    console.log('  👤 Patient: <EMAIL>');
    console.log('  👨‍⚕️ Doctor: <EMAIL>');
    console.log('  🏥 Hospital: <EMAIL>');
    console.log('  🛡️ Insurance: <EMAIL>');
    console.log('  💊 Sample prescriptions and medicines');
    console.log('  🏆 Achievement system with sample achievements');
    console.log('  📊 Gamification stats and sample progress');
    console.log('\n⚠️  Note: These are sample users for development only.');
    console.log('   In production, users should register through the API endpoints.');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

// Create exec_sql function if it doesn't exist
async function createExecSqlFunction() {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  const functionSql = `
    CREATE OR REPLACE FUNCTION exec_sql(sql text)
    RETURNS void
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      EXECUTE sql;
    END;
    $$;
  `;

  try {
    const { error } = await supabase.rpc('exec', { sql: functionSql });
    if (error) {
      console.log('Note: Could not create exec_sql function, will try direct execution');
    }
  } catch (e) {
    console.log('Note: Will try alternative seeding method');
  }
}

if (require.main === module) {
  createExecSqlFunction().then(() => seedDatabase());
}

module.exports = { seedDatabase };
