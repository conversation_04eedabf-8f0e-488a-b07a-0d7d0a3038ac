export interface MedicationInfo {
  name: string;
  genericName?: string;
  dosage: string;
  frequency: string;
  duration: string;
  instructions: string;
  sideEffects: string;
  conditions: string[];
  category: string;
  requiresMonitoring: boolean;
  costTier: 1 | 2 | 3 | 4; // Insurance cost tiers
}

export const medications: MedicationInfo[] = [
  // Diabetes Medications
  {
    name: 'Metformin',
    genericName: 'Metformin HCl',
    dosage: '850mg',
    frequency: 'Twice daily',
    duration: '90 days',
    instructions: 'Take with meals to reduce stomach upset',
    sideEffects: 'Nausea, diarrhea, metallic taste, lactic acidosis (rare)',
    conditions: ['Type 2 Diabetes'],
    category: 'Antidiabetic',
    requiresMonitoring: true,
    costTier: 1,
  },
  {
    name: 'Insulin Glargine',
    genericName: 'Insulin Glargine',
    dosage: '20 units',
    frequency: 'Once daily at bedtime',
    duration: '30 days',
    instructions: 'Inject subcutaneously, rotate injection sites',
    sideEffects: 'Hypoglycemia, injection site reactions, weight gain',
    conditions: ['Type 1 Diabetes', 'Type 2 Diabetes'],
    category: 'Insulin',
    requiresMonitoring: true,
    costTier: 3,
  },
  {
    name: 'Januvia',
    genericName: 'Sitagliptin',
    dosage: '100mg',
    frequency: 'Once daily',
    duration: '90 days',
    instructions: 'Can be taken with or without food',
    sideEffects: 'Upper respiratory infection, headache, pancreatitis (rare)',
    conditions: ['Type 2 Diabetes'],
    category: 'DPP-4 Inhibitor',
    requiresMonitoring: true,
    costTier: 4,
  },

  // Cardiovascular Medications
  {
    name: 'Lisinopril',
    genericName: 'Lisinopril',
    dosage: '10mg',
    frequency: 'Once daily',
    duration: '90 days',
    instructions: 'Take at the same time each day, preferably morning',
    sideEffects: 'Dry cough, dizziness, hyperkalemia, angioedema (rare)',
    conditions: ['Hypertension', 'Heart Disease'],
    category: 'ACE Inhibitor',
    requiresMonitoring: true,
    costTier: 1,
  },
  {
    name: 'Amlodipine',
    genericName: 'Amlodipine Besylate',
    dosage: '5mg',
    frequency: 'Once daily',
    duration: '90 days',
    instructions: 'Take at the same time each day',
    sideEffects: 'Ankle swelling, dizziness, flushing, fatigue',
    conditions: ['Hypertension'],
    category: 'Calcium Channel Blocker',
    requiresMonitoring: true,
    costTier: 1,
  },
  {
    name: 'Atorvastatin',
    genericName: 'Atorvastatin Calcium',
    dosage: '20mg',
    frequency: 'Once daily at bedtime',
    duration: '90 days',
    instructions: 'Take in the evening, avoid grapefruit juice',
    sideEffects: 'Muscle pain, liver enzyme elevation, memory issues',
    conditions: ['High Cholesterol', 'Heart Disease'],
    category: 'Statin',
    requiresMonitoring: true,
    costTier: 1,
  },
  {
    name: 'Warfarin',
    genericName: 'Warfarin Sodium',
    dosage: '5mg',
    frequency: 'Once daily',
    duration: '30 days',
    instructions: 'Take at the same time daily, monitor diet for vitamin K',
    sideEffects: 'Bleeding, bruising, hair loss',
    conditions: ['Atrial Fibrillation', 'Blood Clots'],
    category: 'Anticoagulant',
    requiresMonitoring: true,
    costTier: 1,
  },

  // Mental Health Medications
  {
    name: 'Sertraline',
    genericName: 'Sertraline HCl',
    dosage: '50mg',
    frequency: 'Once daily',
    duration: '90 days',
    instructions: 'Take with food, preferably in the morning',
    sideEffects: 'Nausea, diarrhea, insomnia, sexual dysfunction',
    conditions: ['Depression', 'Anxiety'],
    category: 'SSRI Antidepressant',
    requiresMonitoring: true,
    costTier: 1,
  },
  {
    name: 'Lorazepam',
    genericName: 'Lorazepam',
    dosage: '0.5mg',
    frequency: 'As needed, up to 3 times daily',
    duration: '30 days',
    instructions: 'Take as directed for anxiety, may cause drowsiness',
    sideEffects: 'Drowsiness, dizziness, confusion, dependency risk',
    conditions: ['Anxiety'],
    category: 'Benzodiazepine',
    requiresMonitoring: true,
    costTier: 1,
  },
  {
    name: 'Adderall XR',
    genericName: 'Amphetamine/Dextroamphetamine',
    dosage: '20mg',
    frequency: 'Once daily in morning',
    duration: '30 days',
    instructions: 'Take in morning, avoid late day dosing',
    sideEffects: 'Decreased appetite, insomnia, increased heart rate',
    conditions: ['ADHD'],
    category: 'Stimulant',
    requiresMonitoring: true,
    costTier: 2,
  },

  // Respiratory Medications
  {
    name: 'Albuterol Inhaler',
    genericName: 'Albuterol Sulfate',
    dosage: '90mcg per puff',
    frequency: '2 puffs every 4-6 hours as needed',
    duration: '30 days',
    instructions: 'Shake well before use, rinse mouth after use',
    sideEffects: 'Tremor, nervousness, headache, throat irritation',
    conditions: ['Asthma', 'COPD'],
    category: 'Bronchodilator',
    requiresMonitoring: false,
    costTier: 2,
  },
  {
    name: 'Advair Diskus',
    genericName: 'Fluticasone/Salmeterol',
    dosage: '250/50mcg',
    frequency: 'One inhalation twice daily',
    duration: '30 days',
    instructions: 'Rinse mouth after use, use at same times daily',
    sideEffects: 'Throat irritation, hoarseness, oral thrush',
    conditions: ['Asthma', 'COPD'],
    category: 'Inhaled Corticosteroid/LABA',
    requiresMonitoring: true,
    costTier: 4,
  },

  // Pain Management
  {
    name: 'Ibuprofen',
    genericName: 'Ibuprofen',
    dosage: '600mg',
    frequency: 'Three times daily with food',
    duration: '14 days',
    instructions: 'Take with food or milk to reduce stomach upset',
    sideEffects: 'Stomach upset, ulcers, kidney problems, heart risks',
    conditions: ['Chronic Pain', 'Arthritis'],
    category: 'NSAID',
    requiresMonitoring: true,
    costTier: 1,
  },
  {
    name: 'Tramadol',
    genericName: 'Tramadol HCl',
    dosage: '50mg',
    frequency: 'Every 6 hours as needed',
    duration: '30 days',
    instructions: 'Take as directed for pain, may cause drowsiness',
    sideEffects: 'Nausea, dizziness, constipation, seizure risk',
    conditions: ['Chronic Pain'],
    category: 'Opioid Analgesic',
    requiresMonitoring: true,
    costTier: 1,
  },

  // Gastrointestinal
  {
    name: 'Omeprazole',
    genericName: 'Omeprazole',
    dosage: '20mg',
    frequency: 'Once daily before breakfast',
    duration: '90 days',
    instructions: 'Take 30-60 minutes before first meal of the day',
    sideEffects: 'Headache, nausea, diarrhea, vitamin B12 deficiency',
    conditions: ['GERD', 'Peptic Ulcer'],
    category: 'Proton Pump Inhibitor',
    requiresMonitoring: false,
    costTier: 1,
  },

  // Bone Health
  {
    name: 'Alendronate',
    genericName: 'Alendronate Sodium',
    dosage: '70mg',
    frequency: 'Once weekly',
    duration: '90 days',
    instructions: 'Take on empty stomach with full glass of water, remain upright for 30 minutes',
    sideEffects: 'Esophageal irritation, muscle pain, jaw problems (rare)',
    conditions: ['Osteoporosis'],
    category: 'Bisphosphonate',
    requiresMonitoring: true,
    costTier: 1,
  }
];

export const medicalConditions = [
  'Type 1 Diabetes',
  'Type 2 Diabetes',
  'Hypertension',
  'High Cholesterol',
  'Heart Disease',
  'Atrial Fibrillation',
  'Depression',
  'Anxiety',
  'ADHD',
  'Asthma',
  'COPD',
  'Chronic Pain',
  'Arthritis',
  'GERD',
  'Peptic Ulcer',
  'Osteoporosis',
  'Blood Clots'
];

export const specializations = [
  'Endocrinology',
  'Cardiology',
  'Psychiatry',
  'General Medicine',
  'Pulmonology',
  'Rheumatology',
  'Gastroenterology',
  'Orthopedics',
  'Pain Management',
  'Internal Medicine'
];

// Helper function to get medications for specific conditions
export function getMedicationsForConditions(conditions: string[]): MedicationInfo[] {
  return medications.filter(med => 
    med.conditions.some(condition => conditions.includes(condition))
  );
}

// Helper function to get realistic medication combinations
export function getRealisticMedicationRegimen(conditions: string[]): MedicationInfo[] {
  const availableMeds = getMedicationsForConditions(conditions);
  const regimen: MedicationInfo[] = [];

  // Add core medications for each condition
  conditions.forEach(condition => {
    const conditionMeds = medications.filter(med => med.conditions.includes(condition));
    if (conditionMeds.length > 0) {
      // Add primary medication for condition
      regimen.push(conditionMeds[0]);
      
      // Sometimes add secondary medication
      if (Math.random() > 0.6 && conditionMeds.length > 1) {
        regimen.push(conditionMeds[1]);
      }
    }
  });

  // Remove duplicates
  return regimen.filter((med, index, self) => 
    index === self.findIndex(m => m.name === med.name)
  );
}
