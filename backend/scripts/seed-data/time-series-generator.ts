export interface AdherencePattern {
  patientId: string;
  medicineId: string;
  adherenceRate: number;
  consistencyPattern: 'consistent' | 'declining' | 'improving' | 'erratic';
  weekendEffect: boolean; // Different adherence on weekends
  timeOfDayPreference: 'morning' | 'evening' | 'flexible';
}

export interface GeneratedAdherenceRecord {
  medicineId: string;
  patientId: string;
  scheduledTime: Date;
  actualTime: Date | null;
  status: 'taken' | 'missed' | 'skipped';
  notes?: string;
}

export class TimeSeriesGenerator {
  /**
   * Generate historical adherence records for a patient's medication
   */
  static generateAdherenceHistory(
    patientId: string,
    medicineId: string,
    startDate: Date,
    endDate: Date,
    frequency: string,
    adherenceRate: number,
    pattern: AdherencePattern['consistencyPattern'] = 'consistent'
  ): GeneratedAdherenceRecord[] {
    const records: GeneratedAdherenceRecord[] = [];
    const currentDate = new Date(startDate);
    
    // Parse frequency to determine daily doses
    const dailyDoses = this.parseMedicationFrequency(frequency);
    
    while (currentDate <= endDate) {
      // Generate records for each dose of the day
      dailyDoses.forEach((doseTime, index) => {
        const scheduledDateTime = new Date(currentDate);
        scheduledDateTime.setHours(doseTime.hour, doseTime.minute, 0, 0);
        
        // Calculate adherence probability based on pattern and day
        const adherenceProb = this.calculateAdherenceProbability(
          adherenceRate,
          pattern,
          currentDate,
          index
        );
        
        const record: GeneratedAdherenceRecord = {
          medicineId,
          patientId,
          scheduledTime: new Date(scheduledDateTime),
          actualTime: null,
          status: 'missed',
        };
        
        // Determine if medication was taken
        if (Math.random() < adherenceProb / 100) {
          record.status = 'taken';
          
          // Calculate actual time taken (with some variance)
          const variance = this.getTimeVariance(doseTime.hour);
          const actualTime = new Date(scheduledDateTime);
          actualTime.setMinutes(actualTime.getMinutes() + variance);
          record.actualTime = actualTime;
          
          // Add notes for late doses
          if (Math.abs(variance) > 60) {
            record.notes = variance > 0 ? 'Taken late' : 'Taken early';
          }
        } else {
          // Small chance of being marked as skipped vs missed
          if (Math.random() < 0.1) {
            record.status = 'skipped';
            record.notes = 'Intentionally skipped';
          }
        }
        
        records.push(record);
      });
      
      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return records;
  }
  
  /**
   * Parse medication frequency into daily dose times
   */
  static parseMedicationFrequency(frequency: string): Array<{hour: number, minute: number}> {
    const freq = frequency.toLowerCase();
    
    if (freq.includes('once daily') || freq.includes('once a day')) {
      return [{hour: 8, minute: 0}]; // 8 AM
    } else if (freq.includes('twice daily') || freq.includes('twice a day')) {
      return [{hour: 8, minute: 0}, {hour: 20, minute: 0}]; // 8 AM, 8 PM
    } else if (freq.includes('three times daily') || freq.includes('three times a day')) {
      return [{hour: 8, minute: 0}, {hour: 14, minute: 0}, {hour: 20, minute: 0}]; // 8 AM, 2 PM, 8 PM
    } else if (freq.includes('four times daily') || freq.includes('four times a day')) {
      return [
        {hour: 8, minute: 0}, 
        {hour: 12, minute: 0}, 
        {hour: 16, minute: 0}, 
        {hour: 20, minute: 0}
      ]; // 8 AM, 12 PM, 4 PM, 8 PM
    } else if (freq.includes('bedtime') || freq.includes('at night')) {
      return [{hour: 22, minute: 0}]; // 10 PM
    } else if (freq.includes('morning')) {
      return [{hour: 8, minute: 0}]; // 8 AM
    } else if (freq.includes('evening')) {
      return [{hour: 20, minute: 0}]; // 8 PM
    } else if (freq.includes('every 4 hours')) {
      return [
        {hour: 8, minute: 0}, 
        {hour: 12, minute: 0}, 
        {hour: 16, minute: 0}, 
        {hour: 20, minute: 0}
      ];
    } else if (freq.includes('every 6 hours')) {
      return [
        {hour: 8, minute: 0}, 
        {hour: 14, minute: 0}, 
        {hour: 20, minute: 0}
      ];
    } else if (freq.includes('every 8 hours')) {
      return [
        {hour: 8, minute: 0}, 
        {hour: 16, minute: 0}
      ];
    } else if (freq.includes('every 12 hours')) {
      return [{hour: 8, minute: 0}, {hour: 20, minute: 0}];
    } else if (freq.includes('weekly')) {
      return [{hour: 8, minute: 0}]; // Once weekly, same time
    } else if (freq.includes('as needed')) {
      // For PRN medications, generate 0-3 doses per day randomly
      const numDoses = Math.floor(Math.random() * 4);
      const doses: Array<{hour: number, minute: number}> = [];
      for (let i = 0; i < numDoses; i++) {
        doses.push({
          hour: 8 + (i * 4) + Math.floor(Math.random() * 3),
          minute: Math.floor(Math.random() * 60)
        });
      }
      return doses;
    }
    
    // Default to once daily
    return [{hour: 8, minute: 0}];
  }
  
  /**
   * Calculate adherence probability based on various factors
   */
  private static calculateAdherenceProbability(
    baseRate: number,
    pattern: AdherencePattern['consistencyPattern'],
    date: Date,
    doseIndex: number
  ): number {
    let probability = baseRate;
    
    // Apply pattern effects
    const daysSinceStart = Math.floor((date.getTime() - new Date('2024-01-01').getTime()) / (1000 * 60 * 60 * 24));
    
    switch (pattern) {
      case 'declining':
        // Adherence decreases over time
        probability = baseRate * (1 - (daysSinceStart * 0.001));
        break;
      case 'improving':
        // Adherence improves over time
        probability = Math.min(95, baseRate + (daysSinceStart * 0.002));
        break;
      case 'erratic':
        // Random fluctuations
        probability = baseRate + (Math.random() - 0.5) * 40;
        break;
      case 'consistent':
      default:
        // Small random variation
        probability = baseRate + (Math.random() - 0.5) * 10;
        break;
    }
    
    // Weekend effect (typically lower adherence)
    const dayOfWeek = date.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) { // Sunday or Saturday
      probability *= 0.85;
    }
    
    // Time of day effect (evening doses often missed more)
    if (doseIndex > 0) {
      probability *= 0.95;
    }
    
    // Holiday effect (lower adherence around holidays)
    if (this.isHoliday(date)) {
      probability *= 0.8;
    }
    
    return Math.max(0, Math.min(100, probability));
  }
  
  /**
   * Generate time variance for when medication was actually taken
   */
  private static getTimeVariance(scheduledHour: number): number {
    // Morning medications tend to have less variance
    const baseVariance = scheduledHour < 12 ? 30 : 45;
    
    // Random variance in minutes (-variance to +variance)
    return Math.floor((Math.random() - 0.5) * 2 * baseVariance);
  }
  
  /**
   * Check if date is a holiday (simplified)
   */
  private static isHoliday(date: Date): boolean {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    // Major US holidays
    const holidays = [
      {month: 1, day: 1},   // New Year's Day
      {month: 7, day: 4},   // Independence Day
      {month: 12, day: 25}, // Christmas
      {month: 11, day: 24}, // Thanksgiving (approximate)
    ];
    
    return holidays.some(holiday => holiday.month === month && holiday.day === day);
  }
  
  /**
   * Generate gamification progress based on adherence history
   */
  static calculateGamificationStats(adherenceRecords: GeneratedAdherenceRecord[]) {
    const takenRecords = adherenceRecords.filter(r => r.status === 'taken');
    const totalRecords = adherenceRecords.length;
    
    // Calculate streaks
    let currentStreak = 0;
    let longestStreak = 0;
    let tempStreak = 0;
    
    // Sort by scheduled time
    const sortedRecords = adherenceRecords.sort((a, b) => 
      a.scheduledTime.getTime() - b.scheduledTime.getTime()
    );
    
    for (const record of sortedRecords) {
      if (record.status === 'taken') {
        tempStreak++;
        longestStreak = Math.max(longestStreak, tempStreak);
      } else {
        tempStreak = 0;
      }
    }
    
    // Current streak is from the end
    for (let i = sortedRecords.length - 1; i >= 0; i--) {
      if (sortedRecords[i].status === 'taken') {
        currentStreak++;
      } else {
        break;
      }
    }
    
    const adherenceRate = totalRecords > 0 ? (takenRecords.length / totalRecords) * 100 : 0;
    const totalPoints = Math.floor(takenRecords.length * 10 + longestStreak * 5);
    const level = Math.floor(totalPoints / 100) + 1;
    
    return {
      currentStreak,
      longestStreak,
      totalPoints,
      level,
      adherenceRate: Math.round(adherenceRate * 100) / 100,
      totalMedicinesTaken: takenRecords.length,
    };
  }
  
  /**
   * Generate weekly progress array (last 7 days)
   */
  static generateWeeklyProgress(adherenceRecords: GeneratedAdherenceRecord[]): number[] {
    const weeklyProgress = new Array(7).fill(0);
    const now = new Date();
    
    for (let i = 0; i < 7; i++) {
      const targetDate = new Date(now);
      targetDate.setDate(targetDate.getDate() - i);
      
      const dayRecords = adherenceRecords.filter(record => {
        const recordDate = new Date(record.scheduledTime);
        return recordDate.toDateString() === targetDate.toDateString();
      });
      
      const takenCount = dayRecords.filter(r => r.status === 'taken').length;
      weeklyProgress[6 - i] = takenCount;
    }
    
    return weeklyProgress;
  }
  
  /**
   * Generate monthly progress array (last 31 days)
   */
  static generateMonthlyProgress(adherenceRecords: GeneratedAdherenceRecord[]): number[] {
    const monthlyProgress = new Array(31).fill(0);
    const now = new Date();
    
    for (let i = 0; i < 31; i++) {
      const targetDate = new Date(now);
      targetDate.setDate(targetDate.getDate() - i);
      
      const dayRecords = adherenceRecords.filter(record => {
        const recordDate = new Date(record.scheduledTime);
        return recordDate.toDateString() === targetDate.toDateString();
      });
      
      const takenCount = dayRecords.filter(r => r.status === 'taken').length;
      monthlyProgress[30 - i] = takenCount;
    }
    
    return monthlyProgress;
  }
}
