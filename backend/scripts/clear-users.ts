import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { SupabaseService } from '../src/config/supabase.service';

async function clearUsers() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const supabaseService = app.get(SupabaseService);
  const supabase = supabaseService.getAdminClient();

  console.log('🧹 Clearing existing users...');

  try {
    // Delete from related tables first (foreign key constraints)
    await supabase.from('adherence_records').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('reminders').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('medicines').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('prescriptions').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('gamification_stats').delete().neq('patient_id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('patients').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('doctors').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('hospitals').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('insurance_providers').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    
    // Get all users to delete from auth
    const { data: users } = await supabase.from('users').select('id');
    
    // Delete users from auth
    if (users) {
      for (const user of users) {
        try {
          await supabase.auth.admin.deleteUser(user.id);
          console.log(`🗑️  Deleted auth user: ${user.id}`);
        } catch (error) {
          console.log(`⚠️  Could not delete auth user ${user.id}:`, error);
        }
      }
    }
    
    // Delete from users table
    await supabase.from('users').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    
    console.log('✅ All users cleared successfully!');
  } catch (error) {
    console.error('❌ Error clearing users:', error);
  } finally {
    await app.close();
  }
}

clearUsers();
