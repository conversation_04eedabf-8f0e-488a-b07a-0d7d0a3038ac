import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { SupabaseService } from '../src/config/supabase.service';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);
  
  const supabaseService = app.get(SupabaseService);
  const supabase = supabaseService.getAdminClient();

  console.log('🔍 Checking database structure...');

  try {
    // Check what tables exist
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public');

    if (tablesError) {
      console.log('❌ Error fetching tables:', tablesError.message);
    } else {
      console.log('📋 Existing tables:');
      tables?.forEach(table => console.log(`  - ${table.table_name}`));
    }

    // Check users table structure
    console.log('\n🔍 Checking users table...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    if (usersError) {
      console.log('❌ Users table error:', usersError.message);
    } else {
      console.log('✅ Users table exists and accessible');
    }

    // Check prescriptions table structure
    console.log('\n🔍 Checking prescriptions table...');
    const { data: prescriptions, error: prescriptionsError } = await supabase
      .from('prescriptions')
      .select('*')
      .limit(1);

    if (prescriptionsError) {
      console.log('❌ Prescriptions table error:', prescriptionsError.message);
    } else {
      console.log('✅ Prescriptions table exists and accessible');
    }

    // Check achievements table structure
    console.log('\n🔍 Checking achievements table...');
    const { data: achievements, error: achievementsError } = await supabase
      .from('achievements')
      .select('*')
      .limit(1);

    if (achievementsError) {
      console.log('❌ Achievements table error:', achievementsError.message);
    } else {
      console.log('✅ Achievements table exists and accessible');
    }

    // Check medicines table structure
    console.log('\n🔍 Checking medicines table...');
    const { data: medicines, error: medicinesError } = await supabase
      .from('medicines')
      .select('*')
      .limit(1);

    if (medicinesError) {
      console.log('❌ Medicines table error:', medicinesError.message);
    } else {
      console.log('✅ Medicines table exists and accessible');
    }

    // Check gamification_stats table structure
    console.log('\n🔍 Checking gamification_stats table...');
    const { data: gamificationStats, error: gamificationError } = await supabase
      .from('gamification_stats')
      .select('*')
      .limit(1);

    if (gamificationError) {
      console.log('❌ Gamification stats table error:', gamificationError.message);
    } else {
      console.log('✅ Gamification stats table exists and accessible');
    }
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    await app.close();
  }
}

bootstrap();
