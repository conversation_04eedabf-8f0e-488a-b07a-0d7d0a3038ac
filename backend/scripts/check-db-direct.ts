import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Use service role key to bypass RLS

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkDatabase() {
  try {
    console.log('🔍 Checking database directly...\n');

    // Check reminders table
    const { data: reminders, error: remindersError } = await supabase
      .from('reminders')
      .select('*')
      .order('scheduled_time', { ascending: true });

    if (remindersError) {
      console.error('Error fetching reminders:', remindersError);
    } else {
      console.log(`📋 Total reminders: ${reminders?.length || 0}`);
      if (reminders && reminders.length > 0) {
        reminders.forEach((reminder, index) => {
          console.log(`${index + 1}. ID: ${reminder.id}`);
          console.log(`   Patient ID: ${reminder.patient_id}`);
          console.log(`   Medicine ID: ${reminder.medicine_id}`);
          console.log(`   Scheduled: ${new Date(reminder.scheduled_time).toLocaleString()}`);
          console.log(`   Status: ${reminder.status}`);
          console.log(`   Type: ${reminder.reminder_type}`);
          console.log(`   Active: ${reminder.is_active}`);
          console.log('');
        });
      }
    }

    // Check medicines table
    const { data: medicines, error: medicinesError } = await supabase
      .from('medicines')
      .select('*')
      .limit(5);

    if (medicinesError) {
      console.error('Error fetching medicines:', medicinesError);
    } else {
      console.log(`💊 Total medicines (first 5): ${medicines?.length || 0}`);
    }

    // Check users table
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, role')
      .limit(5);

    if (usersError) {
      console.error('Error fetching users:', usersError);
    } else {
      console.log(`👥 Total users (first 5): ${users?.length || 0}`);
      if (users && users.length > 0) {
        users.forEach(user => {
          console.log(`   - ${user.email} (${user.role}) - ID: ${user.id}`);
        });
      }
    }

    // Check patients table
    const { data: patients, error: patientsError } = await supabase
      .from('patients')
      .select('*')
      .limit(5);

    if (patientsError) {
      console.error('Error fetching patients:', patientsError);
    } else {
      console.log(`🏥 Total patients (first 5): ${patients?.length || 0}`);
      if (patients && patients.length > 0) {
        patients.forEach(patient => {
          console.log(`   - Patient ID: ${patient.id}`);
        });
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

checkDatabase();
