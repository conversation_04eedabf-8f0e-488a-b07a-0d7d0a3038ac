import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugReminders() {
  try {
    console.log('🔍 Debugging reminders filtering...\n');

    const patientUserId = 'dfd17757-e959-473c-a507-0b946b82ab79'; // <EMAIL>
    console.log(`Patient User ID: ${patientUserId}\n`);

    // Check reminders for this patient
    const { data: reminders, error: remindersError } = await supabase
      .from('reminders')
      .select('*')
      .eq('patient_id', patientUserId);

    if (remindersError) {
      console.error('Error fetching reminders:', remindersError);
    } else {
      console.log(`📋 Reminders for patient: ${reminders?.length || 0}`);
      if (reminders && reminders.length > 0) {
        reminders.forEach((reminder, index) => {
          console.log(`${index + 1}. ID: ${reminder.id}`);
          console.log(`   Patient ID: ${reminder.patient_id}`);
          console.log(`   Medicine ID: ${reminder.medicine_id}`);
          console.log(`   Scheduled: ${new Date(reminder.scheduled_time).toLocaleString()}`);
          console.log(`   Status: ${reminder.status}`);
          console.log(`   Active: ${reminder.is_active}`);
          console.log('');
        });
      }
    }

    // Test the upcoming reminders logic
    const now = new Date();
    const futureTime = new Date(now.getTime() + 48 * 60 * 60 * 1000); // 48 hours from now

    console.log(`Current time: ${now.toLocaleString()}`);
    console.log(`Future time (48h): ${futureTime.toLocaleString()}\n`);

    const { data: upcomingReminders, error: upcomingError } = await supabase
      .from('reminders')
      .select('*')
      .eq('patient_id', patientUserId)
      .gte('scheduled_time', now.toISOString())
      .lte('scheduled_time', futureTime.toISOString())
      .eq('status', 'pending')
      .eq('is_active', true);

    if (upcomingError) {
      console.error('Error fetching upcoming reminders:', upcomingError);
    } else {
      console.log(`⏰ Upcoming reminders (48h): ${upcomingReminders?.length || 0}`);
      if (upcomingReminders && upcomingReminders.length > 0) {
        upcomingReminders.forEach((reminder, index) => {
          console.log(`${index + 1}. ID: ${reminder.id}`);
          console.log(`   Scheduled: ${new Date(reminder.scheduled_time).toLocaleString()}`);
          console.log(`   Status: ${reminder.status}`);
          console.log('');
        });
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

debugReminders();
