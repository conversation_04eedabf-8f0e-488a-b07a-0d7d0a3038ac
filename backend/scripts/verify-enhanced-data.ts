import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { SupabaseService } from '../src/config/supabase.service';

async function verifyEnhancedData() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const supabaseService = app.get(SupabaseService);
  const supabase = supabaseService.getAdminClient();

  console.log('🔍 Verifying enhanced database seeding results...\n');

  try {
    // Check patient count and sample data
    const { data: patients, error: patientsError } = await supabase
      .from('patients')
      .select('*, users!inner(name, email)')
      .limit(3);

    if (patientsError) {
      console.error('❌ Error fetching patients:', patientsError);
    } else {
      console.log(`👥 Sample Patients (${patients.length} shown):`);
      patients.forEach(patient => {
        console.log(`  📋 ${patient.users.name} (${patient.users.email})`);
        console.log(`     DOB: ${patient.date_of_birth}, Phone: ${patient.phone}`);
        console.log(`     Address: ${patient.address}`);
      });
    }

    // Check medicines count
    const { data: medicines, count: medicinesCount } = await supabase
      .from('medicines')
      .select('*, patients!inner(*, users!inner(name))', { count: 'exact' })
      .limit(5);

    console.log(`\n💊 Sample Medicines (${medicines?.length} shown, ${medicinesCount} total):`);
    medicines?.forEach(medicine => {
      console.log(`  💊 ${medicine.name} - ${medicine.dosage}`);
      console.log(`     Patient: ${medicine.patients.users.name}`);
      console.log(`     Frequency: ${medicine.frequency}, Instructions: ${medicine.instructions}`);
    });

    // Check adherence records count
    const { count: adherenceCount } = await supabase
      .from('adherence_records')
      .select('*', { count: 'exact' });

    console.log(`\n📊 Adherence Records: ${adherenceCount} total records`);

    // Check recent adherence data
    const { data: recentAdherence } = await supabase
      .from('adherence_records')
      .select(`
        *,
        medicines!inner(name),
        patients!inner(*, users!inner(name))
      `)
      .order('scheduled_time', { ascending: false })
      .limit(5);

    console.log(`\n📈 Recent Adherence Records:`);
    recentAdherence?.forEach(record => {
      const scheduledDate = new Date(record.scheduled_time).toLocaleDateString();
      const takenStatus = record.taken_time ? '✅ Taken' : '❌ Missed';
      console.log(`  ${scheduledDate} | ${record.patients.users.name} | ${record.medicines.name} | ${takenStatus}`);
    });

    // Check gamification stats
    const { data: gamificationStats } = await supabase
      .from('gamification_stats')
      .select(`
        *,
        patients!inner(*, users!inner(name))
      `)
      .order('total_points', { ascending: false })
      .limit(5);

    console.log(`\n🎮 Top Gamification Stats:`);
    gamificationStats?.forEach(stats => {
      console.log(`  🏆 ${stats.patients.users.name}`);
      console.log(`     Points: ${stats.total_points}, Level: ${stats.level}, Streak: ${stats.current_streak}`);
      console.log(`     Adherence Rate: ${stats.adherence_rate}%, Medicines Taken: ${stats.total_medicines_taken}`);
    });

    // Check reminders count
    const { count: remindersCount } = await supabase
      .from('reminders')
      .select('*', { count: 'exact' });

    console.log(`\n⏰ Reminders: ${remindersCount} total reminders created`);

    // Check doctors and hospitals
    const { count: doctorsCount } = await supabase
      .from('doctors')
      .select('*', { count: 'exact' });

    const { count: hospitalsCount } = await supabase
      .from('hospitals')
      .select('*', { count: 'exact' });

    const { count: insuranceCount } = await supabase
      .from('insurance_providers')
      .select('*', { count: 'exact' });

    console.log(`\n📊 Summary:`);
    console.log(`   👨‍⚕️ Doctors: ${doctorsCount}`);
    console.log(`   🏥 Hospitals: ${hospitalsCount}`);
    console.log(`   🏢 Insurance Providers: ${insuranceCount}`);
    console.log(`   💊 Medicines: ${medicinesCount}`);
    console.log(`   📊 Adherence Records: ${adherenceCount}`);
    console.log(`   ⏰ Reminders: ${remindersCount}`);

    console.log('\n✅ Enhanced database seeding verification completed!');

  } catch (error) {
    console.error('❌ Error during verification:', error);
  } finally {
    await app.close();
  }
}

verifyEnhancedData();
