import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { SupabaseService } from '../src/config/supabase.service';

async function simpleVerify() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const supabaseService = app.get(SupabaseService);
  const supabase = supabaseService.getAdminClient();

  console.log('🔍 Quick verification of enhanced seeding...\n');

  try {
    // Count records in each table
    const tables = [
      'users',
      'patients', 
      'doctors',
      'hospitals',
      'insurance_providers',
      'medicines',
      'adherence_records',
      'reminders',
      'gamification_stats'
    ];

    for (const table of tables) {
      const { count, error } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
      
      if (error) {
        console.log(`❌ Error counting ${table}:`, error.message);
      } else {
        console.log(`📊 ${table}: ${count} records`);
      }
    }

    console.log('\n✅ Verification completed!');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await app.close();
  }
}

simpleVerify();
