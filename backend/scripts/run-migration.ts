import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { SupabaseService } from '../src/config/supabase.service';

async function runMigration() {
  console.log('🔄 Running prescription fields migration...');

  try {
    const app = await NestFactory.createApplicationContext(AppModule);
    const supabaseService = app.get(SupabaseService);
    const supabase = supabaseService.getAdminClient();

    // First, let's check the current schema
    console.log('🔍 Checking current prescriptions table schema...');
    const { data: currentColumns, error: schemaError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable')
      .eq('table_name', 'prescriptions')
      .order('ordinal_position');

    if (schemaError) {
      console.error('❌ Error checking schema:', schemaError);
    } else {
      console.log('📋 Current prescriptions table columns:');
      console.table(currentColumns);
    }

    // Check if columns already exist
    const hasExtractedText = currentColumns?.some(col => col.column_name === 'extracted_text');
    const hasFilename = currentColumns?.some(col => col.column_name === 'filename');
    const hasUploadedAt = currentColumns?.some(col => col.column_name === 'uploaded_at');

    console.log(`📊 Column status: extracted_text=${hasExtractedText}, filename=${hasFilename}, uploaded_at=${hasUploadedAt}`);

    if (!hasExtractedText || !hasFilename || !hasUploadedAt) {
      console.log('⚠️  Missing columns detected. Manual database update required.');
      console.log('');
      console.log('🔧 Please run the following SQL in your Supabase dashboard:');
      console.log('');
      console.log('-- Add missing columns to prescriptions table');
      if (!hasExtractedText) {
        console.log('ALTER TABLE prescriptions ADD COLUMN IF NOT EXISTS extracted_text TEXT;');
      }
      if (!hasFilename) {
        console.log('ALTER TABLE prescriptions ADD COLUMN IF NOT EXISTS filename VARCHAR(255);');
      }
      if (!hasUploadedAt) {
        console.log('ALTER TABLE prescriptions ADD COLUMN IF NOT EXISTS uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();');
      }
      console.log('');
      console.log('-- Update existing records');
      console.log(`UPDATE prescriptions
SET
    filename = COALESCE(filename, 'unknown.pdf'),
    uploaded_at = COALESCE(uploaded_at, created_at)
WHERE filename IS NULL OR uploaded_at IS NULL;`);
      console.log('');
    } else {
      console.log('✅ All required columns are present!');
    }

    console.log('✅ Migration completed successfully!');

    await app.close();

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

runMigration();
