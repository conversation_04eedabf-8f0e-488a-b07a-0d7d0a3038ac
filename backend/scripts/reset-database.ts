import * as fs from 'fs';
import * as path from 'path';

async function bootstrap() {
  console.log('📋 Generating SQL commands for database reset...');

  try {
    // Generate drop statements
    const dropStatements = [
      '-- Drop all tables and types in reverse dependency order',
      'DROP TABLE IF EXISTS notifications CASCADE;',
      'DROP TABLE IF EXISTS claims CASCADE;',
      'DROP TABLE IF EXISTS gamification_stats CASCADE;',
      'DROP TABLE IF EXISTS user_achievements CASCADE;',
      'DROP TABLE IF EXISTS achievements CASCADE;',
      'DROP TABLE IF EXISTS reminder_logs CASCADE;',
      'DROP TABLE IF EXISTS reminders CASCADE;',
      'DROP TABLE IF EXISTS adherence_records CASCADE;',
      'DROP TABLE IF EXISTS medicines CASCADE;',
      'DROP TABLE IF EXISTS prescriptions CASCADE;',
      'DROP TABLE IF EXISTS insurance_providers CASCADE;',
      'DROP TABLE IF EXISTS hospitals CASCADE;',
      'DROP TABLE IF EXISTS doctors CASCADE;',
      'DROP TABLE IF EXISTS patients CASCADE;',
      'DROP TABLE IF EXISTS users CASCADE;',
      '',
      '-- Drop custom types',
      'DROP TYPE IF EXISTS notification_type CASCADE;',
      'DROP TYPE IF EXISTS claim_type CASCADE;',
      'DROP TYPE IF EXISTS claim_status CASCADE;',
      'DROP TYPE IF EXISTS achievement_category CASCADE;',
      'DROP TYPE IF EXISTS adherence_status CASCADE;',
      'DROP TYPE IF EXISTS recurrence_pattern CASCADE;',
      'DROP TYPE IF EXISTS reminder_log_status CASCADE;',
      'DROP TYPE IF EXISTS reminder_type CASCADE;',
      'DROP TYPE IF EXISTS reminder_status CASCADE;',
      'DROP TYPE IF EXISTS prescription_status CASCADE;',
      'DROP TYPE IF EXISTS user_role CASCADE;',
      '',
      '-- Now run the supabase-setup.sql file content below:',
      '',
    ];

    // Read the setup SQL file
    const setupPath = path.join(__dirname, '../supabase-setup.sql');
    const setupSQL = fs.readFileSync(setupPath, 'utf8');

    // Combine drop statements with setup SQL
    const fullSQL = dropStatements.join('\n') + setupSQL;

    // Write to a file for easy copy-paste
    const outputPath = path.join(__dirname, '../database-reset.sql');
    fs.writeFileSync(outputPath, fullSQL);

    console.log('✅ Database reset SQL generated!');
    console.log('📁 File saved to: database-reset.sql');
    console.log('');
    console.log('🔧 To reset your database:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste the content of database-reset.sql');
    console.log('4. Run the entire script');
    console.log('');
    console.log('⚠️  WARNING: This will delete ALL existing data!');

  } catch (error) {
    console.error('❌ Error generating reset script:', error);
  }
}

bootstrap();
