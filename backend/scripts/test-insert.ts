import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { SupabaseService } from '../src/config/supabase.service';

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);
  
  const supabaseService = app.get(SupabaseService);
  const supabase = supabaseService.getAdminClient();

  console.log('🧪 Testing table structures...');

  try {
    // Test achievements table
    console.log('\n🏆 Testing achievements table...');
    const { error: achievementError } = await supabase
      .from('achievements')
      .insert({
        name: 'Test Achievement',
        description: 'Test description',
        icon: 'test-icon',
        category: 'milestone',
        requirement: 1,
        points: 10,
      });

    if (achievementError) {
      console.log('❌ Achievement insert error:', achievementError.message);
    } else {
      console.log('✅ Achievement insert successful');
      // Clean up
      await supabase.from('achievements').delete().eq('name', 'Test Achievement');
    }

    // Test prescriptions table
    console.log('\n📋 Testing prescriptions table...');
    
    // First, get a patient ID
    const { data: patients } = await supabase
      .from('users')
      .select('id')
      .eq('role', 'patient')
      .limit(1);

    if (patients && patients.length > 0) {
      const { error: prescriptionError } = await supabase
        .from('prescriptions')
        .insert({
          patient_id: patients[0].id,
          filename: 'test-prescription.pdf',
          file_url: '/test/path',
          status: 'processing',
          extracted_text: 'Test extracted text',
        });

      if (prescriptionError) {
        console.log('❌ Prescription insert error:', prescriptionError.message);
      } else {
        console.log('✅ Prescription insert successful');
        // Clean up
        await supabase.from('prescriptions').delete().eq('filename', 'test-prescription.pdf');
      }
    }

    // Test gamification_stats table
    console.log('\n🎮 Testing gamification_stats table...');
    if (patients && patients.length > 0) {
      const { error: gamificationError } = await supabase
        .from('gamification_stats')
        .insert({
          patient_id: patients[0].id,
          current_streak: 0,
          longest_streak: 0,
          total_points: 0,
          completion_rate: 0.00,
          weekly_progress: [0, 0, 0, 0, 0, 0, 0],
          monthly_progress: Array(30).fill(0),
        });

      if (gamificationError) {
        console.log('❌ Gamification stats insert error:', gamificationError.message);
      } else {
        console.log('✅ Gamification stats insert successful');
        // Clean up
        await supabase.from('gamification_stats').delete().eq('patient_id', patients[0].id);
      }
    }
    
  } catch (error) {
    console.error('❌ Error testing database:', error);
  } finally {
    await app.close();
  }
}

bootstrap();
