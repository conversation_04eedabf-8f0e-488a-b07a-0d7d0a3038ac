import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { SupabaseService } from '../src/config/supabase.service';

async function checkUsers() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const supabaseService = app.get(SupabaseService);
  const supabase = supabaseService.getAdminClient();

  console.log('🔍 Checking existing users...');

  try {
    const { data: users, error } = await supabase
      .from('users')
      .select('email, name, role')
      .order('email');

    if (error) {
      console.error('❌ Error fetching users:', error);
      return;
    }

    console.log(`\n📊 Found ${users.length} existing users:`);
    users.forEach(user => {
      console.log(`  ${user.role.padEnd(10)} | ${user.email.padEnd(30)} | ${user.name}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await app.close();
  }
}

checkUsers();
