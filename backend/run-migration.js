const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  try {
    console.log('🚀 Starting doctor profile fields migration...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, 'migrations', 'add_doctor_profile_fields.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
        
        const { data, error } = await supabase.rpc('exec_sql', {
          sql: statement
        });
        
        if (error) {
          console.error(`❌ Error executing statement ${i + 1}:`, error);
          console.error('Statement:', statement);
          throw error;
        }
        
        console.log(`✅ Statement ${i + 1} executed successfully`);
      }
    }
    
    console.log('🎉 Migration completed successfully!');
    
    // Verify the migration by checking the doctors table structure
    console.log('🔍 Verifying migration...');
    const { data: doctors, error: verifyError } = await supabase
      .from('doctors')
      .select('*')
      .limit(1);
    
    if (verifyError) {
      console.error('❌ Error verifying migration:', verifyError);
    } else {
      console.log('✅ Migration verified - doctors table updated successfully');
      if (doctors && doctors.length > 0) {
        console.log('📊 Sample doctor record structure:', Object.keys(doctors[0]));
      }
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Alternative method using direct SQL execution if RPC doesn't work
async function runMigrationDirect() {
  try {
    console.log('🚀 Starting doctor profile fields migration (direct method)...');
    
    // Read the migration file
    const migrationPath = path.join(__dirname, 'migrations', 'add_doctor_profile_fields.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📝 Executing migration SQL...');
    
    // Try to execute the entire migration as one query
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
    
    console.log('🎉 Migration completed successfully!');
    console.log('📊 Migration result:', data);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.log('\n💡 Manual migration required. Please run the SQL in migrations/add_doctor_profile_fields.sql directly in your Supabase SQL editor.');
    process.exit(1);
  }
}

// Run the migration
if (process.argv.includes('--direct')) {
  runMigrationDirect();
} else {
  runMigration();
}
