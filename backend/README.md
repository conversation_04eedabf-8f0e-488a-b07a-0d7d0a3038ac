# MedCare Backend API

A comprehensive NestJS backend for the MedCare platform - a medicine adherence tracking system for patients, doctors, hospitals, and insurance providers.

## 🚀 Features

### ✅ Completed
- **Authentication & Authorization**: JWT-based auth with role-based access control
- **Database Schema**: Complete PostgreSQL schema with all entities
- **User Management**: Support for patients, doctors, hospitals, and insurance providers
- **API Documentation**: Swagger/OpenAPI documentation
- **Configuration Management**: Environment-based configuration
- **Rate Limiting**: Built-in request throttling
- **File Upload Support**: Static file serving setup
- **Sample Data**: Development seed data for testing

### 🚧 In Progress
- Core entity modules (Users, Medicines, Prescriptions)
- Reminders & scheduling system
- Adherence tracking & gamification
- External service integrations (Twilio, ElevenLabs)
- Claims & insurance features
- Real-time WebSocket notifications

## 🛠️ Tech Stack

- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth + JWT
- **Documentation**: Swagger/OpenAPI
- **External Services**:
  - T<PERSON><PERSON> (SMS & Voice)
  - ElevenLabs (Conversational AI)
- **Validation**: class-validator
- **Configuration**: @nestjs/config

## 📋 Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Supabase account and project
- Twilio account (for SMS/calls)
- ElevenLabs account (for AI conversations)

## ⚙️ Setup

1. **Clone and install dependencies**:
   ```bash
   cd backend
   npm install
   ```

2. **Environment Configuration**:
   ```bash
   cp .env.example .env
   ```

   Fill in your environment variables in `.env`

3. **Database Setup**:
   ```bash
   # Seed the database with schema and sample data
   npm run db:seed
   ```

4. **Start the development server**:
   ```bash
   npm run start:dev
   ```

## 📚 API Documentation

Once the server is running, visit:
- **API Base**: http://localhost:3001/api/v1
- **Swagger Docs**: http://localhost:3001/api/docs

## 🔐 Authentication

The API uses JWT tokens for authentication. All endpoints (except auth endpoints) require a valid Bearer token.

### User Roles
- **patient**: End users tracking their medications
- **doctor**: Healthcare providers managing patients
- **hospital**: Healthcare institutions
- **insurance**: Insurance providers managing claims
- **admin**: System administrators

### Auth Endpoints
- `POST /auth/register` - Register new user
- `POST /auth/login` - Login user
- `GET /auth/profile` - Get current user profile
- `POST /auth/logout` - Logout user

## 📊 Database Schema

The database includes the following main entities:
- **Users** (base table for all user types)
- **Patients, Doctors, Hospitals, Insurance Providers** (role-specific tables)
- **Prescriptions & Medicines** (medication management)
- **Reminders & Adherence Records** (tracking system)
- **Achievements & Gamification Stats** (engagement system)
- **Claims** (insurance management)
- **Notifications** (communication system)

## 🧪 Development

### Sample Data
The database seeding script creates sample users for testing:
- Patient: `<EMAIL>`
- Doctor: `<EMAIL>`
- Hospital: `<EMAIL>`
- Insurance: `<EMAIL>`

**Note**: These are development-only users. In production, all users should register through the API.

### Available Scripts
- `npm run start:dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run start:prod` - Start production server
- `npm run db:seed` - Seed database with schema and sample data
- `npm run test` - Run unit tests
- `npm run lint` - Run ESLint

## 🚀 Next Steps

1. **Core Entity Modules**: Implement CRUD operations for medicines, prescriptions, etc.
2. **Reminders System**: Build scheduled notification system
3. **Gamification**: Implement achievement and streak tracking
4. **External Integrations**: Connect Twilio and ElevenLabs services
5. **Real-time Features**: Add WebSocket support for live notifications
6. **Testing**: Add comprehensive unit and integration tests

## 📝 API Status

- ✅ Authentication endpoints
- 🚧 User management endpoints
- 🚧 Medicine management endpoints
- 🚧 Prescription management endpoints
- 🚧 Reminder endpoints
- 🚧 Adherence tracking endpoints
- 🚧 Gamification endpoints
- 🚧 Claims management endpoints
- 🚧 Notification endpoints
