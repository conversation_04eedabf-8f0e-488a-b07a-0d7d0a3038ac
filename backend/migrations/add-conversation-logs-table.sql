-- Create conversation_logs table for storing ElevenLabs conversation data
CREATE TABLE IF NOT EXISTS conversation_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    conversation_id VARCHAR(255) NOT NULL UNIQUE,
    agent_id VARCHAR(255) NOT NULL,
    patient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    medicine_id UUID REFERENCES medicines(id) ON DELETE CASCADE,
    reminder_id UUID REFERENCES reminders(id) ON DELETE SET NULL,
    transcript JSONB NOT NULL,
    call_duration_secs INTEGER NOT NULL DEFAULT 0,
    call_cost INTEGER NOT NULL DEFAULT 0, -- Cost in cents
    transcript_summary TEXT,
    call_successful VARCHAR(50) DEFAULT 'unknown',
    medicine_confirmed BOOLEAN NOT NULL DEFAULT false,
    confidence_score INTEGER NOT NULL DEFAULT 0, -- 0-100
    ai_reasoning TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_conversation_logs_patient_id ON conversation_logs(patient_id);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_medicine_id ON conversation_logs(medicine_id);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_reminder_id ON conversation_logs(reminder_id);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_conversation_id ON conversation_logs(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_created_at ON conversation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_conversation_logs_medicine_confirmed ON conversation_logs(medicine_confirmed);

-- Add RLS (Row Level Security) policies
ALTER TABLE conversation_logs ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own conversation logs
CREATE POLICY "Users can view own conversation logs" ON conversation_logs
    FOR SELECT USING (
        patient_id = auth.uid()
        OR EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role IN ('admin', 'doctor', 'hospital')
        )
    );

-- Policy: Only system can insert conversation logs (via service account)
CREATE POLICY "System can insert conversation logs" ON conversation_logs
    FOR INSERT WITH CHECK (true);

-- Policy: Only system can update conversation logs
CREATE POLICY "System can update conversation logs" ON conversation_logs
    FOR UPDATE USING (true);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_conversation_logs_updated_at 
    BEFORE UPDATE ON conversation_logs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE conversation_logs IS 'Stores conversation logs from ElevenLabs conversational AI calls';
COMMENT ON COLUMN conversation_logs.conversation_id IS 'Unique conversation ID from ElevenLabs';
COMMENT ON COLUMN conversation_logs.agent_id IS 'ElevenLabs agent ID used for the conversation';
COMMENT ON COLUMN conversation_logs.transcript IS 'Full conversation transcript as JSON array';
COMMENT ON COLUMN conversation_logs.call_duration_secs IS 'Duration of the call in seconds';
COMMENT ON COLUMN conversation_logs.call_cost IS 'Cost of the call in cents';
COMMENT ON COLUMN conversation_logs.medicine_confirmed IS 'Whether patient confirmed taking medicine';
COMMENT ON COLUMN conversation_logs.confidence_score IS 'AI confidence score (0-100) for medicine confirmation';
COMMENT ON COLUMN conversation_logs.ai_reasoning IS 'AI reasoning for the medicine confirmation decision';
