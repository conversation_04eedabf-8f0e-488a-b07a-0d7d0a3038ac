-- Migration: Add doctor profile fields
-- Description: Adds missing fields to doctors table for complete doctor profiles
-- Date: 2025-01-27

-- Add new columns to doctors table
ALTER TABLE doctors 
ADD COLUMN IF NOT EXISTS phone VARCHAR(50),
ADD COLUMN IF NOT EXISTS experience INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS patients_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS adherence_rate DECIMAL(5,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS rating DECIMAL(3,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS reviews_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS schedule VARCHAR(100) DEFAULT 'Full-time',
ADD COLUMN IF NOT EXISTS consultation_fee DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS qualifications TEXT[],
ADD COLUMN IF NOT EXISTS languages TEXT[],
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active';

-- Add check constraint for status field
ALTER TABLE doctors 
ADD CONSTRAINT doctors_status_check 
CHECK (status IN ('active', 'inactive', 'on-leave'));

-- Update existing doctors with default values and sample data
UPDATE doctors SET
    experience = FLOOR(RANDOM() * 20) + 1, -- Random experience 1-20 years
    patients_count = FLOOR(RANDOM() * 200) + 50, -- Random patient count 50-250
    adherence_rate = ROUND((RANDOM() * 20 + 80)::numeric, 2), -- Random adherence 80-100%
    rating = ROUND((RANDOM() * 1.5 + 3.5)::numeric, 2), -- Random rating 3.5-5.0
    reviews_count = FLOOR(RANDOM() * 300) + 50, -- Random reviews 50-350
    schedule = CASE 
        WHEN RANDOM() < 0.8 THEN 'Full-time'
        WHEN RANDOM() < 0.9 THEN 'Part-time'
        ELSE 'Contract'
    END,
    consultation_fee = ROUND((RANDOM() * 200 + 150)::numeric, 2), -- Random fee $150-350
    qualifications = ARRAY[
        'MD - ' || (ARRAY['Harvard Medical School', 'Johns Hopkins', 'Stanford University', 'Yale University', 'University of Pennsylvania'])[FLOOR(RANDOM() * 5) + 1],
        CASE 
            WHEN RANDOM() < 0.7 THEN 'Fellowship - ' || (ARRAY['Mayo Clinic', 'Cleveland Clinic', 'Mass General', 'UCSF', 'Mount Sinai'])[FLOOR(RANDOM() * 5) + 1]
            ELSE 'Residency - ' || (ARRAY['Johns Hopkins Hospital', 'Massachusetts General Hospital', 'Cleveland Clinic', 'UCSF Medical Center'])[FLOOR(RANDOM() * 4) + 1]
        END
    ],
    languages = CASE 
        WHEN RANDOM() < 0.3 THEN ARRAY['English']
        WHEN RANDOM() < 0.6 THEN ARRAY['English', (ARRAY['Spanish', 'French', 'German', 'Italian'])[FLOOR(RANDOM() * 4) + 1]]
        ELSE ARRAY['English', 'Spanish', (ARRAY['French', 'Portuguese', 'Mandarin', 'Arabic'])[FLOOR(RANDOM() * 4) + 1]]
    END,
    status = CASE 
        WHEN RANDOM() < 0.85 THEN 'active'
        WHEN RANDOM() < 0.95 THEN 'on-leave'
        ELSE 'inactive'
    END
WHERE experience IS NULL OR experience = 0;

-- Add phone numbers for existing doctors (sample format)
UPDATE doctors SET 
    phone = '+1 (555) ' || LPAD(FLOOR(RANDOM() * 900 + 100)::text, 3, '0') || '-' || LPAD(FLOOR(RANDOM() * 9000 + 1000)::text, 4, '0')
WHERE phone IS NULL;

-- Create index on status for better query performance
CREATE INDEX IF NOT EXISTS idx_doctors_status ON doctors(status);

-- Create index on hospital_id and status for hospital queries
CREATE INDEX IF NOT EXISTS idx_doctors_hospital_status ON doctors(hospital_id, status);

-- Create index on specialization for filtering
CREATE INDEX IF NOT EXISTS idx_doctors_specialization ON doctors(specialization);

-- Update updated_at timestamp
UPDATE doctors SET updated_at = NOW();

-- Add trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_doctors_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_doctors_updated_at ON doctors;
CREATE TRIGGER trigger_update_doctors_updated_at
    BEFORE UPDATE ON doctors
    FOR EACH ROW
    EXECUTE FUNCTION update_doctors_updated_at();

-- Verify the migration
SELECT 
    COUNT(*) as total_doctors,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_doctors,
    COUNT(CASE WHEN status = 'on-leave' THEN 1 END) as on_leave_doctors,
    COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_doctors,
    ROUND(AVG(experience), 1) as avg_experience,
    ROUND(AVG(patients_count), 0) as avg_patients,
    ROUND(AVG(adherence_rate), 2) as avg_adherence_rate,
    ROUND(AVG(rating), 2) as avg_rating
FROM doctors;
