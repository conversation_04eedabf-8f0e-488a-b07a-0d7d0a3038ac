-- Migration: Add missing prescription fields
-- Description: Adds missing fields to prescriptions table for OCR functionality
-- Date: 2025-06-30

-- Add missing columns to prescriptions table
ALTER TABLE prescriptions 
ADD COLUMN IF NOT EXISTS extracted_text TEXT,
ADD COLUMN IF NOT EXISTS filename VA<PERSON>HAR(255),
ADD COLUMN IF NOT EXISTS uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update existing records to have default values
UPDATE prescriptions 
SET 
    filename = COALESCE(filename, 'unknown.pdf'),
    uploaded_at = COALESCE(uploaded_at, created_at)
WHERE filename IS NULL OR uploaded_at IS NULL;

-- Make filename NOT NULL after setting defaults
ALTER TABLE prescriptions 
ALTER COLUMN filename SET NOT NULL;

-- Add comment for documentation
COMMENT ON COLUMN prescriptions.extracted_text IS 'Text extracted from prescription using AWS Textract';
COMMENT ON COLUMN prescriptions.filename IS 'Original filename of uploaded prescription';
COMMENT ON COLUMN prescriptions.uploaded_at IS 'Timestamp when prescription was uploaded';

-- Verification query
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'prescriptions' 
AND column_name IN ('extracted_text', 'filename', 'uploaded_at')
ORDER BY column_name;
