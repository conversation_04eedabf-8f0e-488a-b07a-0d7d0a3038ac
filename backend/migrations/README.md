# Database Migrations

## Doctor Profile Fields Migration

### Overview
This migration adds comprehensive doctor profile fields to support the hospital doctors management feature.

### Fields Added
- `phone` - Doctor's phone number
- `experience` - Years of experience
- `patients_count` - Number of patients
- `adherence_rate` - Patient adherence rate percentage
- `rating` - Doctor rating (1-5)
- `reviews_count` - Number of reviews
- `schedule` - Work schedule (Full-time, Part-time, Contract)
- `consultation_fee` - Consultation fee amount
- `qualifications` - Array of qualifications
- `languages` - Array of languages spoken
- `status` - Doctor status (active, inactive, on-leave)

### How to Run the Migration

#### Option 1: Supabase Dashboard (Recommended)
1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `add_doctor_profile_fields.sql`
4. Click "Run" to execute the migration

#### Option 2: Command Line (if you have direct database access)
```bash
psql -h your-db-host -U your-username -d your-database -f migrations/add_doctor_profile_fields.sql
```

### What the Migration Does
1. **Adds new columns** to the `doctors` table with appropriate data types and defaults
2. **Adds constraints** for data validation (status check constraint)
3. **Populates existing records** with realistic sample data
4. **Creates indexes** for better query performance
5. **Adds triggers** for automatic timestamp updates
6. **Provides verification** query to confirm successful migration

### Sample Data Generated
The migration automatically generates realistic sample data for existing doctors:
- Random experience levels (1-20 years)
- Random patient counts (50-250 patients)
- Random adherence rates (80-100%)
- Random ratings (3.5-5.0 stars)
- Random consultation fees ($150-350)
- Realistic qualifications from top medical schools
- Multiple language combinations
- Appropriate status distribution (85% active, 10% on-leave, 5% inactive)

### Verification
After running the migration, you can verify it worked by running:
```sql
SELECT 
    COUNT(*) as total_doctors,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_doctors,
    ROUND(AVG(experience), 1) as avg_experience,
    ROUND(AVG(rating), 2) as avg_rating
FROM doctors;
```

### Rollback (if needed)
If you need to rollback this migration:
```sql
ALTER TABLE doctors 
DROP COLUMN IF EXISTS phone,
DROP COLUMN IF EXISTS experience,
DROP COLUMN IF EXISTS patients_count,
DROP COLUMN IF EXISTS adherence_rate,
DROP COLUMN IF EXISTS rating,
DROP COLUMN IF EXISTS reviews_count,
DROP COLUMN IF EXISTS schedule,
DROP COLUMN IF EXISTS consultation_fee,
DROP COLUMN IF EXISTS qualifications,
DROP COLUMN IF EXISTS languages,
DROP COLUMN IF EXISTS status;

DROP INDEX IF EXISTS idx_doctors_status;
DROP INDEX IF EXISTS idx_doctors_hospital_status;
DROP INDEX IF EXISTS idx_doctors_specialization;
DROP TRIGGER IF EXISTS trigger_update_doctors_updated_at ON doctors;
DROP FUNCTION IF EXISTS update_doctors_updated_at();
```

### Next Steps
After running this migration:
1. The hospital doctors management feature will have access to complete doctor profiles
2. The frontend will display rich doctor information including ratings, experience, and patient counts
3. Filtering and sorting by status, specialization, and other fields will work properly
4. The API will return properly structured doctor data matching the `HospitalDoctor` interface
