-- Migration: Fix RLS policies for medicines table
-- Date: 2025-06-30
-- Description: Add proper RLS policies for medicines table to allow insertion and access

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "medicines_select_policy" ON medicines;
DROP POLICY IF EXISTS "medicines_insert_policy" ON medicines;
DROP POLICY IF EXISTS "medicines_update_policy" ON medicines;
DROP POLICY IF EXISTS "medicines_delete_policy" ON medicines;

-- Create comprehensive RLS policies for medicines table
-- Allow patients to view their own medicines
CREATE POLICY "medicines_select_policy" ON medicines
    FOR SELECT USING (
        auth.uid() = patient_id::uuid OR
        auth.uid() IN (
            SELECT d.id::uuid FROM doctors d
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = medicines.patient_id
        ) OR
        auth.uid() IN (
            SELECT h.id::uuid FROM hospitals h
            JOIN doctors d ON d.hospital_id = h.id
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = medicines.patient_id
        ) OR
        auth.uid() IN (
            SELECT ip.id::uuid FROM insurance_providers ip
            JOIN patients p ON p.insurance_provider_id = ip.id
            WHERE p.id = medicines.patient_id
        )
    );

-- Allow patients, doctors, hospitals, and admins to insert medicines
CREATE POLICY "medicines_insert_policy" ON medicines
    FOR INSERT WITH CHECK (
        auth.uid() = patient_id::uuid OR
        auth.uid() IN (
            SELECT d.id::uuid FROM doctors d
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = medicines.patient_id
        ) OR
        auth.uid() IN (
            SELECT h.id::uuid FROM hospitals h
            JOIN doctors d ON d.hospital_id = h.id
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = medicines.patient_id
        ) OR
        EXISTS (
            SELECT 1 FROM users u
            WHERE u.id::uuid = auth.uid() AND u.role = 'admin'
        )
    );

-- Allow patients and their doctors to update medicines
CREATE POLICY "medicines_update_policy" ON medicines
    FOR UPDATE USING (
        auth.uid() = patient_id::uuid OR
        auth.uid() IN (
            SELECT d.id::uuid FROM doctors d
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = medicines.patient_id
        ) OR
        EXISTS (
            SELECT 1 FROM users u
            WHERE u.id::uuid = auth.uid() AND u.role = 'admin'
        )
    );

-- Allow patients and their doctors to delete medicines
CREATE POLICY "medicines_delete_policy" ON medicines
    FOR DELETE USING (
        auth.uid() = patient_id::uuid OR
        auth.uid() IN (
            SELECT d.id::uuid FROM doctors d
            JOIN patients p ON p.assigned_doctor_id = d.id
            WHERE p.id = medicines.patient_id
        ) OR
        EXISTS (
            SELECT 1 FROM users u
            WHERE u.id::uuid = auth.uid() AND u.role = 'admin'
        )
    );
