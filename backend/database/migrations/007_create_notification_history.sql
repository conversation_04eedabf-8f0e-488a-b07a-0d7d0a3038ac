-- Create notification_history table for storing notification records
CREATE TABLE IF NOT EXISTS notification_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_role VARCHAR(20) NOT NULL,
    data JSONB,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    read BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_notification_history_user_id ON notification_history(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_history_type ON notification_history(type);
CREATE INDEX IF NOT EXISTS idx_notification_history_timestamp ON notification_history(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_notification_history_read ON notification_history(read);
CREATE INDEX IF NOT EXISTS idx_notification_history_user_role ON notification_history(user_role);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_notification_history_user_read ON notification_history(user_id, read);
CREATE INDEX IF NOT EXISTS idx_notification_history_user_timestamp ON notification_history(user_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_notification_history_role_timestamp ON notification_history(user_role, timestamp DESC);

-- Add RLS (Row Level Security) policies
ALTER TABLE notification_history ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own notifications
CREATE POLICY notification_history_user_policy ON notification_history
    FOR ALL USING (user_id = auth.uid());

-- Policy: Admins can see all notifications
CREATE POLICY notification_history_admin_policy ON notification_history
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND users.role = 'admin'
        )
    );

-- Policy: Doctors can see notifications for their patients
CREATE POLICY notification_history_doctor_policy ON notification_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users u
            JOIN doctors d ON u.id = d.id
            JOIN patients p ON d.id = p.assigned_doctor_id
            WHERE u.id = auth.uid() 
            AND u.role = 'doctor'
            AND p.id = notification_history.user_id
        )
    );

-- Policy: Hospital staff can see notifications for their patients
CREATE POLICY notification_history_hospital_policy ON notification_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users u
            JOIN hospitals h ON u.id = h.id
            JOIN doctors d ON h.id = d.hospital_id
            JOIN patients p ON d.id = p.assigned_doctor_id
            WHERE u.id = auth.uid() 
            AND u.role = 'hospital'
            AND p.id = notification_history.user_id
        )
    );

-- Policy: Insurance providers can see notifications for their policy holders
CREATE POLICY notification_history_insurance_policy ON notification_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users u
            JOIN insurance_providers ip ON u.id = ip.id
            JOIN patients p ON ip.id = p.insurance_id
            WHERE u.id = auth.uid()
            AND u.role = 'insurance'
            AND p.id = notification_history.user_id
        )
    );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_notification_history_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER notification_history_updated_at_trigger
    BEFORE UPDATE ON notification_history
    FOR EACH ROW
    EXECUTE FUNCTION update_notification_history_updated_at();

-- Add comments for documentation
COMMENT ON TABLE notification_history IS 'Stores notification history for all users';
COMMENT ON COLUMN notification_history.id IS 'Unique identifier for the notification';
COMMENT ON COLUMN notification_history.type IS 'Type of notification (reminder:due, achievement:unlocked, etc.)';
COMMENT ON COLUMN notification_history.title IS 'Notification title';
COMMENT ON COLUMN notification_history.message IS 'Notification message content';
COMMENT ON COLUMN notification_history.user_id IS 'ID of the user who received the notification';
COMMENT ON COLUMN notification_history.user_role IS 'Role of the user who received the notification';
COMMENT ON COLUMN notification_history.data IS 'Additional notification data in JSON format';
COMMENT ON COLUMN notification_history.timestamp IS 'When the notification was created';
COMMENT ON COLUMN notification_history.read IS 'Whether the notification has been read by the user';
COMMENT ON COLUMN notification_history.created_at IS 'Record creation timestamp';
COMMENT ON COLUMN notification_history.updated_at IS 'Record last update timestamp';
