-- Sample achievements data
INSERT INTO achievements (name, description, icon, category, requirement, points) VALUES
('First Steps', 'Take your first medication on time', '🎯', 'milestone', 1, 10),
('Consistency Champion', 'Take medications on time for 3 consecutive days', '🏆', 'streak', 3, 25),
('Week Warrior', 'Maintain a 7-day medication streak', '⚡', 'streak', 7, 50),
('Monthly Master', 'Complete 30 days of consistent medication taking', '👑', 'streak', 30, 150),
('Perfect Week', 'Achieve 100% adherence for a full week', '💯', 'consistency', 7, 75),
('Dedication Award', 'Take 50 medications on time', '🎖️', 'completion', 50, 100),
('Century Club', 'Take 100 medications on time', '💪', 'completion', 100, 200),
('Streak Starter', 'Start your first medication streak', '🚀', 'milestone', 1, 15),
('Early Bird', 'Take morning medication before 8 AM for 5 days', '🌅', 'consistency', 5, 40),
('Night Owl', 'Take evening medication after 8 PM for 5 days', '🌙', 'consistency', 5, 40),
('Reminder Master', 'Respond to 20 medication reminders', '🔔', 'completion', 20, 60),
('Health Hero', 'Complete your first full prescription cycle', '🦸', 'milestone', 1, 80),
('Consistency King', 'Maintain 90% adherence for a month', '👑', 'consistency', 30, 120),
('Streak Legend', 'Achieve a 60-day medication streak', '🔥', 'streak', 60, 300),
('Platinum Performer', 'Take 500 medications on time', '💎', 'completion', 500, 500);

-- Sample users data (for testing - these would normally be created through registration)
-- Note: In production, these would be created through the auth endpoints
-- This is just for development/testing purposes

-- Sample patient user
INSERT INTO users (id, email, name, role, avatar) VALUES
('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', 'John Patient', 'patient', null);

INSERT INTO patients (id, date_of_birth, emergency_contact) VALUES
('550e8400-e29b-41d4-a716-446655440000', '1990-05-15', '+**********');

INSERT INTO gamification_stats (patient_id) VALUES
('550e8400-e29b-41d4-a716-446655440000');

-- Sample doctor user
INSERT INTO users (id, email, name, role, avatar) VALUES
('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'Dr. Sarah Smith', 'doctor', null);

INSERT INTO doctors (id, specialization, license_number) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'General Medicine', 'MD123456789');

-- Sample hospital user
INSERT INTO users (id, email, name, role, avatar) VALUES
('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', 'City General Hospital', 'hospital', null);

INSERT INTO hospitals (id, address, phone, website) VALUES
('550e8400-e29b-41d4-a716-446655440002', '123 Medical Center Dr, Healthcare City, HC 12345', '******-HOSPITAL', 'https://citygeneral.com');

-- Sample insurance provider user
INSERT INTO users (id, email, name, role, avatar) VALUES
('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'HealthCare Plus Insurance', 'insurance', null);

INSERT INTO insurance_providers (id, company_name, address, phone, website, policy_types, coverage_areas) VALUES
('550e8400-e29b-41d4-a716-446655440003', 'HealthCare Plus Insurance', '456 Insurance Blvd, Coverage City, CC 67890', '******-INSURE', 'https://healthcareplus.com', ARRAY['Health', 'Dental', 'Vision', 'Prescription'], ARRAY['New York', 'California', 'Texas', 'Florida']);

-- Update patient with doctor and insurance assignments
UPDATE patients SET 
  assigned_doctor_id = '550e8400-e29b-41d4-a716-446655440001',
  insurance_id = '550e8400-e29b-41d4-a716-446655440003'
WHERE id = '550e8400-e29b-41d4-a716-446655440000';

-- Update doctor with hospital assignment
UPDATE doctors SET 
  hospital_id = '550e8400-e29b-41d4-a716-446655440002'
WHERE id = '550e8400-e29b-41d4-a716-446655440001';

-- Sample prescription
INSERT INTO prescriptions (id, patient_id, doctor_id, filename, file_url, status, extracted_text) VALUES
('660e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440001', 'prescription_001.pdf', '/uploads/prescriptions/prescription_001.pdf', 'completed', 'Amoxicillin 500mg - Take 1 tablet twice daily for 7 days. Ibuprofen 200mg - Take 1 tablet as needed for pain, maximum 3 times daily.');

-- Sample medicines
INSERT INTO medicines (id, name, dosage, frequency, duration, instructions, side_effects, start_date, end_date, prescription_id, patient_id) VALUES
('770e8400-e29b-41d4-a716-446655440000', 'Amoxicillin', '500mg', 'Twice daily', 7, 'Take with food to reduce stomach upset', 'May cause nausea, diarrhea, or stomach upset', '2025-06-29', '2025-07-05', '660e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440000'),
('770e8400-e29b-41d4-a716-446655440001', 'Ibuprofen', '200mg', 'As needed (max 3x daily)', 30, 'Take with food or milk. Do not exceed 3 tablets per day', 'May cause stomach irritation, dizziness', '2025-06-29', '2025-07-28', '660e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440000');

-- Sample reminders
INSERT INTO reminders (id, medicine_id, patient_id, scheduled_time, reminder_type, status) VALUES
('880e8400-e29b-41d4-a716-446655440000', '770e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440000', '2025-06-29 08:00:00+00', 'notification', 'pending'),
('880e8400-e29b-41d4-a716-446655440001', '770e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440000', '2025-06-29 20:00:00+00', 'notification', 'pending');

-- Sample adherence records
INSERT INTO adherence_records (id, patient_id, medicine_id, scheduled_time, taken_time, status, notes) VALUES
('990e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440000', '770e8400-e29b-41d4-a716-446655440000', '2025-06-28 08:00:00+00', '2025-06-28 08:15:00+00', 'taken', 'Taken with breakfast'),
('990e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440000', '770e8400-e29b-41d4-a716-446655440000', '2025-06-28 20:00:00+00', '2025-06-28 20:30:00+00', 'taken', 'Taken with dinner');

-- Sample user achievements
INSERT INTO user_achievements (user_id, achievement_id) VALUES
('550e8400-e29b-41d4-a716-446655440000', (SELECT id FROM achievements WHERE name = 'First Steps')),
('550e8400-e29b-41d4-a716-446655440000', (SELECT id FROM achievements WHERE name = 'Streak Starter'));

-- Update gamification stats
UPDATE gamification_stats SET
  current_streak = 2,
  longest_streak = 2,
  total_points = 25,
  completion_rate = 100.00,
  weekly_progress = ARRAY[2, 0, 0, 0, 0, 0, 0],
  monthly_progress = ARRAY[2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
WHERE patient_id = '550e8400-e29b-41d4-a716-446655440000';

-- Sample notifications
INSERT INTO notifications (id, user_id, title, message, type, read, data) VALUES
('aa0e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440000', 'Medication Reminder', 'Time to take your Amoxicillin (500mg)', 'info', false, '{"medicine_id": "770e8400-e29b-41d4-a716-446655440000", "reminder_id": "880e8400-e29b-41d4-a716-446655440000"}'),
('aa0e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440000', 'Achievement Unlocked!', 'Congratulations! You unlocked the "First Steps" achievement', 'success', false, '{"achievement_id": "' || (SELECT id FROM achievements WHERE name = 'First Steps') || '"}');

-- Sample claim
INSERT INTO claims (id, patient_id, insurance_id, hospital_id, doctor_id, claim_number, submission_date, service_date, amount, status, type, description, documents) VALUES
('bb0e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 'CLM-2025-001', '2025-06-29', '2025-06-28', 125.50, 'pending', 'medication', 'Prescription medication claim for Amoxicillin and Ibuprofen', ARRAY['/uploads/claims/prescription_001.pdf', '/uploads/claims/receipt_001.pdf']);
