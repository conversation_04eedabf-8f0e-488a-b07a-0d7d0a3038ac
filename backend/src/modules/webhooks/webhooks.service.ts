import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SupabaseService } from '../../config/supabase.service';
import { OpenAiService } from '../../common/services/openai.service';
import { AdherenceService } from '../adherence/adherence.service';
import { AdherenceStatus } from '../adherence/dto';
import * as crypto from 'crypto';

export interface ElevenLabsWebhookPayload {
  type: string;
  event_timestamp: number;
  data: {
    conversation_id: string;
    agent_id: string;
    status: string;
    transcript: Array<{
      role: string;
      message: string;
      time_in_call_secs: number;
    }>;
    analysis: {
      transcript_summary: string;
      call_successful: string;
    };
    metadata: {
      start_time_unix_secs: number;
      call_duration_secs: number;
      cost: number;
    };
    conversation_initiation_client_data?: {
      dynamic_variables?: {
        user_name?: string;
        patient_id?: string;
        medicine_id?: string;
        reminder_id?: string;
        medicine_name?: string;
        dosage?: string;
      };
    };
  };
}

@Injectable()
export class WebhooksService {
  private readonly logger = new Logger(WebhooksService.name);
  private readonly webhookSecret: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly supabaseService: SupabaseService,
    private readonly openAiService: OpenAiService,
    private readonly adherenceService: AdherenceService,
  ) {
    this.webhookSecret = this.configService.get<string>('ELEVENLABS_WEBHOOK_SECRET') || '';
    if (!this.webhookSecret) {
      this.logger.warn('ElevenLabs webhook secret not configured - webhook validation disabled');
    }
  }

  /**
   * Validate ElevenLabs webhook signature using HMAC
   */
  validateWebhookSignature(
    payload: string,
    signature: string,
    timestamp: string
  ): boolean {
    if (!this.webhookSecret) {
      this.logger.warn('Webhook secret not configured - skipping validation');
      return true; // Allow for development/testing
    }

    try {
      // Parse signature header: "t=timestamp,v0=hash"
      const parts = signature.split(',');
      const timestampPart = parts.find(part => part.startsWith('t='));
      const hashPart = parts.find(part => part.startsWith('v0='));

      if (!timestampPart || !hashPart) {
        this.logger.error('Invalid signature format');
        return false;
      }

      const receivedTimestamp = timestampPart.split('=')[1];
      const receivedHash = hashPart.split('=')[1];

      // Validate timestamp (within 5 minutes)
      const currentTime = Math.floor(Date.now() / 1000);
      const timestampDiff = currentTime - parseInt(receivedTimestamp);
      if (timestampDiff > 300) { // 5 minutes
        this.logger.error('Webhook timestamp too old');
        return false;
      }

      // Validate HMAC signature
      const fullPayloadToSign = `${receivedTimestamp}.${payload}`;
      const expectedHash = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(fullPayloadToSign)
        .digest('hex');

      const isValid = crypto.timingSafeEqual(
        Buffer.from(receivedHash, 'hex'),
        Buffer.from(expectedHash, 'hex')
      );

      if (!isValid) {
        this.logger.error('Invalid webhook signature');
      }

      return isValid;
    } catch (error) {
      this.logger.error('Error validating webhook signature:', error);
      return false;
    }
  }

  /**
   * Process ElevenLabs post-call webhook
   */
  async processElevenLabsWebhook(payload: ElevenLabsWebhookPayload): Promise<{
    success: boolean;
    message: string;
    adherenceRecordCreated?: boolean;
    error?: string;
  }> {
    try {
      this.logger.log(`Processing ElevenLabs webhook for conversation: ${payload.data.conversation_id}`);

      // Validate payload structure
      if (payload.type !== 'post_call_transcription') {
        return {
          success: false,
          message: `Unsupported webhook type: ${payload.type}`
        };
      }

      if (!payload.data.transcript || payload.data.transcript.length === 0) {
        return {
          success: false,
          message: 'No transcript data in webhook payload'
        };
      }

      // Extract conversation metadata
      const conversationData = payload.data;
      const dynamicVars = conversationData.conversation_initiation_client_data?.dynamic_variables;

      if (!dynamicVars?.patient_id || !dynamicVars?.medicine_id || !dynamicVars?.reminder_id) {
        this.logger.warn('Missing required dynamic variables in conversation data');
        return {
          success: true,
          message: 'Conversation processed but missing metadata for adherence tracking',
          adherenceRecordCreated: false
        };
      }

      // Analyze conversation for medicine intake confirmation
      const analysis = await this.openAiService.analyzeConversationForMedicineIntake(
        conversationData.transcript,
        dynamicVars.user_name || 'Patient',
        dynamicVars.medicine_name || 'Medicine',
        dynamicVars.dosage || 'Unknown dosage'
      );

      if (!analysis.success) {
        this.logger.error('Failed to analyze conversation:', analysis.error);
        return {
          success: false,
          message: 'Failed to analyze conversation',
          error: analysis.error
        };
      }

      this.logger.log(`Conversation analysis result: ${analysis.medicineConfirmed ? 'CONFIRMED' : 'NOT CONFIRMED'} (${analysis.confidence}% confidence)`);

      // Store conversation log
      await this.storeConversationLog(payload, analysis);

      // Create adherence record if medicine intake was confirmed
      let adherenceRecordCreated = false;
      if (analysis.medicineConfirmed && analysis.confidence >= 70) {
        try {
          await this.createAdherenceRecord(
            dynamicVars.patient_id,
            dynamicVars.medicine_id,
            dynamicVars.reminder_id,
            conversationData,
            analysis
          );
          adherenceRecordCreated = true;
          this.logger.log(`Adherence record created for patient ${dynamicVars.patient_id}`);
        } catch (error) {
          this.logger.error('Failed to create adherence record:', error);
          // Don't fail the webhook processing if adherence record creation fails
        }
      }

      return {
        success: true,
        message: `Conversation processed successfully. Medicine intake ${analysis.medicineConfirmed ? 'confirmed' : 'not confirmed'}.`,
        adherenceRecordCreated
      };

    } catch (error) {
      this.logger.error('Error processing ElevenLabs webhook:', error);
      return {
        success: false,
        message: 'Internal error processing webhook',
        error: error.message
      };
    }
  }

  /**
   * Store conversation log in database
   */
  private async storeConversationLog(
    payload: ElevenLabsWebhookPayload,
    analysis: any
  ): Promise<void> {
    const supabase = this.supabaseService.getAdminClient();
    
    const { error } = await supabase
      .from('conversation_logs')
      .insert({
        conversation_id: payload.data.conversation_id,
        agent_id: payload.data.agent_id,
        patient_id: payload.data.conversation_initiation_client_data?.dynamic_variables?.patient_id,
        medicine_id: payload.data.conversation_initiation_client_data?.dynamic_variables?.medicine_id,
        reminder_id: payload.data.conversation_initiation_client_data?.dynamic_variables?.reminder_id,
        transcript: payload.data.transcript,
        call_duration_secs: payload.data.metadata.call_duration_secs,
        call_cost: payload.data.metadata.cost,
        transcript_summary: payload.data.analysis.transcript_summary,
        call_successful: payload.data.analysis.call_successful,
        medicine_confirmed: analysis.medicineConfirmed,
        confidence_score: analysis.confidence,
        ai_reasoning: analysis.reasoning,
        created_at: new Date().toISOString(),
      });

    if (error) {
      this.logger.error('Failed to store conversation log:', error);
      throw new Error(`Failed to store conversation log: ${error.message}`);
    }
  }

  /**
   * Create adherence record based on confirmed medicine intake
   */
  private async createAdherenceRecord(
    patientId: string,
    medicineId: string,
    reminderId: string,
    conversationData: any,
    analysis: any
  ): Promise<void> {
    const supabase = this.supabaseService.getAdminClient();

    // Get reminder details to determine scheduled time
    const { data: reminder, error: reminderError } = await supabase
      .from('reminders')
      .select('scheduled_time')
      .eq('id', reminderId)
      .single();

    if (reminderError) {
      throw new Error(`Failed to get reminder details: ${reminderError.message}`);
    }

    // Get user details for adherence service
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, email, name, role, created_at, updated_at')
      .eq('id', patientId)
      .single();

    if (userError) {
      throw new Error(`Failed to get user details: ${userError.message}`);
    }

    // Create adherence record
    const adherenceData = {
      patient_id: patientId,
      medicine_id: medicineId,
      scheduled_time: reminder.scheduled_time,
      taken_time: new Date().toISOString(), // Current time as taken time
      status: AdherenceStatus.TAKEN,
      notes: `Confirmed via AI conversation (${analysis.confidence}% confidence): ${analysis.reasoning}`,
    };

    // Use the existing adherence service to create the record
    // This will also trigger gamification updates
    await this.adherenceService.create(adherenceData, user);
  }
}
