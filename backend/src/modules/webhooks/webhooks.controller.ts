import { 
  Controller, 
  Post, 
  Body, 
  Headers, 
  HttpStatus, 
  HttpException,
  Logger,
  RawBodyRequest,
  Req
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiHeader } from '@nestjs/swagger';
import { WebhooksService, ElevenLabsWebhookPayload } from './webhooks.service';
import { Request } from 'express';

@ApiTags('webhooks')
@Controller('webhooks')
export class WebhooksController {
  private readonly logger = new Logger(WebhooksController.name);

  constructor(private readonly webhooksService: WebhooksService) {}

  @Post('elevenlabs')
  @ApiOperation({ 
    summary: 'ElevenLabs post-call webhook',
    description: 'Receives post-call webhook from ElevenLabs conversational AI with conversation transcript and analysis'
  })
  @ApiHeader({
    name: 'elevenlabs-signature',
    description: 'HMAC signature for webhook validation',
    required: true,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Webhook processed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' },
        adherenceRecordCreated: { type: 'boolean' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid webhook payload or signature',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Invalid webhook signature',
  })
  async handleElevenLabsWebhook(
    @Req() req: RawBodyRequest<Request>,
    @Headers('elevenlabs-signature') signature: string,
    @Headers('x-timestamp') timestamp: string,
    @Body() payload: ElevenLabsWebhookPayload,
  ) {
    try {
      this.logger.log('Received ElevenLabs webhook');

      // Get raw body for signature validation
      const rawBody = req.rawBody?.toString() || JSON.stringify(payload);

      // Validate webhook signature
      if (signature) {
        const isValidSignature = this.webhooksService.validateWebhookSignature(
          rawBody,
          signature,
          timestamp
        );

        if (!isValidSignature) {
          this.logger.error('Invalid webhook signature');
          throw new HttpException('Invalid webhook signature', HttpStatus.UNAUTHORIZED);
        }
      } else {
        this.logger.warn('No signature provided for webhook validation');
      }

      // Process the webhook
      const result = await this.webhooksService.processElevenLabsWebhook(payload);

      if (!result.success) {
        this.logger.error(`Webhook processing failed: ${result.message}`);
        throw new HttpException(
          result.message || 'Failed to process webhook',
          HttpStatus.BAD_REQUEST
        );
      }

      this.logger.log(`Webhook processed successfully: ${result.message}`);
      return {
        success: true,
        message: result.message,
        adherenceRecordCreated: result.adherenceRecordCreated || false,
      };

    } catch (error) {
      this.logger.error('Error handling ElevenLabs webhook:', error);
      
      if (error instanceof HttpException) {
        throw error;
      }

      throw new HttpException(
        'Internal server error processing webhook',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('test/elevenlabs')
  @ApiOperation({ 
    summary: 'Test ElevenLabs webhook processing',
    description: 'Test endpoint for ElevenLabs webhook processing with sample data'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Test webhook processed successfully',
  })
  async testElevenLabsWebhook() {
    // Sample webhook payload for testing
    const testPayload: ElevenLabsWebhookPayload = {
      type: 'post_call_transcription',
      event_timestamp: Math.floor(Date.now() / 1000),
      data: {
        conversation_id: 'test-conversation-123',
        agent_id: 'test-agent-456',
        status: 'done',
        transcript: [
          {
            role: 'agent',
            message: 'Hello! This is a reminder to take your medication. Have you taken your Aspirin today?',
            time_in_call_secs: 0,
          },
          {
            role: 'user',
            message: 'Yes, I took it this morning with breakfast.',
            time_in_call_secs: 5,
          },
          {
            role: 'agent',
            message: 'Great! Thank you for taking your medication as prescribed. Have a wonderful day!',
            time_in_call_secs: 8,
          },
        ],
        analysis: {
          transcript_summary: 'Patient confirmed taking their Aspirin medication as prescribed.',
          call_successful: 'success',
        },
        metadata: {
          start_time_unix_secs: Math.floor(Date.now() / 1000) - 30,
          call_duration_secs: 15,
          cost: 150,
        },
        conversation_initiation_client_data: {
          dynamic_variables: {
            user_name: 'Test Patient',
            patient_id: 'test-patient-123',
            medicine_id: 'test-medicine-456',
            reminder_id: 'test-reminder-789',
            medicine_name: 'Aspirin',
            dosage: '100mg',
          },
        },
      },
    };

    this.logger.log('Processing test ElevenLabs webhook');
    const result = await this.webhooksService.processElevenLabsWebhook(testPayload);

    return {
      success: true,
      message: 'Test webhook processed',
      result,
    };
  }
}
