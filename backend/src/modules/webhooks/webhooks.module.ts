import { Module } from '@nestjs/common';
import { WebhooksController } from './webhooks.controller';
import { WebhooksService } from './webhooks.service';
import { SupabaseService } from '../../config/supabase.service';
import { OpenAiService } from '../../common/services/openai.service';
import { AdherenceModule } from '../adherence/adherence.module';

@Module({
  imports: [
    AdherenceModule,
  ],
  controllers: [WebhooksController],
  providers: [
    WebhooksService,
    SupabaseService,
    OpenAiService,
  ],
  exports: [WebhooksService],
})
export class WebhooksModule {}
