import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { SupabaseService } from '../../config/supabase.service';
import { User, Achievement, UserAchievement } from '../../common/types';
import { 
  CreateAchievementDto,
  UpdateAchievementDto,
  AchievementQueryDto,
  UserAchievementQueryDto,
  AchievementProgress,
  AchievementSummary,
  AchievementCategory,
  DEFAULT_ACHIEVEMENTS
} from './dto';

@Injectable()
export class AchievementsService {
  constructor(private readonly supabaseService: SupabaseService) {}

  async create(createAchievementDto: CreateAchievementDto, currentUser: User): Promise<Achievement> {
    // Only admins can create achievements
    if (currentUser.role !== 'admin') {
      throw new ForbiddenException('Only administrators can create achievements');
    }

    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('achievements')
      .insert({
        name: createAchievementDto.name,
        description: createAchievementDto.description,
        icon: createAchievementDto.icon,
        category: createAchievementDto.category,
        requirement: createAchievementDto.requirement,
        points: createAchievementDto.points,
      })
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to create achievement: ${error.message}`);
    }

    return data;
  }

  async findAll(query: AchievementQueryDto): Promise<Achievement[]> {
    const supabase = this.supabaseService.getClient();

    let dbQuery = supabase
      .from('achievements')
      .select('*');

    // Apply filters
    if (query.category) {
      dbQuery = dbQuery.eq('category', query.category);
    }

    // Apply pagination and ordering
    dbQuery = dbQuery
      .order('category', { ascending: true })
      .order('requirement', { ascending: true })
      .range(query.offset || 0, (query.offset || 0) + (query.limit || 20) - 1);

    const { data, error } = await dbQuery;

    if (error) {
      throw new BadRequestException(`Failed to fetch achievements: ${error.message}`);
    }

    return data || [];
  }

  async findOne(id: string): Promise<Achievement> {
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('achievements')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) {
      throw new NotFoundException(`Achievement with ID ${id} not found`);
    }

    return data;
  }

  async update(id: string, updateAchievementDto: UpdateAchievementDto, currentUser: User): Promise<Achievement> {
    // Only admins can update achievements
    if (currentUser.role !== 'admin') {
      throw new ForbiddenException('Only administrators can update achievements');
    }

    // First check if achievement exists
    await this.findOne(id);

    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('achievements')
      .update({
        ...updateAchievementDto,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to update achievement: ${error.message}`);
    }

    return data;
  }

  async remove(id: string, currentUser: User): Promise<void> {
    // Only admins can delete achievements
    if (currentUser.role !== 'admin') {
      throw new ForbiddenException('Only administrators can delete achievements');
    }

    // First check if achievement exists
    await this.findOne(id);

    const supabase = this.supabaseService.getClient();

    const { error } = await supabase
      .from('achievements')
      .delete()
      .eq('id', id);

    if (error) {
      throw new BadRequestException(`Failed to delete achievement: ${error.message}`);
    }
  }

  async getUserAchievements(userId: string, currentUser: User): Promise<UserAchievement[]> {
    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== userId) {
      throw new ForbiddenException('Patients can only view their own achievements');
    }

    if (currentUser.role === 'doctor') {
      // Verify patient is assigned to this doctor
      const supabase = this.supabaseService.getClient();
      const { data: patient } = await supabase
        .from('patients')
        .select('assigned_doctor_id')
        .eq('id', userId)
        .single();

      if (!patient || patient.assigned_doctor_id !== currentUser.id) {
        throw new ForbiddenException('You can only view achievements for your assigned patients');
      }
    }

    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('user_achievements')
      .select(`
        *,
        achievement:achievements(*)
      `)
      .eq('user_id', userId)
      .order('unlocked_at', { ascending: false });

    if (error) {
      throw new BadRequestException(`Failed to fetch user achievements: ${error.message}`);
    }

    return data || [];
  }

  async getAchievementProgress(userId: string, currentUser: User): Promise<AchievementProgress[]> {
    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== userId) {
      throw new ForbiddenException('Patients can only view their own progress');
    }

    const supabase = this.supabaseService.getClient();

    // Get all achievements
    const { data: achievements } = await supabase
      .from('achievements')
      .select('*')
      .order('category', { ascending: true })
      .order('requirement', { ascending: true });

    if (!achievements) {
      return [];
    }

    // Get user's unlocked achievements
    const { data: userAchievements } = await supabase
      .from('user_achievements')
      .select('achievement_id, unlocked_at')
      .eq('user_id', userId);

    const unlockedMap = new Map(
      (userAchievements || []).map(ua => [ua.achievement_id, ua.unlocked_at])
    );

    // Calculate progress for each achievement
    const progressList: AchievementProgress[] = [];

    for (const achievement of achievements) {
      const isUnlocked = unlockedMap.has(achievement.id);
      const unlockedAt = unlockedMap.get(achievement.id);
      
      let currentProgress = 0;
      let estimatedCompletion = 'Unknown';

      if (!isUnlocked) {
        // Calculate current progress based on achievement type
        currentProgress = await this.calculateAchievementProgress(userId, achievement);
        estimatedCompletion = this.estimateCompletionTime(achievement, currentProgress);
      } else {
        currentProgress = achievement.requirement;
      }

      const progressPercentage = Math.min((currentProgress / achievement.requirement) * 100, 100);

      progressList.push({
        achievement_id: achievement.id,
        achievement_name: achievement.name,
        achievement_description: achievement.description,
        achievement_icon: achievement.icon,
        category: achievement.category as AchievementCategory,
        requirement: achievement.requirement,
        points: achievement.points,
        current_progress: currentProgress,
        progress_percentage: Math.round(progressPercentage * 100) / 100,
        is_unlocked: isUnlocked,
        unlocked_at: unlockedAt ? new Date(unlockedAt) : undefined,
        estimated_completion: isUnlocked ? undefined : estimatedCompletion,
      });
    }

    return progressList;
  }

  private async calculateAchievementProgress(userId: string, achievement: Achievement): Promise<number> {
    const supabase = this.supabaseService.getClient();

    switch (achievement.category) {
      case 'streak':
        // Get current streak from gamification stats
        const { data: stats } = await supabase
          .from('gamification_stats')
          .select('current_streak')
          .eq('patient_id', userId)
          .single();
        return stats?.current_streak || 0;

      case 'consistency':
        // Get completion rate from gamification stats
        const { data: consistencyStats } = await supabase
          .from('gamification_stats')
          .select('completion_rate')
          .eq('patient_id', userId)
          .single();
        return consistencyStats?.completion_rate || 0;

      case 'milestone':
        // Count total medications taken
        const { data: adherenceRecords } = await supabase
          .from('adherence_records')
          .select('id')
          .eq('patient_id', userId)
          .eq('status', 'taken');
        return adherenceRecords?.length || 0;

      case 'completion':
        // Count completed prescription courses
        const { data: prescriptions } = await supabase
          .from('prescriptions')
          .select('id')
          .eq('patient_id', userId)
          .eq('status', 'completed');
        return prescriptions?.length || 0;

      default:
        return 0;
    }
  }

  private estimateCompletionTime(achievement: Achievement, currentProgress: number): string {
    const remaining = achievement.requirement - currentProgress;

    if (remaining <= 0) return 'Ready to unlock';

    switch (achievement.category) {
      case 'streak':
        return `${remaining} days`;

      case 'consistency':
        return 'Next month review';

      case 'milestone':
        // Assume 3 medications per day average
        const days = Math.ceil(remaining / 3);
        if (days < 30) return `${days} days`;
        if (days < 365) return `${Math.ceil(days / 30)} months`;
        return `${Math.ceil(days / 365)} years`;

      case 'completion':
        // Assume 1 prescription per month average
        const months = remaining;
        if (months < 12) return `${months} months`;
        return `${Math.ceil(months / 12)} years`;

      default:
        return 'Unknown';
    }
  }

  async getAchievementSummary(userId: string, currentUser: User): Promise<AchievementSummary> {
    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== userId) {
      throw new ForbiddenException('Patients can only view their own summary');
    }

    const supabase = this.supabaseService.getClient();

    // Get all achievements and user achievements
    const [achievementsResult, userAchievementsResult] = await Promise.all([
      supabase.from('achievements').select('*'),
      supabase.from('user_achievements').select(`
        *,
        achievement:achievements(name, points)
      `).eq('user_id', userId).order('unlocked_at', { ascending: false })
    ]);

    const achievements = achievementsResult.data || [];
    const userAchievements = userAchievementsResult.data || [];

    const totalAchievements = achievements.length;
    const unlockedAchievements = userAchievements.length;
    const totalPointsFromAchievements = userAchievements.reduce(
      (sum, ua) => sum + (ua.achievement?.points || 0), 0
    );
    const completionPercentage = totalAchievements > 0
      ? (unlockedAchievements / totalAchievements) * 100
      : 0;

    // Recent achievements (last 5)
    const recentAchievements = userAchievements.slice(0, 5).map(ua => ({
      achievement_id: ua.achievement_id,
      achievement_name: ua.achievement?.name || 'Unknown Achievement',
      points: ua.achievement?.points || 0,
      unlocked_at: new Date(ua.unlocked_at),
    }));

    // Get progress for next achievements
    const progressList = await this.getAchievementProgress(userId, currentUser);
    const nextAchievements = progressList
      .filter(p => !p.is_unlocked && p.progress_percentage > 0)
      .sort((a, b) => b.progress_percentage - a.progress_percentage)
      .slice(0, 3)
      .map(p => ({
        achievement_id: p.achievement_id,
        achievement_name: p.achievement_name,
        progress_percentage: p.progress_percentage,
        estimated_completion: p.estimated_completion || 'Unknown',
      }));

    // Category breakdown
    const categoryBreakdown = Object.values(AchievementCategory).map(category => {
      const categoryAchievements = achievements.filter(a => a.category === category);
      const categoryUnlocked = userAchievements.filter(ua =>
        achievements.find(a => a.id === ua.achievement_id)?.category === category
      );

      return {
        category,
        total: categoryAchievements.length,
        unlocked: categoryUnlocked.length,
        percentage: categoryAchievements.length > 0
          ? (categoryUnlocked.length / categoryAchievements.length) * 100
          : 0,
      };
    });

    return {
      user_id: userId,
      total_achievements: totalAchievements,
      unlocked_achievements: unlockedAchievements,
      total_points_from_achievements: totalPointsFromAchievements,
      completion_percentage: Math.round(completionPercentage * 100) / 100,
      recent_achievements: recentAchievements,
      next_achievements: nextAchievements,
      category_breakdown: categoryBreakdown,
    };
  }

  async checkAndAwardAchievements(userId: string): Promise<UserAchievement[]> {
    const supabase = this.supabaseService.getClient();

    // Get all achievements
    const { data: achievements } = await supabase
      .from('achievements')
      .select('*');

    if (!achievements) return [];

    // Get user's current achievements
    const { data: userAchievements } = await supabase
      .from('user_achievements')
      .select('achievement_id')
      .eq('user_id', userId);

    const unlockedIds = new Set((userAchievements || []).map(ua => ua.achievement_id));
    const newAchievements: UserAchievement[] = [];

    // Check each achievement
    for (const achievement of achievements) {
      if (unlockedIds.has(achievement.id)) continue; // Already unlocked

      const currentProgress = await this.calculateAchievementProgress(userId, achievement);

      if (currentProgress >= achievement.requirement) {
        // Award the achievement
        const { data: newAchievement, error } = await supabase
          .from('user_achievements')
          .insert({
            user_id: userId,
            achievement_id: achievement.id,
          })
          .select(`
            *,
            achievement:achievements(*)
          `)
          .single();

        if (!error && newAchievement) {
          newAchievements.push(newAchievement);

          // Award points to gamification stats
          await this.awardAchievementPoints(userId, achievement.points);
        }
      }
    }

    return newAchievements;
  }

  private async awardAchievementPoints(userId: string, points: number): Promise<void> {
    const supabase = this.supabaseService.getClient();

    // Get current stats
    const { data: stats } = await supabase
      .from('gamification_stats')
      .select('total_points')
      .eq('patient_id', userId)
      .single();

    const currentPoints = stats?.total_points || 0;
    const newTotalPoints = currentPoints + points;

    // Update points
    await supabase
      .from('gamification_stats')
      .update({
        total_points: newTotalPoints,
        updated_at: new Date().toISOString(),
      })
      .eq('patient_id', userId);
  }

  async seedDefaultAchievements(): Promise<void> {
    const supabase = this.supabaseService.getClient();

    // Check if achievements already exist
    const { data: existingAchievements } = await supabase
      .from('achievements')
      .select('name')
      .limit(1);

    if (existingAchievements && existingAchievements.length > 0) {
      return; // Already seeded
    }

    // Insert default achievements
    const { error } = await supabase
      .from('achievements')
      .insert(DEFAULT_ACHIEVEMENTS);

    if (error) {
      throw new BadRequestException(`Failed to seed achievements: ${error.message}`);
    }
  }
}
