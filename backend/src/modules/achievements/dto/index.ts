import { IsString, <PERSON>U<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>N<PERSON>ber, <PERSON>, <PERSON>, IsNotEmpty, MaxLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export enum AchievementCategory {
  STREAK = 'streak',
  CONSISTENCY = 'consistency',
  COMPLETION = 'completion',
  MILESTONE = 'milestone',
}

export class CreateAchievementDto {
  @ApiProperty({
    description: 'Achievement name',
    example: 'Week Warrior',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Achievement description',
    example: 'Maintain a 7-day medication streak',
    maxLength: 1000,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(1000)
  description: string;

  @ApiProperty({
    description: 'Achievement icon identifier',
    example: 'trophy-star',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  icon: string;

  @ApiProperty({
    description: 'Achievement category',
    enum: AchievementCategory,
    example: AchievementCategory.STREAK,
  })
  @IsEnum(AchievementCategory)
  @IsNotEmpty()
  category: AchievementCategory;

  @ApiProperty({
    description: 'Requirement value to unlock this achievement',
    example: 7,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  requirement: number;

  @ApiProperty({
    description: 'Points awarded for this achievement',
    example: 100,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  points: number;
}

export class UpdateAchievementDto {
  @ApiPropertyOptional({
    description: 'Achievement name',
    example: 'Week Warrior',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiPropertyOptional({
    description: 'Achievement description',
    example: 'Maintain a 7-day medication streak',
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiPropertyOptional({
    description: 'Achievement icon identifier',
    example: 'trophy-star',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  icon?: string;

  @ApiPropertyOptional({
    description: 'Achievement category',
    enum: AchievementCategory,
    example: AchievementCategory.STREAK,
  })
  @IsOptional()
  @IsEnum(AchievementCategory)
  category?: AchievementCategory;

  @ApiPropertyOptional({
    description: 'Requirement value to unlock this achievement',
    example: 7,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  requirement?: number;

  @ApiPropertyOptional({
    description: 'Points awarded for this achievement',
    example: 100,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  points?: number;
}

export class AchievementQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by category',
    enum: AchievementCategory,
    example: AchievementCategory.STREAK,
  })
  @IsOptional()
  @IsEnum(AchievementCategory)
  category?: AchievementCategory;

  @ApiPropertyOptional({
    description: 'Number of achievements to return',
    example: 20,
    default: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: 'Number of achievements to skip',
    example: 0,
    default: 0,
    minimum: 0,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(0)
  offset?: number = 0;
}

export class UserAchievementQueryDto {
  @ApiProperty({
    description: 'User ID to get achievements for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  user_id: string;

  @ApiPropertyOptional({
    description: 'Filter by category',
    enum: AchievementCategory,
    example: AchievementCategory.STREAK,
  })
  @IsOptional()
  @IsEnum(AchievementCategory)
  category?: AchievementCategory;

  @ApiPropertyOptional({
    description: 'Include only unlocked achievements',
    example: true,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  unlocked_only?: boolean = false;
}

export interface AchievementProgress {
  achievement_id: string;
  achievement_name: string;
  achievement_description: string;
  achievement_icon: string;
  category: AchievementCategory;
  requirement: number;
  points: number;
  current_progress: number;
  progress_percentage: number;
  is_unlocked: boolean;
  unlocked_at?: Date;
  estimated_completion?: string;
}

export interface AchievementSummary {
  user_id: string;
  total_achievements: number;
  unlocked_achievements: number;
  total_points_from_achievements: number;
  completion_percentage: number;
  recent_achievements: Array<{
    achievement_id: string;
    achievement_name: string;
    points: number;
    unlocked_at: Date;
  }>;
  next_achievements: Array<{
    achievement_id: string;
    achievement_name: string;
    progress_percentage: number;
    estimated_completion: string;
  }>;
  category_breakdown: Array<{
    category: AchievementCategory;
    total: number;
    unlocked: number;
    percentage: number;
  }>;
}

// Predefined achievements that will be seeded into the database
export const DEFAULT_ACHIEVEMENTS = [
  // Streak Achievements
  {
    name: 'First Steps',
    description: 'Take your first medication on time',
    icon: 'baby-steps',
    category: AchievementCategory.STREAK,
    requirement: 1,
    points: 10,
  },
  {
    name: 'Week Warrior',
    description: 'Maintain a 7-day medication streak',
    icon: 'trophy-week',
    category: AchievementCategory.STREAK,
    requirement: 7,
    points: 100,
  },
  {
    name: 'Two Week Champion',
    description: 'Maintain a 14-day medication streak',
    icon: 'trophy-two-weeks',
    category: AchievementCategory.STREAK,
    requirement: 14,
    points: 200,
  },
  {
    name: 'Monthly Master',
    description: 'Maintain a 30-day medication streak',
    icon: 'trophy-month',
    category: AchievementCategory.STREAK,
    requirement: 30,
    points: 500,
  },
  {
    name: 'Quarterly Hero',
    description: 'Maintain a 90-day medication streak',
    icon: 'trophy-quarter',
    category: AchievementCategory.STREAK,
    requirement: 90,
    points: 1000,
  },
  {
    name: 'Half Year Legend',
    description: 'Maintain a 180-day medication streak',
    icon: 'trophy-half-year',
    category: AchievementCategory.STREAK,
    requirement: 180,
    points: 2000,
  },
  {
    name: 'Year Long Champion',
    description: 'Maintain a 365-day medication streak',
    icon: 'trophy-year',
    category: AchievementCategory.STREAK,
    requirement: 365,
    points: 5000,
  },

  // Consistency Achievements
  {
    name: 'Reliable Rookie',
    description: 'Achieve 80% adherence rate for a month',
    icon: 'consistency-80',
    category: AchievementCategory.CONSISTENCY,
    requirement: 80,
    points: 150,
  },
  {
    name: 'Dependable Pro',
    description: 'Achieve 90% adherence rate for a month',
    icon: 'consistency-90',
    category: AchievementCategory.CONSISTENCY,
    requirement: 90,
    points: 300,
  },
  {
    name: 'Perfect Patient',
    description: 'Achieve 95% adherence rate for a month',
    icon: 'consistency-95',
    category: AchievementCategory.CONSISTENCY,
    requirement: 95,
    points: 500,
  },
  {
    name: 'Flawless Performer',
    description: 'Achieve 100% adherence rate for a month',
    icon: 'consistency-100',
    category: AchievementCategory.CONSISTENCY,
    requirement: 100,
    points: 1000,
  },

  // Milestone Achievements
  {
    name: 'Century Club',
    description: 'Take 100 medications',
    icon: 'milestone-100',
    category: AchievementCategory.MILESTONE,
    requirement: 100,
    points: 200,
  },
  {
    name: 'Half Thousand Hero',
    description: 'Take 500 medications',
    icon: 'milestone-500',
    category: AchievementCategory.MILESTONE,
    requirement: 500,
    points: 750,
  },
  {
    name: 'Thousand Strong',
    description: 'Take 1000 medications',
    icon: 'milestone-1000',
    category: AchievementCategory.MILESTONE,
    requirement: 1000,
    points: 1500,
  },

  // Completion Achievements
  {
    name: 'Course Completer',
    description: 'Complete your first prescription course',
    icon: 'completion-first',
    category: AchievementCategory.COMPLETION,
    requirement: 1,
    points: 100,
  },
  {
    name: 'Treatment Veteran',
    description: 'Complete 5 prescription courses',
    icon: 'completion-five',
    category: AchievementCategory.COMPLETION,
    requirement: 5,
    points: 500,
  },
  {
    name: 'Wellness Warrior',
    description: 'Complete 10 prescription courses',
    icon: 'completion-ten',
    category: AchievementCategory.COMPLETION,
    requirement: 10,
    points: 1000,
  },
];
