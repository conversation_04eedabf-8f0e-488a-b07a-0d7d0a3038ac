import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { AchievementsService } from './achievements.service';
import { 
  CreateAchievementDto,
  UpdateAchievementDto,
  AchievementQueryDto,
  UserAchievementQueryDto,
  AchievementProgress,
  AchievementSummary
} from './dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { Achievement, UserAchievement } from '../../common/types';

@ApiTags('achievements')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('achievements')
export class AchievementsController {
  constructor(private readonly achievementsService: AchievementsService) {}

  @Post()
  @Roles('admin')
  @ApiOperation({ 
    summary: 'Create achievement',
    description: 'Create a new achievement (admin only)'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Achievement created successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions - admin only',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async create(
    @Body() createAchievementDto: CreateAchievementDto,
    @Request() req: any,
  ): Promise<Achievement> {
    return this.achievementsService.create(createAchievementDto, req.user);
  }

  @Get()
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get all achievements',
    description: 'Retrieve all available achievements with optional filtering'
  })
  @ApiQuery({ name: 'category', required: false, description: 'Filter by achievement category' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of achievements to return' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of achievements to skip' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Achievements retrieved successfully',
    type: [Object],
  })
  async findAll(@Query() query: AchievementQueryDto): Promise<Achievement[]> {
    return this.achievementsService.findAll(query);
  }

  @Get('user/:user_id')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get user achievements',
    description: 'Retrieve achievements earned by a specific user'
  })
  @ApiParam({ name: 'user_id', description: 'User ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User achievements retrieved successfully',
    type: [Object],
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  async getUserAchievements(
    @Param('user_id') userId: string,
    @Request() req: any,
  ): Promise<UserAchievement[]> {
    return this.achievementsService.getUserAchievements(userId, req.user);
  }

  @Get('progress/:user_id')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get achievement progress',
    description: 'Retrieve progress toward all achievements for a user'
  })
  @ApiParam({ name: 'user_id', description: 'User ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Achievement progress retrieved successfully',
    type: [Object],
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  async getAchievementProgress(
    @Param('user_id') userId: string,
    @Request() req: any,
  ): Promise<AchievementProgress[]> {
    return this.achievementsService.getAchievementProgress(userId, req.user);
  }

  @Get('summary/:user_id')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get achievement summary',
    description: 'Retrieve comprehensive achievement summary for a user'
  })
  @ApiParam({ name: 'user_id', description: 'User ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Achievement summary retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  async getAchievementSummary(
    @Param('user_id') userId: string,
    @Request() req: any,
  ): Promise<AchievementSummary> {
    return this.achievementsService.getAchievementSummary(userId, req.user);
  }

  @Post('check/:user_id')
  @Roles('admin')
  @ApiOperation({ 
    summary: 'Check and award achievements',
    description: 'Check user progress and award any newly earned achievements (admin only)'
  })
  @ApiParam({ name: 'user_id', description: 'User ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Achievement check completed',
    type: [Object],
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions - admin only',
  })
  async checkAndAwardAchievements(
    @Param('user_id') userId: string,
    @Request() req: any,
  ): Promise<UserAchievement[]> {
    return this.achievementsService.checkAndAwardAchievements(userId);
  }

  @Post('seed')
  @Roles('admin')
  @ApiOperation({ 
    summary: 'Seed default achievements',
    description: 'Seed the database with default achievements (admin only)'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Default achievements seeded successfully',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions - admin only',
  })
  async seedDefaultAchievements(@Request() req: any): Promise<{ message: string }> {
    await this.achievementsService.seedDefaultAchievements();
    return { message: 'Default achievements seeded successfully' };
  }

  @Get(':id')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get achievement by ID',
    description: 'Retrieve a specific achievement'
  })
  @ApiParam({ name: 'id', description: 'Achievement ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Achievement retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Achievement not found',
  })
  async findOne(@Param('id') id: string): Promise<Achievement> {
    return this.achievementsService.findOne(id);
  }

  @Patch(':id')
  @Roles('admin')
  @ApiOperation({ 
    summary: 'Update achievement',
    description: 'Update an existing achievement (admin only)'
  })
  @ApiParam({ name: 'id', description: 'Achievement ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Achievement updated successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions - admin only',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Achievement not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async update(
    @Param('id') id: string,
    @Body() updateAchievementDto: UpdateAchievementDto,
    @Request() req: any,
  ): Promise<Achievement> {
    return this.achievementsService.update(id, updateAchievementDto, req.user);
  }

  @Delete(':id')
  @Roles('admin')
  @ApiOperation({ 
    summary: 'Delete achievement',
    description: 'Delete an achievement (admin only)'
  })
  @ApiParam({ name: 'id', description: 'Achievement ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Achievement deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions - admin only',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Achievement not found',
  })
  async remove(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    return this.achievementsService.remove(id, req.user);
  }
}
