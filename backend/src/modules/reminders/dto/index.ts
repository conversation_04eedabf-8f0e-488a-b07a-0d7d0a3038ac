import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsUUID, IsEnum, IsOptional, IsDateString, IsBoolean, IsArray, IsNumber, Min, Max } from 'class-validator';

export class CreateReminderDto {
  @ApiProperty({ example: 'uuid-of-patient' })
  @IsUUID()
  patient_id: string;

  @ApiProperty({ example: 'uuid-of-medicine' })
  @IsUUID()
  medicine_id: string;

  @ApiProperty({ example: 'sms', enum: ['sms', 'call', 'both'] })
  @IsEnum(['sms', 'call', 'both'])
  reminder_type: 'sms' | 'call' | 'both';

  @ApiProperty({ example: '2024-01-15T09:00:00Z' })
  @IsDateString()
  scheduled_time: string;

  @ApiPropertyOptional({ example: 'Take your morning medication' })
  @IsOptional()
  @IsString()
  message?: string;

  @ApiPropertyOptional({ example: true })
  @IsOptional()
  @IsBoolean()
  is_recurring?: boolean;

  @ApiPropertyOptional({ example: 'daily', enum: ['daily', 'weekly', 'monthly'] })
  @IsOptional()
  @IsEnum(['daily', 'weekly', 'monthly'])
  recurrence_pattern?: 'daily' | 'weekly' | 'monthly';

  @ApiPropertyOptional({ example: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(30)
  recurrence_interval?: number;

  @ApiPropertyOptional({ example: ['monday', 'wednesday', 'friday'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  recurrence_days?: string[];

  @ApiPropertyOptional({ example: '2024-12-31T23:59:59Z' })
  @IsOptional()
  @IsDateString()
  end_date?: string;
}

export class UpdateReminderDto {
  @ApiPropertyOptional({ example: 'sms', enum: ['sms', 'call', 'both'] })
  @IsOptional()
  @IsEnum(['sms', 'call', 'both'])
  reminder_type?: 'sms' | 'call' | 'both';

  @ApiPropertyOptional({ example: '2024-01-15T09:00:00Z' })
  @IsOptional()
  @IsDateString()
  scheduled_time?: string;

  @ApiPropertyOptional({ example: 'Take your morning medication' })
  @IsOptional()
  @IsString()
  message?: string;

  @ApiPropertyOptional({ example: true })
  @IsOptional()
  @IsBoolean()
  is_recurring?: boolean;

  @ApiPropertyOptional({ example: 'daily', enum: ['daily', 'weekly', 'monthly'] })
  @IsOptional()
  @IsEnum(['daily', 'weekly', 'monthly'])
  recurrence_pattern?: 'daily' | 'weekly' | 'monthly';

  @ApiPropertyOptional({ example: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(30)
  recurrence_interval?: number;

  @ApiPropertyOptional({ example: ['monday', 'wednesday', 'friday'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  recurrence_days?: string[];

  @ApiPropertyOptional({ example: '2024-12-31T23:59:59Z' })
  @IsOptional()
  @IsDateString()
  end_date?: string;

  @ApiPropertyOptional({ example: true })
  @IsOptional()
  @IsBoolean()
  is_active?: boolean;
}

export class ReminderQueryDto {
  @ApiPropertyOptional({ example: 'uuid-of-patient' })
  @IsOptional()
  @IsUUID()
  patient_id?: string;

  @ApiPropertyOptional({ example: 'uuid-of-medicine' })
  @IsOptional()
  @IsUUID()
  medicine_id?: string;

  @ApiPropertyOptional({ example: 'pending', enum: ['pending', 'sent', 'failed', 'all'] })
  @IsOptional()
  @IsString()
  status?: 'pending' | 'sent' | 'failed' | 'all';

  @ApiPropertyOptional({ example: 'sms', enum: ['sms', 'call', 'both', 'all'] })
  @IsOptional()
  @IsString()
  reminder_type?: 'sms' | 'call' | 'both' | 'all';

  @ApiPropertyOptional({ example: '2024-01-15' })
  @IsOptional()
  @IsDateString()
  date_from?: string;

  @ApiPropertyOptional({ example: '2024-01-31' })
  @IsOptional()
  @IsDateString()
  date_to?: string;

  @ApiPropertyOptional({ example: true })
  @IsOptional()
  @IsBoolean()
  is_active?: boolean;
}

export class ReminderLogDto {
  @ApiProperty({ example: 'uuid-of-reminder' })
  @IsUUID()
  reminder_id: string;

  @ApiProperty({ example: 'sent', enum: ['sent', 'failed', 'delivered', 'read'] })
  @IsEnum(['sent', 'failed', 'delivered', 'read'])
  status: 'sent' | 'failed' | 'delivered' | 'read';

  @ApiPropertyOptional({ example: 'SMS sent successfully' })
  @IsOptional()
  @IsString()
  message?: string;

  @ApiPropertyOptional({ example: 'Failed to send: Invalid phone number' })
  @IsOptional()
  @IsString()
  error_message?: string;

  @ApiPropertyOptional({ example: 'twilio_message_id_123' })
  @IsOptional()
  @IsString()
  external_id?: string;
}

export class BulkReminderDto {
  @ApiProperty({ example: ['uuid1', 'uuid2', 'uuid3'] })
  @IsArray()
  @IsUUID(undefined, { each: true })
  patient_ids: string[];

  @ApiProperty({ example: 'uuid-of-medicine' })
  @IsUUID()
  medicine_id: string;

  @ApiProperty({ example: 'sms', enum: ['sms', 'call', 'both'] })
  @IsEnum(['sms', 'call', 'both'])
  reminder_type: 'sms' | 'call' | 'both';

  @ApiProperty({ example: '09:00' })
  @IsString()
  time: string; // Format: HH:MM

  @ApiPropertyOptional({ example: 'Take your daily medication' })
  @IsOptional()
  @IsString()
  message?: string;

  @ApiPropertyOptional({ example: true })
  @IsOptional()
  @IsBoolean()
  is_recurring?: boolean;

  @ApiPropertyOptional({ example: 'daily', enum: ['daily', 'weekly', 'monthly'] })
  @IsOptional()
  @IsEnum(['daily', 'weekly', 'monthly'])
  recurrence_pattern?: 'daily' | 'weekly' | 'monthly';
}
