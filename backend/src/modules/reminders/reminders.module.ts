import { Modu<PERSON> } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { RemindersService } from './reminders.service';
import { RemindersController } from './reminders.controller';
import { SupabaseService } from '../../config/supabase.service';
import { TwilioService } from '../../common/services/twilio.service';
import { ElevenLabsService } from '../../common/services/elevenlabs.service';

@Module({
  imports: [EventEmitterModule],
  controllers: [RemindersController],
  providers: [RemindersService, SupabaseService, TwilioService, ElevenLabsService],
  exports: [RemindersService],
})
export class RemindersModule {}
