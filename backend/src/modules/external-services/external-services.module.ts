import { Modu<PERSON> } from '@nestjs/common';
import { ExternalServicesController } from './external-services.controller';
import { ExternalServicesService } from './external-services.service';
import { TwilioService } from '../../common/services/twilio.service';
import { ElevenLabsService } from '../../common/services/elevenlabs.service';
import { AwsService } from '../../common/services/aws.service';
import { OpenAiService } from '../../common/services/openai.service';

@Module({
  controllers: [ExternalServicesController],
  providers: [ExternalServicesService, TwilioService, ElevenLabsService, AwsService, OpenAiService],
  exports: [ExternalServicesService, TwilioService, ElevenLabsService, AwsService, OpenAiService],
})
export class ExternalServicesModule {}
