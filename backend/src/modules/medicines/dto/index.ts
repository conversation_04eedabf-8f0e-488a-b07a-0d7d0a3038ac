import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsUUID, IsInt, IsDateString, IsOptional, Min } from 'class-validator';

export class CreateMedicineDto {
  @ApiProperty({ example: 'Aspirin' })
  @IsString()
  name: string;

  @ApiProperty({ example: '100mg' })
  @IsString()
  dosage: string;

  @ApiProperty({ example: 'Twice daily' })
  @IsString()
  frequency: string;

  @ApiProperty({ example: 30, description: 'Duration in days' })
  @IsInt()
  @Min(1)
  duration: number;

  @ApiProperty({ example: 'Take with food' })
  @IsString()
  instructions: string;

  @ApiPropertyOptional({ example: 'May cause drowsiness' })
  @IsOptional()
  @IsString()
  side_effects?: string;

  @ApiProperty({ example: '2024-01-01' })
  @IsDateString()
  start_date: string;

  @ApiProperty({ example: '2024-01-31' })
  @IsDateString()
  end_date: string;

  @ApiProperty({ example: 'uuid-of-prescription' })
  @IsUUID()
  prescription_id: string;

  @ApiProperty({ example: 'uuid-of-patient' })
  @IsUUID()
  patient_id: string;
}

export class UpdateMedicineDto {
  @ApiPropertyOptional({ example: 'Aspirin Updated' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ example: '200mg' })
  @IsOptional()
  @IsString()
  dosage?: string;

  @ApiPropertyOptional({ example: 'Three times daily' })
  @IsOptional()
  @IsString()
  frequency?: string;

  @ApiPropertyOptional({ example: 45, description: 'Duration in days' })
  @IsOptional()
  @IsInt()
  @Min(1)
  duration?: number;

  @ApiPropertyOptional({ example: 'Take with food and water' })
  @IsOptional()
  @IsString()
  instructions?: string;

  @ApiPropertyOptional({ example: 'May cause drowsiness and nausea' })
  @IsOptional()
  @IsString()
  side_effects?: string;

  @ApiPropertyOptional({ example: '2024-01-02' })
  @IsOptional()
  @IsDateString()
  start_date?: string;

  @ApiPropertyOptional({ example: '2024-02-15' })
  @IsOptional()
  @IsDateString()
  end_date?: string;
}

export class MedicineQueryDto {
  @ApiPropertyOptional({ example: 'uuid-of-patient' })
  @IsOptional()
  @IsUUID()
  patient_id?: string;

  @ApiPropertyOptional({ example: 'uuid-of-prescription' })
  @IsOptional()
  @IsUUID()
  prescription_id?: string;

  @ApiPropertyOptional({ example: 'Aspirin' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ example: 'active', enum: ['active', 'completed', 'all'] })
  @IsOptional()
  @IsString()
  status?: 'active' | 'completed' | 'all';
}
