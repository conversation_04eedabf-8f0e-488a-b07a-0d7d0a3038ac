import { Module } from '@nestjs/common';
import { GamificationService } from './gamification.service';
import { GamificationController } from './gamification.controller';
import { SupabaseService } from '../../config/supabase.service';

@Module({
  controllers: [GamificationController],
  providers: [GamificationService, SupabaseService],
  exports: [GamificationService],
})
export class GamificationModule {}
