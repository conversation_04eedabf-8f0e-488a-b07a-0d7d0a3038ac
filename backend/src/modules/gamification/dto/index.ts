import { IsUUID, Is<PERSON>ptional, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export class GamificationStatsDto {
  @ApiProperty({
    description: 'Patient ID for gamification stats',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  patient_id: string;
}

export class UpdateGamificationStatsDto {
  @ApiPropertyOptional({
    description: 'Current streak count',
    example: 7,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  current_streak?: number;

  @ApiPropertyOptional({
    description: 'Longest streak achieved',
    example: 30,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  longest_streak?: number;

  @ApiPropertyOptional({
    description: 'Total points earned',
    example: 1250,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  total_points?: number;

  @ApiPropertyOptional({
    description: 'Overall completion rate percentage',
    example: 85.5,
    minimum: 0,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  completion_rate?: number;

  @ApiPropertyOptional({
    description: 'Weekly progress array',
    example: [80, 90, 75, 95, 85, 88, 92],
    type: [Number],
  })
  @IsOptional()
  weekly_progress?: number[];

  @ApiPropertyOptional({
    description: 'Monthly progress array',
    example: [85, 88, 92, 87],
    type: [Number],
  })
  @IsOptional()
  monthly_progress?: number[];
}

export class LeaderboardQueryDto {
  @ApiPropertyOptional({
    description: 'Number of top users to return',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Leaderboard type',
    example: 'points',
    enum: ['points', 'streak', 'completion_rate'],
    default: 'points',
  })
  @IsOptional()
  type?: 'points' | 'streak' | 'completion_rate' = 'points';
}

export interface GamificationLevel {
  level: number;
  name: string;
  min_points: number;
  max_points: number;
  benefits: string[];
}

export interface PointsCalculation {
  base_points: number;
  bonus_points: number;
  total_points: number;
  reason: string;
  multiplier?: number;
}

export interface LeaderboardEntry {
  patient_id: string;
  patient_name: string;
  rank: number;
  points: number;
  current_streak: number;
  completion_rate: number;
  level: GamificationLevel;
}

export interface GamificationDashboard {
  patient_id: string;
  current_stats: {
    total_points: number;
    current_streak: number;
    longest_streak: number;
    completion_rate: number;
    level: GamificationLevel;
    points_to_next_level: number;
  };
  recent_achievements: Array<{
    achievement_id: string;
    achievement_name: string;
    points_earned: number;
    unlocked_at: Date;
  }>;
  weekly_progress: {
    current_week: number[];
    previous_week: number[];
    improvement: number;
  };
  monthly_progress: {
    current_month: number[];
    previous_month: number[];
    improvement: number;
  };
  next_milestones: Array<{
    type: 'streak' | 'points' | 'completion';
    target: number;
    current: number;
    progress_percentage: number;
    estimated_days: number;
  }>;
}

// Predefined gamification levels
export const GAMIFICATION_LEVELS: GamificationLevel[] = [
  {
    level: 1,
    name: 'Beginner',
    min_points: 0,
    max_points: 100,
    benefits: ['Basic tracking', 'Simple achievements'],
  },
  {
    level: 2,
    name: 'Committed',
    min_points: 101,
    max_points: 300,
    benefits: ['Streak bonuses', 'Weekly challenges'],
  },
  {
    level: 3,
    name: 'Dedicated',
    min_points: 301,
    max_points: 600,
    benefits: ['Monthly rewards', 'Advanced analytics'],
  },
  {
    level: 4,
    name: 'Expert',
    min_points: 601,
    max_points: 1000,
    benefits: ['Premium features', 'Priority support'],
  },
  {
    level: 5,
    name: 'Master',
    min_points: 1001,
    max_points: 1500,
    benefits: ['Exclusive content', 'Mentor status'],
  },
  {
    level: 6,
    name: 'Champion',
    min_points: 1501,
    max_points: 2500,
    benefits: ['Leadership board', 'Special recognition'],
  },
  {
    level: 7,
    name: 'Legend',
    min_points: 2501,
    max_points: 5000,
    benefits: ['Hall of fame', 'Lifetime benefits'],
  },
  {
    level: 8,
    name: 'Grandmaster',
    min_points: 5001,
    max_points: Number.MAX_SAFE_INTEGER,
    benefits: ['Ultimate status', 'All features unlocked'],
  },
];

// Points system configuration
export const POINTS_CONFIG = {
  MEDICATION_ON_TIME: 10,
  MEDICATION_LATE: 5,
  PERFECT_DAY_BONUS: 20,
  WEEKLY_STREAK_BONUS: 50,
  MONTHLY_CONSISTENCY_BONUS: 100,
  ACHIEVEMENT_MULTIPLIER: 1.5,
  STREAK_MULTIPLIER_THRESHOLD: 7, // Days
  STREAK_MULTIPLIER: 1.2,
};
