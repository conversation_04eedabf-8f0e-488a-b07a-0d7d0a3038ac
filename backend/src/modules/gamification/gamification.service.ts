import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { SupabaseService } from '../../config/supabase.service';
import { User, GamificationStats } from '../../common/types';
import {
  GamificationStatsDto,
  UpdateGamificationStatsDto,
  LeaderboardQueryDto,
  LeaderboardEntry,
  GamificationDashboard,
  GamificationLevel,
  PointsCalculation,
  GAMIFICATION_LEVELS,
  POINTS_CONFIG
} from './dto';
import { createClient } from '@supabase/supabase-js';

@Injectable()
export class GamificationService {
  constructor(private readonly supabaseService: SupabaseService) {}

  private getServiceRoleClient() {
    return createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  async getStats(patientId: string, currentUser: User): Promise<GamificationStats> {
    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== patientId) {
      throw new ForbiddenException('Patients can only view their own gamification stats');
    }

    if (currentUser.role === 'doctor') {
      // Verify patient is assigned to this doctor
      const supabase = this.supabaseService.getClient();
      const { data: patient } = await supabase
        .from('patients')
        .select('assigned_doctor_id')
        .eq('id', patientId)
        .single();

      if (!patient || patient.assigned_doctor_id !== currentUser.id) {
        throw new ForbiddenException('You can only view stats for your assigned patients');
      }
    }

    // Use service role client to bypass RLS for gamification stats operations
    const serviceSupabase = this.getServiceRoleClient();

    // Get or create gamification stats
    let { data: stats, error } = await serviceSupabase
      .from('gamification_stats')
      .select('*')
      .eq('patient_id', patientId)
      .single();

    if (error && error.code === 'PGRST116') {
      // Stats don't exist, create them
      console.log('Creating new gamification stats for patient:', patientId);
      const { data: newStats, error: createError } = await serviceSupabase
        .from('gamification_stats')
        .insert({
          patient_id: patientId,
          current_streak: 0,
          longest_streak: 0,
          total_points: 0,
          adherence_rate: 0.0,
          total_medicines_taken: 0,
          level: 1,
        })
        .select()
        .single();

      if (createError) {
        console.error('Create error:', createError);
        throw new BadRequestException(`Failed to create gamification stats: ${createError.message}`);
      }

      stats = newStats;
    } else if (error) {
      throw new BadRequestException(`Failed to fetch gamification stats: ${error.message}`);
    }

    return stats;
  }

  async updateStats(patientId: string, updateDto: UpdateGamificationStatsDto, currentUser: User): Promise<GamificationStats> {
    // Only allow admin and system updates
    if (currentUser.role !== 'admin') {
      throw new ForbiddenException('Only administrators can manually update gamification stats');
    }

    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('gamification_stats')
      .update({
        ...updateDto,
        updated_at: new Date().toISOString(),
      })
      .eq('patient_id', patientId)
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to update gamification stats: ${error.message}`);
    }

    return data;
  }

  async calculatePointsForAdherence(patientId: string, adherenceStatus: string, scheduledTime: Date, takenTime?: Date): Promise<PointsCalculation> {
    let basePoints = 0;
    let bonusPoints = 0;
    let reason = '';
    let multiplier = 1;

    if (adherenceStatus === 'taken' && takenTime) {
      const scheduled = new Date(scheduledTime);
      const taken = new Date(takenTime);
      const diffHours = (taken.getTime() - scheduled.getTime()) / (1000 * 60 * 60);

      if (diffHours <= 1) {
        // On time
        basePoints = POINTS_CONFIG.MEDICATION_ON_TIME;
        reason = 'Medication taken on time';
      } else if (diffHours <= 4) {
        // Late but within acceptable window
        basePoints = POINTS_CONFIG.MEDICATION_LATE;
        reason = 'Medication taken late';
      }

      // Check for streak bonus
      const stats = await this.getStats(patientId, { role: 'admin' } as User);
      if (stats.current_streak >= POINTS_CONFIG.STREAK_MULTIPLIER_THRESHOLD) {
        multiplier = POINTS_CONFIG.STREAK_MULTIPLIER;
        reason += ` (${stats.current_streak}-day streak bonus)`;
      }

      // Check for perfect day bonus
      const todayStart = new Date(scheduled);
      todayStart.setHours(0, 0, 0, 0);
      const todayEnd = new Date(scheduled);
      todayEnd.setHours(23, 59, 59, 999);

      const supabase = this.supabaseService.getClient();
      const { data: todayRecords } = await supabase
        .from('adherence_records')
        .select('status')
        .eq('patient_id', patientId)
        .gte('scheduled_time', todayStart.toISOString())
        .lte('scheduled_time', todayEnd.toISOString());

      if (todayRecords) {
        const allTaken = todayRecords.every(record => record.status === 'taken');
        if (allTaken && todayRecords.length > 1) {
          bonusPoints = POINTS_CONFIG.PERFECT_DAY_BONUS;
          reason += ' + Perfect day bonus';
        }
      }
    }

    const totalPoints = Math.round((basePoints * multiplier) + bonusPoints);

    return {
      base_points: basePoints,
      bonus_points: bonusPoints,
      total_points: totalPoints,
      reason,
      multiplier: multiplier !== 1 ? multiplier : undefined,
    };
  }

  async updateStatsAfterAdherence(patientId: string, adherenceStatus: string, scheduledTime: Date, takenTime?: Date): Promise<void> {
    const pointsCalc = await this.calculatePointsForAdherence(patientId, adherenceStatus, scheduledTime, takenTime);
    
    if (pointsCalc.total_points > 0) {
      const supabase = this.supabaseService.getClient();
      
      // Get current stats
      const currentStats = await this.getStats(patientId, { role: 'admin' } as User);
      
      // Update points
      const newTotalPoints = currentStats.total_points + pointsCalc.total_points;
      
      // Recalculate adherence rate and streaks
      const { adherence_rate, current_streak, longest_streak } = await this.recalculateStats(patientId);

      // Update stats
      await supabase
        .from('gamification_stats')
        .update({
          total_points: newTotalPoints,
          adherence_rate,
          current_streak,
          longest_streak: Math.max(longest_streak, currentStats.longest_streak),
          updated_at: new Date().toISOString(),
        })
        .eq('patient_id', patientId);
    }
  }

  private async recalculateStats(patientId: string): Promise<{ adherence_rate: number; current_streak: number; longest_streak: number }> {
    const supabase = this.supabaseService.getClient();
    
    // Get adherence records for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const { data: records } = await supabase
      .from('adherence_records')
      .select('status, scheduled_time')
      .eq('patient_id', patientId)
      .gte('scheduled_time', thirtyDaysAgo.toISOString())
      .order('scheduled_time', { ascending: true });

    if (!records || records.length === 0) {
      return { adherence_rate: 0, current_streak: 0, longest_streak: 0 };
    }

    // Calculate adherence rate
    const totalRecords = records.length;
    const takenRecords = records.filter(r => r.status === 'taken').length;
    const adherence_rate = (takenRecords / totalRecords) * 100;

    // Calculate streaks by day
    const dailyRecords = new Map<string, any[]>();
    records.forEach(record => {
      const date = new Date(record.scheduled_time).toDateString();
      if (!dailyRecords.has(date)) {
        dailyRecords.set(date, []);
      }
      dailyRecords.get(date)!.push(record);
    });

    // Calculate daily adherence rates
    const dailyRates: { date: string; rate: number }[] = [];
    for (const [date, dayRecords] of dailyRecords) {
      const taken = dayRecords.filter(r => r.status === 'taken').length;
      const total = dayRecords.length;
      const rate = (taken / total) * 100;
      dailyRates.push({ date, rate });
    }

    // Sort by date
    dailyRates.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Calculate streaks (>80% adherence required)
    let current_streak = 0;
    let longest_streak = 0;
    let tempStreak = 0;

    for (let i = dailyRates.length - 1; i >= 0; i--) {
      if (dailyRates[i].rate >= 80) {
        tempStreak++;
        if (i === dailyRates.length - 1) {
          current_streak = tempStreak;
        }
      } else {
        if (i === dailyRates.length - 1) {
          current_streak = 0;
        }
        longest_streak = Math.max(longest_streak, tempStreak);
        tempStreak = 0;
      }
    }

    longest_streak = Math.max(longest_streak, tempStreak);

    return {
      adherence_rate: Math.round(adherence_rate * 100) / 100,
      current_streak,
      longest_streak,
    };
  }

  async getLeaderboard(query: LeaderboardQueryDto, currentUser: User): Promise<LeaderboardEntry[]> {
    const supabase = this.supabaseService.getClient();

    let orderBy = 'total_points';
    if (query.type === 'streak') {
      orderBy = 'current_streak';
    } else if (query.type === 'completion_rate') {
      orderBy = 'completion_rate';
    }

    const { data: stats, error } = await supabase
      .from('gamification_stats')
      .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name))
      `)
      .order(orderBy, { ascending: false })
      .limit(query.limit || 10);

    if (error) {
      throw new BadRequestException(`Failed to fetch leaderboard: ${error.message}`);
    }

    return (stats || []).map((stat, index) => ({
      patient_id: stat.patient_id,
      patient_name: stat.patient?.users?.name || 'Unknown Patient',
      rank: index + 1,
      points: stat.total_points,
      current_streak: stat.current_streak,
      completion_rate: stat.completion_rate,
      level: this.calculateLevel(stat.total_points),
    }));
  }

  calculateLevel(points: number): GamificationLevel {
    for (let i = GAMIFICATION_LEVELS.length - 1; i >= 0; i--) {
      if (points >= GAMIFICATION_LEVELS[i].min_points) {
        return GAMIFICATION_LEVELS[i];
      }
    }
    return GAMIFICATION_LEVELS[0]; // Default to first level
  }

  async getDashboard(patientId: string, currentUser: User): Promise<GamificationDashboard> {
    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== patientId) {
      throw new ForbiddenException('Patients can only view their own dashboard');
    }

    if (currentUser.role === 'doctor') {
      // Verify patient is assigned to this doctor
      const supabase = this.supabaseService.getClient();
      const { data: patient } = await supabase
        .from('patients')
        .select('assigned_doctor_id')
        .eq('id', patientId)
        .single();

      if (!patient || patient.assigned_doctor_id !== currentUser.id) {
        throw new ForbiddenException('You can only view dashboard for your assigned patients');
      }
    }

    // Use service role client for database operations
    const serviceSupabase = this.getServiceRoleClient();

    // Get current stats
    const stats = await this.getStats(patientId, currentUser);
    const currentLevel = this.calculateLevel(stats.total_points);
    const nextLevel = GAMIFICATION_LEVELS.find(level => level.min_points > stats.total_points);
    const pointsToNextLevel = nextLevel ? nextLevel.min_points - stats.total_points : 0;

    // Get recent achievements
    const { data: recentAchievements } = await serviceSupabase
      .from('user_achievements')
      .select(`
        achievement_id,
        unlocked_at,
        achievement:achievements(name, points)
      `)
      .eq('user_id', patientId)
      .order('unlocked_at', { ascending: false })
      .limit(5);

    // Calculate weekly and monthly progress
    const weeklyProgress = await this.calculateWeeklyProgress(patientId);
    const monthlyProgress = await this.calculateMonthlyProgress(patientId);

    // Calculate next milestones
    const nextMilestones = this.calculateNextMilestones(stats);

    return {
      patient_id: patientId,
      current_stats: {
        total_points: stats.total_points,
        current_streak: stats.current_streak,
        longest_streak: stats.longest_streak,
        completion_rate: stats.adherence_rate, // Map adherence_rate to completion_rate for API consistency
        level: currentLevel,
        points_to_next_level: pointsToNextLevel,
      },
      recent_achievements: (recentAchievements || []).map(achievement => ({
        achievement_id: achievement.achievement_id,
        achievement_name: (achievement.achievement as any)?.name || 'Unknown Achievement',
        points_earned: (achievement.achievement as any)?.points || 0,
        unlocked_at: new Date(achievement.unlocked_at),
      })),
      weekly_progress: weeklyProgress,
      monthly_progress: monthlyProgress,
      next_milestones: nextMilestones,
    };
  }

  private async calculateWeeklyProgress(patientId: string): Promise<{ current_week: number[]; previous_week: number[]; improvement: number }> {
    const supabase = this.getServiceRoleClient();

    const now = new Date();
    const currentWeekStart = new Date(now);
    currentWeekStart.setDate(now.getDate() - now.getDay()); // Start of current week
    currentWeekStart.setHours(0, 0, 0, 0);

    const previousWeekStart = new Date(currentWeekStart);
    previousWeekStart.setDate(currentWeekStart.getDate() - 7);

    const previousWeekEnd = new Date(currentWeekStart);
    previousWeekEnd.setTime(currentWeekStart.getTime() - 1);

    // Get current week data
    const { data: currentWeekRecords } = await supabase
      .from('adherence_records')
      .select('status, scheduled_time')
      .eq('patient_id', patientId)
      .gte('scheduled_time', currentWeekStart.toISOString())
      .lte('scheduled_time', now.toISOString());

    // Get previous week data
    const { data: previousWeekRecords } = await supabase
      .from('adherence_records')
      .select('status, scheduled_time')
      .eq('patient_id', patientId)
      .gte('scheduled_time', previousWeekStart.toISOString())
      .lte('scheduled_time', previousWeekEnd.toISOString());

    const currentWeek = this.calculateDailyRates(currentWeekRecords || [], currentWeekStart, 7);
    const previousWeek = this.calculateDailyRates(previousWeekRecords || [], previousWeekStart, 7);

    const currentAvg = currentWeek.reduce((sum, rate) => sum + rate, 0) / currentWeek.length;
    const previousAvg = previousWeek.reduce((sum, rate) => sum + rate, 0) / previousWeek.length;
    const improvement = currentAvg - previousAvg;

    return {
      current_week: currentWeek,
      previous_week: previousWeek,
      improvement: Math.round(improvement * 100) / 100,
    };
  }

  private async calculateMonthlyProgress(patientId: string): Promise<{ current_month: number[]; previous_month: number[]; improvement: number }> {
    const supabase = this.getServiceRoleClient();

    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const previousMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const previousMonthEnd = new Date(currentMonthStart);
    previousMonthEnd.setTime(currentMonthStart.getTime() - 1);

    // Get current month data
    const { data: currentMonthRecords } = await supabase
      .from('adherence_records')
      .select('status, scheduled_time')
      .eq('patient_id', patientId)
      .gte('scheduled_time', currentMonthStart.toISOString())
      .lte('scheduled_time', now.toISOString());

    // Get previous month data
    const { data: previousMonthRecords } = await supabase
      .from('adherence_records')
      .select('status, scheduled_time')
      .eq('patient_id', patientId)
      .gte('scheduled_time', previousMonthStart.toISOString())
      .lte('scheduled_time', previousMonthEnd.toISOString());

    const daysInCurrentMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
    const daysInPreviousMonth = new Date(now.getFullYear(), now.getMonth(), 0).getDate();

    const currentMonth = this.calculateDailyRates(currentMonthRecords || [], currentMonthStart, daysInCurrentMonth);
    const previousMonth = this.calculateDailyRates(previousMonthRecords || [], previousMonthStart, daysInPreviousMonth);

    const currentAvg = currentMonth.reduce((sum, rate) => sum + rate, 0) / currentMonth.length;
    const previousAvg = previousMonth.reduce((sum, rate) => sum + rate, 0) / previousMonth.length;
    const improvement = currentAvg - previousAvg;

    return {
      current_month: currentMonth,
      previous_month: previousMonth,
      improvement: Math.round(improvement * 100) / 100,
    };
  }

  private calculateDailyRates(records: any[], startDate: Date, days: number): number[] {
    const dailyRates: number[] = [];

    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      const dateStr = date.toDateString();

      const dayRecords = records.filter(record =>
        new Date(record.scheduled_time).toDateString() === dateStr
      );

      if (dayRecords.length === 0) {
        dailyRates.push(0);
      } else {
        const taken = dayRecords.filter(r => r.status === 'taken').length;
        const rate = (taken / dayRecords.length) * 100;
        dailyRates.push(Math.round(rate * 100) / 100);
      }
    }

    return dailyRates;
  }

  private calculateNextMilestones(stats: GamificationStats): Array<{ type: 'streak' | 'points' | 'completion'; target: number; current: number; progress_percentage: number; estimated_days: number }> {
    const milestones: Array<{ type: 'streak' | 'points' | 'completion'; target: number; current: number; progress_percentage: number; estimated_days: number }> = [];

    // Streak milestones
    const streakTargets = [7, 14, 30, 60, 90, 180, 365];
    const nextStreakTarget = streakTargets.find(target => target > stats.current_streak);
    if (nextStreakTarget) {
      milestones.push({
        type: 'streak' as const,
        target: nextStreakTarget,
        current: stats.current_streak,
        progress_percentage: (stats.current_streak / nextStreakTarget) * 100,
        estimated_days: nextStreakTarget - stats.current_streak,
      });
    }

    // Points milestones
    const pointsTargets = [100, 300, 600, 1000, 1500, 2500, 5000, 10000];
    const nextPointsTarget = pointsTargets.find(target => target > stats.total_points);
    if (nextPointsTarget) {
      const pointsNeeded = nextPointsTarget - stats.total_points;
      const estimatedDays = Math.ceil(pointsNeeded / 10); // Assuming 10 points per day average

      milestones.push({
        type: 'points' as const,
        target: nextPointsTarget,
        current: stats.total_points,
        progress_percentage: (stats.total_points / nextPointsTarget) * 100,
        estimated_days: estimatedDays,
      });
    }

    // Completion rate milestones
    const completionTargets = [80, 85, 90, 95, 98, 100];
    const nextCompletionTarget = completionTargets.find(target => target > stats.adherence_rate);
    if (nextCompletionTarget) {
      milestones.push({
        type: 'completion' as const,
        target: nextCompletionTarget,
        current: stats.adherence_rate,
        progress_percentage: (stats.adherence_rate / nextCompletionTarget) * 100,
        estimated_days: 30, // Rough estimate for completion rate improvement
      });
    }

    return milestones.slice(0, 3); // Return top 3 milestones
  }
}
