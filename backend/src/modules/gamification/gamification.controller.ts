import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { GamificationService } from './gamification.service';
import { 
  GamificationStatsDto,
  UpdateGamificationStatsDto,
  LeaderboardQueryDto,
  LeaderboardEntry,
  GamificationDashboard
} from './dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { GamificationStats } from '../../common/types';

@ApiTags('gamification')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('gamification')
export class GamificationController {
  constructor(private readonly gamificationService: GamificationService) {}

  @Get('stats/:patient_id')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get gamification stats',
    description: 'Retrieve gamification statistics for a patient'
  })
  @ApiParam({ name: 'patient_id', description: 'Patient ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Gamification stats retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Patient not found',
  })
  async getStats(
    @Param('patient_id') patientId: string,
    @Request() req: any,
  ): Promise<GamificationStats> {
    return this.gamificationService.getStats(patientId, req.user);
  }

  @Get('dashboard/:patient_id')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get gamification dashboard',
    description: 'Retrieve comprehensive gamification dashboard for a patient'
  })
  @ApiParam({ name: 'patient_id', description: 'Patient ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Dashboard retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Patient not found',
  })
  async getDashboard(
    @Param('patient_id') patientId: string,
    @Request() req: any,
  ): Promise<GamificationDashboard> {
    return this.gamificationService.getDashboard(patientId, req.user);
  }

  @Get('leaderboard')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get leaderboard',
    description: 'Retrieve gamification leaderboard'
  })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of top users to return (default: 10)' })
  @ApiQuery({ name: 'type', required: false, description: 'Leaderboard type: points, streak, or completion_rate (default: points)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Leaderboard retrieved successfully',
    type: [Object],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid query parameters',
  })
  async getLeaderboard(
    @Query() query: LeaderboardQueryDto,
    @Request() req: any,
  ): Promise<LeaderboardEntry[]> {
    return this.gamificationService.getLeaderboard(query, req.user);
  }

  @Patch('stats/:patient_id')
  @Roles('admin')
  @ApiOperation({ 
    summary: 'Update gamification stats',
    description: 'Manually update gamification statistics (admin only)'
  })
  @ApiParam({ name: 'patient_id', description: 'Patient ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Stats updated successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions - admin only',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  async updateStats(
    @Param('patient_id') patientId: string,
    @Body() updateDto: UpdateGamificationStatsDto,
    @Request() req: any,
  ): Promise<GamificationStats> {
    return this.gamificationService.updateStats(patientId, updateDto, req.user);
  }

  @Post('calculate-points/:patient_id')
  @Roles('admin')
  @ApiOperation({ 
    summary: 'Calculate points for adherence',
    description: 'Calculate points for a specific adherence event (admin/testing only)'
  })
  @ApiParam({ name: 'patient_id', description: 'Patient ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Points calculated successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions - admin only',
  })
  async calculatePoints(
    @Param('patient_id') patientId: string,
    @Body() body: { 
      adherence_status: string; 
      scheduled_time: string; 
      taken_time?: string; 
    },
    @Request() req: any,
  ) {
    const scheduledTime = new Date(body.scheduled_time);
    const takenTime = body.taken_time ? new Date(body.taken_time) : undefined;
    
    return this.gamificationService.calculatePointsForAdherence(
      patientId, 
      body.adherence_status, 
      scheduledTime, 
      takenTime
    );
  }

  @Get('levels')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get gamification levels',
    description: 'Retrieve all available gamification levels and their requirements'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Levels retrieved successfully',
    type: [Object],
  })
  async getLevels() {
    const { GAMIFICATION_LEVELS } = await import('./dto');
    return GAMIFICATION_LEVELS;
  }

  @Get('points-config')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get points configuration',
    description: 'Retrieve the points system configuration'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Points configuration retrieved successfully',
    type: Object,
  })
  async getPointsConfig() {
    const { POINTS_CONFIG } = await import('./dto');
    return POINTS_CONFIG;
  }
}
