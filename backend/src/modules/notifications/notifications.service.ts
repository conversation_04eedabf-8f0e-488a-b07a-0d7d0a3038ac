import { Injectable, Logger } from '@nestjs/common';
import { SupabaseService } from '../../config/supabase.service';
import {
  BaseNotification,
  NotificationType,
  NotificationRole,
  SendNotificationDto,
  BroadcastNotificationDto,
  NotificationHistoryQueryDto,
  NotificationResponse,
  NotificationHistoryResponse,
  ConnectedUser,
} from './dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);
  private connectedUsers: Map<string, ConnectedUser> = new Map();
  private userSockets: Map<string, Set<string>> = new Map(); // userId -> Set of socketIds

  constructor(private readonly supabaseService: SupabaseService) {}

  // Connection Management
  addConnectedUser(userId: string, userRole: NotificationRole, socketId: string): void {
    const connectedUser: ConnectedUser = {
      userId,
      userRole,
      socketId,
      connectedAt: new Date(),
      lastActivity: new Date(),
    };

    this.connectedUsers.set(socketId, connectedUser);

    // Track multiple sockets per user
    if (!this.userSockets.has(userId)) {
      this.userSockets.set(userId, new Set());
    }
    this.userSockets.get(userId)!.add(socketId);

    this.logger.log(`User connected: ${userId} (${userRole}) - Socket: ${socketId}`);
  }

  removeConnectedUser(socketId: string): void {
    const connectedUser = this.connectedUsers.get(socketId);
    if (connectedUser) {
      const { userId } = connectedUser;
      
      // Remove from connected users
      this.connectedUsers.delete(socketId);
      
      // Remove from user sockets
      const userSocketSet = this.userSockets.get(userId);
      if (userSocketSet) {
        userSocketSet.delete(socketId);
        if (userSocketSet.size === 0) {
          this.userSockets.delete(userId);
        }
      }

      this.logger.log(`User disconnected: ${userId} - Socket: ${socketId}`);
    }
  }

  updateUserActivity(socketId: string): void {
    const connectedUser = this.connectedUsers.get(socketId);
    if (connectedUser) {
      connectedUser.lastActivity = new Date();
    }
  }

  getConnectedUser(socketId: string): ConnectedUser | undefined {
    return this.connectedUsers.get(socketId);
  }

  getUserSockets(userId: string): string[] {
    const socketSet = this.userSockets.get(userId);
    return socketSet ? Array.from(socketSet) : [];
  }

  getConnectedUsersByRole(role: NotificationRole): ConnectedUser[] {
    return Array.from(this.connectedUsers.values()).filter(user => user.userRole === role);
  }

  isUserConnected(userId: string): boolean {
    return this.userSockets.has(userId);
  }

  // Notification Creation
  async createNotification(
    userId: string,
    userRole: NotificationRole,
    type: NotificationType,
    title: string,
    message: string,
    data?: any,
  ): Promise<BaseNotification> {
    const notification: BaseNotification = {
      id: uuidv4(),
      type,
      title,
      message,
      userId,
      userRole,
      data,
      timestamp: new Date(),
      read: false,
    };

    // Save to database
    await this.saveNotificationToDatabase(notification);

    return notification;
  }

  // Send Notification to Specific User
  async sendNotificationToUser(dto: SendNotificationDto): Promise<NotificationResponse> {
    try {
      const notification = await this.createNotification(
        dto.userId,
        dto.userRole,
        dto.type,
        dto.title,
        dto.message,
        dto.data,
      );

      const socketIds = this.getUserSockets(dto.userId);
      
      return {
        success: true,
        message: 'Notification sent successfully',
        notificationId: notification.id,
        deliveredTo: socketIds.length,
      };
    } catch (error) {
      this.logger.error('Failed to send notification:', error);
      return {
        success: false,
        message: 'Failed to send notification',
      };
    }
  }

  // Broadcast Notification to Role
  async broadcastNotificationToRole(dto: BroadcastNotificationDto): Promise<NotificationResponse> {
    try {
      let targetUsers = this.getConnectedUsersByRole(dto.targetRole);

      // Apply filters if provided
      if (dto.filter) {
        targetUsers = await this.filterUsersByCriteria(targetUsers, dto.filter);
      }

      let deliveredCount = 0;
      for (const user of targetUsers) {
        await this.createNotification(
          user.userId,
          user.userRole,
          dto.type,
          dto.title,
          dto.message,
          dto.data,
        );
        deliveredCount++;
      }

      return {
        success: true,
        message: 'Broadcast notification sent successfully',
        deliveredTo: deliveredCount,
      };
    } catch (error) {
      this.logger.error('Failed to broadcast notification:', error);
      return {
        success: false,
        message: 'Failed to broadcast notification',
      };
    }
  }

  // Get Notification History
  async getNotificationHistory(query: NotificationHistoryQueryDto): Promise<NotificationHistoryResponse> {
    try {
      const supabase = this.supabaseService.getAdminClient();
      
      let queryBuilder = supabase
        .from('notification_history')
        .select('*', { count: 'exact' });

      // Apply filters
      if (query.userId) {
        queryBuilder = queryBuilder.eq('user_id', query.userId);
      }
      if (query.type) {
        queryBuilder = queryBuilder.eq('type', query.type);
      }
      if (query.read !== undefined) {
        queryBuilder = queryBuilder.eq('read', query.read);
      }
      if (query.startDate) {
        queryBuilder = queryBuilder.gte('timestamp', query.startDate);
      }
      if (query.endDate) {
        queryBuilder = queryBuilder.lte('timestamp', query.endDate);
      }

      // Pagination
      const page = query.page || 1;
      const limit = query.limit || 20;
      const offset = (page - 1) * limit;
      queryBuilder = queryBuilder
        .order('timestamp', { ascending: false })
        .range(offset, offset + limit - 1);

      const { data, error, count } = await queryBuilder;

      if (error) {
        throw error;
      }

      return {
        notifications: data || [],
        total: count || 0,
        page: page,
        limit: limit,
        totalPages: Math.ceil((count || 0) / limit),
      };
    } catch (error) {
      this.logger.error('Failed to get notification history:', error);
      throw error;
    }
  }

  // Mark Notification as Read
  async markNotificationAsRead(notificationId: string, userId: string): Promise<boolean> {
    try {
      const supabase = this.supabaseService.getAdminClient();
      
      const { error } = await supabase
        .from('notification_history')
        .update({ read: true })
        .eq('id', notificationId)
        .eq('user_id', userId);

      if (error) {
        throw error;
      }

      return true;
    } catch (error) {
      this.logger.error('Failed to mark notification as read:', error);
      return false;
    }
  }

  // Private Methods
  private async saveNotificationToDatabase(notification: BaseNotification): Promise<void> {
    try {
      const supabase = this.supabaseService.getAdminClient();
      
      const { error } = await supabase
        .from('notification_history')
        .insert({
          id: notification.id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          user_id: notification.userId,
          user_role: notification.userRole,
          data: notification.data,
          timestamp: notification.timestamp.toISOString(),
          read: notification.read,
        });

      if (error) {
        throw error;
      }
    } catch (error) {
      this.logger.error('Failed to save notification to database:', error);
      // Don't throw error to prevent notification delivery failure
    }
  }

  private async filterUsersByCriteria(users: ConnectedUser[], filter: any): Promise<ConnectedUser[]> {
    // This would implement filtering logic based on hospital, insurance, department, etc.
    // For now, return all users (can be enhanced based on specific requirements)
    return users;
  }

  // Utility Methods for Integration
  async sendReminderDueNotification(userId: string, reminderData: any): Promise<void> {
    await this.sendNotificationToUser({
      userId,
      userRole: NotificationRole.PATIENT,
      type: NotificationType.REMINDER_DUE,
      title: 'Medication Reminder',
      message: `Time to take your ${reminderData.medicineName}`,
      data: reminderData,
    });
  }

  async sendAdherenceUpdateNotification(userId: string, adherenceData: any): Promise<void> {
    await this.sendNotificationToUser({
      userId,
      userRole: NotificationRole.PATIENT,
      type: NotificationType.ADHERENCE_UPDATED,
      title: 'Adherence Updated',
      message: `Your medication adherence has been updated`,
      data: adherenceData,
    });
  }

  async sendAchievementNotification(userId: string, achievementData: any): Promise<void> {
    await this.sendNotificationToUser({
      userId,
      userRole: NotificationRole.PATIENT,
      type: NotificationType.ACHIEVEMENT_UNLOCKED,
      title: 'Achievement Unlocked!',
      message: `Congratulations! You've earned: ${achievementData.achievementName}`,
      data: achievementData,
    });
  }

  async sendCriticalAdherenceAlert(doctorId: string, patientData: any): Promise<void> {
    await this.sendNotificationToUser({
      userId: doctorId,
      userRole: NotificationRole.DOCTOR,
      type: NotificationType.ADHERENCE_CRITICAL,
      title: 'Critical Adherence Alert',
      message: `Patient ${patientData.patientName} has critical adherence issues`,
      data: patientData,
    });
  }
}
