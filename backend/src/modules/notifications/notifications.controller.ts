import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Query,
  UseGuards,
  ParseUUI<PERSON>ipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { NotificationsService } from './notifications.service';
import { NotificationsGateway } from './notifications.gateway';
import {
  SendNotificationDto,
  BroadcastNotificationDto,
  NotificationHistoryQueryDto,
  NotificationRole,
} from './dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../../common/types';

@ApiTags('notifications')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('notifications')
export class NotificationsController {
  constructor(
    private readonly notificationsService: NotificationsService,
    private readonly notificationsGateway: NotificationsGateway,
  ) {}

  @Post('send')
  @ApiOperation({ summary: 'Send notification to specific user' })
  @ApiResponse({ status: 201, description: 'Notification sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('admin', 'doctor', 'hospital')
  async sendNotification(
    @Body() sendNotificationDto: SendNotificationDto,
    @CurrentUser() currentUser: User,
  ) {
    const result = await this.notificationsService.sendNotificationToUser(sendNotificationDto);
    
    if (result.success) {
      // Emit real-time notification
      await this.notificationsGateway.server
        .to(`user:${sendNotificationDto.userId}`)
        .emit('notification', {
          id: result.notificationId,
          type: sendNotificationDto.type,
          title: sendNotificationDto.title,
          message: sendNotificationDto.message,
          data: sendNotificationDto.data,
          timestamp: new Date(),
        });
    }

    return result;
  }

  @Post('broadcast')
  @ApiOperation({ summary: 'Broadcast notification to user role' })
  @ApiResponse({ status: 201, description: 'Broadcast notification sent successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('admin', 'hospital')
  async broadcastNotification(
    @Body() broadcastNotificationDto: BroadcastNotificationDto,
    @CurrentUser() currentUser: User,
  ) {
    const result = await this.notificationsService.broadcastNotificationToRole(broadcastNotificationDto);
    
    if (result.success) {
      // Emit real-time broadcast
      await this.notificationsGateway.server
        .to(`role:${broadcastNotificationDto.targetRole}`)
        .emit('notification', {
          type: broadcastNotificationDto.type,
          title: broadcastNotificationDto.title,
          message: broadcastNotificationDto.message,
          data: broadcastNotificationDto.data,
          timestamp: new Date(),
        });
    }

    return result;
  }

  @Get('history')
  @ApiOperation({ summary: 'Get notification history' })
  @ApiQuery({ name: 'userId', required: false, description: 'Filter by user ID (admin only)' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by notification type' })
  @ApiQuery({ name: 'read', required: false, description: 'Filter by read status' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Start date filter' })
  @ApiQuery({ name: 'endDate', required: false, description: 'End date filter' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiResponse({ status: 200, description: 'Notification history retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('patient', 'doctor', 'hospital', 'insurance', 'admin')
  async getNotificationHistory(
    @Query() query: NotificationHistoryQueryDto,
    @CurrentUser() currentUser: User,
  ) {
    // Ensure user can only get their own notifications unless admin
    if (currentUser.role !== 'admin' && query.userId && query.userId !== currentUser.id) {
      throw new Error('Access denied to other user notifications');
    }

    // Set userId to current user if not provided and not admin
    if (!query.userId && currentUser.role !== 'admin') {
      query.userId = currentUser.id;
    }

    return this.notificationsService.getNotificationHistory(query);
  }

  @Patch(':id/read')
  @ApiOperation({ summary: 'Mark notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read successfully' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('patient', 'doctor', 'hospital', 'insurance', 'admin')
  async markNotificationAsRead(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    const success = await this.notificationsService.markNotificationAsRead(id, currentUser.id);
    
    if (success) {
      // Emit real-time update
      await this.notificationsGateway.server
        .to(`user:${currentUser.id}`)
        .emit('notification-read', {
          notificationId: id,
          timestamp: new Date(),
        });
    }

    return { success, notificationId: id };
  }

  @Get('connected-users')
  @ApiOperation({ summary: 'Get connected users (admin only)' })
  @ApiResponse({ status: 200, description: 'Connected users retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('admin')
  async getConnectedUsers(@CurrentUser() currentUser: User) {
    const connectedUsers = Array.from((this.notificationsService as any).connectedUsers.values());
    return {
      total: connectedUsers.length,
      users: connectedUsers,
      byRole: this.groupUsersByRole(connectedUsers),
    };
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get notification statistics' })
  @ApiResponse({ status: 200, description: 'Notification statistics retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('admin', 'hospital', 'doctor')
  async getNotificationStats(@CurrentUser() currentUser: User) {
    // This would return statistics about notifications
    // For now, return basic connected user stats
    const connectedUsers = Array.from((this.notificationsService as any).connectedUsers.values());
    
    return {
      connectedUsers: connectedUsers.length,
      usersByRole: this.groupUsersByRole(connectedUsers),
      timestamp: new Date(),
    };
  }

  // Test endpoints for development
  @Post('test/reminder')
  @ApiOperation({ summary: 'Test reminder notification (development only)' })
  @ApiResponse({ status: 201, description: 'Test reminder sent successfully' })
  @Roles('admin')
  async testReminderNotification(
    @Body() data: { userId: string; medicineName: string },
    @CurrentUser() currentUser: User,
  ) {
    await this.notificationsGateway.emitReminderDue(data.userId, {
      medicineName: data.medicineName,
      dosage: '1 tablet',
      scheduledTime: new Date(),
    });

    return { success: true, message: 'Test reminder notification sent' };
  }

  @Post('test/achievement')
  @ApiOperation({ summary: 'Test achievement notification (development only)' })
  @ApiResponse({ status: 201, description: 'Test achievement sent successfully' })
  @Roles('admin')
  async testAchievementNotification(
    @Body() data: { userId: string; achievementName: string },
    @CurrentUser() currentUser: User,
  ) {
    await this.notificationsGateway.emitAchievementUnlocked(data.userId, {
      achievementName: data.achievementName,
      description: 'Test achievement',
      points: 100,
      badge: 'test-badge',
    });

    return { success: true, message: 'Test achievement notification sent' };
  }

  @Post('test/critical-alert')
  @ApiOperation({ summary: 'Test critical adherence alert (development only)' })
  @ApiResponse({ status: 201, description: 'Test critical alert sent successfully' })
  @Roles('admin')
  async testCriticalAlert(
    @Body() data: { doctorId: string; patientName: string },
    @CurrentUser() currentUser: User,
  ) {
    await this.notificationsGateway.emitCriticalAdherenceAlert(data.doctorId, {
      patientName: data.patientName,
      adherenceRate: 45,
      riskLevel: 'critical',
    });

    return { success: true, message: 'Test critical alert sent' };
  }

  // Private helper methods
  private groupUsersByRole(users: any[]): Record<string, number> {
    return users.reduce((acc, user) => {
      acc[user.userRole] = (acc[user.userRole] || 0) + 1;
      return acc;
    }, {});
  }
}
