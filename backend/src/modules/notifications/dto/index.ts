import { IsString, IsUUID, IsOptional, IsEnum, IsObject, IsDateString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

// Notification Types
export enum NotificationType {
  REMINDER_DUE = 'reminder:due',
  REMINDER_SENT = 'reminder:sent',
  REMINDER_FAILED = 'reminder:failed',
  ADHERENCE_UPDATED = 'adherence:updated',
  ADHERENCE_CRITICAL = 'adherence:critical',
  ACHIEVEMENT_UNLOCKED = 'achievement:unlocked',
  POINTS_UPDATED = 'points:updated',
  MEDICATION_ADDED = 'medication:added',
  PATIENT_MISSED_MEDICATION = 'patient:missed:medication',
  DASHBOARD_UPDATE = 'dashboard:update',
  RISK_ASSESSMENT_CHANGE = 'risk:assessment:change',
}

// User Roles for Notifications
export enum NotificationRole {
  PATIENT = 'patient',
  DOCTOR = 'doctor',
  HOSPITAL = 'hospital',
  INSURANCE = 'insurance',
  ADMIN = 'admin',
}

// Base Notification Interface
export interface BaseNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  userId: string;
  userRole: NotificationRole;
  data?: any;
  timestamp: Date;
  read: boolean;
}

// Patient Notification Interfaces
export interface ReminderDueNotification extends BaseNotification {
  type: NotificationType.REMINDER_DUE;
  data: {
    reminderId: string;
    medicineId: string;
    medicineName: string;
    dosage: string;
    scheduledTime: Date;
  };
}

export interface AdherenceUpdatedNotification extends BaseNotification {
  type: NotificationType.ADHERENCE_UPDATED;
  data: {
    medicineId: string;
    medicineName: string;
    status: 'taken' | 'missed' | 'skipped';
    timestamp: Date;
    streak: number;
    points: number;
  };
}

export interface AchievementUnlockedNotification extends BaseNotification {
  type: NotificationType.ACHIEVEMENT_UNLOCKED;
  data: {
    achievementId: string;
    achievementName: string;
    description: string;
    points: number;
    badge: string;
  };
}

export interface PointsUpdatedNotification extends BaseNotification {
  type: NotificationType.POINTS_UPDATED;
  data: {
    totalPoints: number;
    pointsEarned: number;
    currentStreak: number;
    level: number;
    reason: string;
  };
}

// Healthcare Provider Notification Interfaces
export interface PatientAdherenceCriticalNotification extends BaseNotification {
  type: NotificationType.ADHERENCE_CRITICAL;
  data: {
    patientId: string;
    patientName: string;
    medicineId: string;
    medicineName: string;
    adherenceRate: number;
    missedDoses: number;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
  };
}

export interface PatientMissedMedicationNotification extends BaseNotification {
  type: NotificationType.PATIENT_MISSED_MEDICATION;
  data: {
    patientId: string;
    patientName: string;
    medicineId: string;
    medicineName: string;
    scheduledTime: Date;
    missedTime: Date;
    consecutiveMissed: number;
  };
}

export interface DashboardUpdateNotification extends BaseNotification {
  type: NotificationType.DASHBOARD_UPDATE;
  data: {
    totalPatients: number;
    criticalPatients: number;
    adherenceRate: number;
    recentAlerts: number;
    updatedAt: Date;
  };
}

// Insurance Provider Notification Interfaces
export interface RiskAssessmentChangeNotification extends BaseNotification {
  type: NotificationType.RISK_ASSESSMENT_CHANGE;
  data: {
    policyHolderId: string;
    policyHolderName: string;
    previousRiskLevel: string;
    newRiskLevel: string;
    adherenceRate: number;
    riskFactors: string[];
  };
}

// WebSocket Event DTOs
export class SendNotificationDto {
  @ApiProperty({ description: 'User ID to send notification to' })
  @IsUUID()
  userId: string;

  @ApiProperty({ description: 'User role', enum: NotificationRole })
  @IsEnum(NotificationRole)
  userRole: NotificationRole;

  @ApiProperty({ description: 'Notification type', enum: NotificationType })
  @IsEnum(NotificationType)
  type: NotificationType;

  @ApiProperty({ description: 'Notification title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Notification message' })
  @IsString()
  message: string;

  @ApiProperty({ description: 'Additional notification data', required: false })
  @IsOptional()
  @IsObject()
  data?: any;
}

export class BroadcastNotificationDto {
  @ApiProperty({ description: 'Target user role', enum: NotificationRole })
  @IsEnum(NotificationRole)
  targetRole: NotificationRole;

  @ApiProperty({ description: 'Notification type', enum: NotificationType })
  @IsEnum(NotificationType)
  type: NotificationType;

  @ApiProperty({ description: 'Notification title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Notification message' })
  @IsString()
  message: string;

  @ApiProperty({ description: 'Additional notification data', required: false })
  @IsOptional()
  @IsObject()
  data?: any;

  @ApiProperty({ description: 'Filter criteria', required: false })
  @IsOptional()
  @IsObject()
  filter?: {
    hospitalId?: string;
    insuranceId?: string;
    departmentId?: string;
  };
}

export class NotificationHistoryQueryDto {
  @ApiProperty({ description: 'User ID', required: false })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiProperty({ description: 'Notification type', enum: NotificationType, required: false })
  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @ApiProperty({ description: 'Read status', required: false })
  @IsOptional()
  read?: boolean;

  @ApiProperty({ description: 'Start date', required: false })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ description: 'End date', required: false })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({ description: 'Page number', required: false, default: 1 })
  @IsOptional()
  page?: number = 1;

  @ApiProperty({ description: 'Items per page', required: false, default: 20 })
  @IsOptional()
  limit?: number = 20;
}

// WebSocket Connection Info
export interface ConnectedUser {
  userId: string;
  userRole: NotificationRole;
  socketId: string;
  connectedAt: Date;
  lastActivity: Date;
}

// Notification Response Interfaces
export interface NotificationResponse {
  success: boolean;
  message: string;
  notificationId?: string;
  deliveredTo?: number;
}

export interface NotificationHistoryResponse {
  notifications: BaseNotification[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
