import { Module } from '@nestjs/common';
import { InsuranceController } from './insurance.controller';
import { InsuranceService } from './insurance.service';
import { SupabaseService } from '../../config/supabase.service';
import { AdherenceService } from '../adherence/adherence.service';
import { GamificationService } from '../gamification/gamification.service';

@Module({
  controllers: [InsuranceController],
  providers: [
    InsuranceService,
    SupabaseService,
    AdherenceService,
    GamificationService,
  ],
  exports: [InsuranceService],
})
export class InsuranceModule {}
