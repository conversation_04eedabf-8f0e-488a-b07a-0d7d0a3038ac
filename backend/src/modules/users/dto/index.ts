import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEmail, IsEnum, IsOptional, IsUUID, IsDateString, IsArray, IsPhoneNumber } from 'class-validator';
import { UserRole } from '../../../common/types';

export class CreateUserDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: '<PERSON> Do<PERSON>' })
  @IsString()
  name: string;

  @ApiProperty({ enum: ['patient', 'doctor', 'hospital', 'admin', 'insurance'] })
  @IsEnum(['patient', 'doctor', 'hospital', 'admin', 'insurance'])
  role: UserRole;

  @ApiPropertyOptional({ example: 'https://example.com/avatar.jpg' })
  @IsOptional()
  @IsString()
  avatar?: string;
}

export class UpdateUserDto {
  @ApiPropertyOptional({ example: '<PERSON>' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ example: 'https://example.com/new-avatar.jpg' })
  @IsOptional()
  @IsString()
  avatar?: string;
}

export class CreatePatientDto extends CreateUserDto {
  @ApiProperty({ enum: ['patient'] })
  @IsEnum(['patient'])
  declare role: 'patient';

  @ApiPropertyOptional({ example: '1990-01-01' })
  @IsOptional()
  @IsDateString()
  date_of_birth?: string;

  @ApiPropertyOptional({ example: 'Jane Doe - 555-0123' })
  @IsOptional()
  @IsString()
  emergency_contact?: string;

  @ApiPropertyOptional({ example: 'uuid-of-assigned-doctor' })
  @IsOptional()
  @IsUUID()
  assigned_doctor_id?: string;

  @ApiPropertyOptional({ example: 'uuid-of-insurance-provider' })
  @IsOptional()
  @IsUUID()
  insurance_id?: string;
}

export class CreateDoctorDto extends CreateUserDto {
  @ApiProperty({ enum: ['doctor'] })
  @IsEnum(['doctor'])
  declare role: 'doctor';

  @ApiProperty({ example: 'Cardiology' })
  @IsString()
  specialization: string;

  @ApiProperty({ example: 'MD123456' })
  @IsString()
  license_number: string;

  @ApiPropertyOptional({ example: 'uuid-of-hospital' })
  @IsOptional()
  @IsUUID()
  hospital_id?: string;
}

export class CreateHospitalDto extends CreateUserDto {
  @ApiProperty({ enum: ['hospital'] })
  @IsEnum(['hospital'])
  declare role: 'hospital';

  @ApiProperty({ example: '123 Medical Center Dr, City, State 12345' })
  @IsString()
  address: string;

  @ApiProperty({ example: '******-0123' })
  @IsPhoneNumber()
  phone: string;

  @ApiPropertyOptional({ example: 'https://hospital.com' })
  @IsOptional()
  @IsString()
  website?: string;
}

export class CreateInsuranceDto extends CreateUserDto {
  @ApiProperty({ enum: ['insurance'] })
  @IsEnum(['insurance'])
  declare role: 'insurance';

  @ApiProperty({ example: 'HealthCare Insurance Co.' })
  @IsString()
  company_name: string;

  @ApiProperty({ example: '456 Insurance Blvd, City, State 12345' })
  @IsString()
  address: string;

  @ApiProperty({ example: '******-0456' })
  @IsPhoneNumber()
  phone: string;

  @ApiPropertyOptional({ example: 'https://insurance.com' })
  @IsOptional()
  @IsString()
  website?: string;

  @ApiPropertyOptional({ example: ['health', 'dental', 'vision'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  policy_types?: string[];

  @ApiPropertyOptional({ example: ['nationwide', 'regional'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  coverage_areas?: string[];
}

export class UpdatePatientDto {
  @ApiPropertyOptional({ example: 'John Doe Updated' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ example: 'https://example.com/new-avatar.jpg' })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ example: '1990-01-01' })
  @IsOptional()
  @IsDateString()
  date_of_birth?: string;

  @ApiPropertyOptional({ example: 'Jane Doe - 555-0123' })
  @IsOptional()
  @IsString()
  emergency_contact?: string;

  @ApiPropertyOptional({ example: 'uuid-of-assigned-doctor' })
  @IsOptional()
  @IsUUID()
  assigned_doctor_id?: string;

  @ApiPropertyOptional({ example: 'uuid-of-insurance-provider' })
  @IsOptional()
  @IsUUID()
  insurance_id?: string;
}

export class UpdateDoctorDto {
  @ApiPropertyOptional({ example: 'Dr. John Doe Updated' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ example: 'https://example.com/new-avatar.jpg' })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ example: 'Pediatric Cardiology' })
  @IsOptional()
  @IsString()
  specialization?: string;

  @ApiPropertyOptional({ example: 'uuid-of-hospital' })
  @IsOptional()
  @IsUUID()
  hospital_id?: string;
}

export class UpdateHospitalDto {
  @ApiPropertyOptional({ example: 'Updated Hospital Name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ example: 'https://example.com/new-avatar.jpg' })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ example: '123 Updated Medical Center Dr, City, State 12345' })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({ example: '******-0789' })
  @IsOptional()
  @IsPhoneNumber()
  phone?: string;

  @ApiPropertyOptional({ example: 'https://updated-hospital.com' })
  @IsOptional()
  @IsString()
  website?: string;
}

export class UpdateInsuranceDto {
  @ApiPropertyOptional({ example: 'Updated Insurance Co.' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ example: 'https://example.com/new-avatar.jpg' })
  @IsOptional()
  @IsString()
  avatar?: string;

  @ApiPropertyOptional({ example: 'Updated HealthCare Insurance Co.' })
  @IsOptional()
  @IsString()
  company_name?: string;

  @ApiPropertyOptional({ example: '456 Updated Insurance Blvd, City, State 12345' })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({ example: '******-0999' })
  @IsOptional()
  @IsPhoneNumber()
  phone?: string;

  @ApiPropertyOptional({ example: 'https://updated-insurance.com' })
  @IsOptional()
  @IsString()
  website?: string;

  @ApiPropertyOptional({ example: ['health', 'dental', 'vision', 'life'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  policy_types?: string[];

  @ApiPropertyOptional({ example: ['nationwide', 'international'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  coverage_areas?: string[];
}
