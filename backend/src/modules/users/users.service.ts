import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { SupabaseService } from '../../config/supabase.service';
import { User, UserRole } from '../../common/types';
import {
  CreatePatientDto,
  CreateDoctorDto,
  CreateHospitalDto,
  CreateInsuranceDto,
  UpdatePatientDto,
  UpdateDoctorDto,
  UpdateHospitalDto,
  UpdateInsuranceDto,
} from './dto';

@Injectable()
export class UsersService {
  constructor(private readonly supabaseService: SupabaseService) {}

  async findAll(role?: UserRole): Promise<User[]> {
    const supabase = this.supabaseService.getClient();
    
    let query = supabase.from('users').select('*');
    
    if (role) {
      query = query.eq('role', role);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) {
      throw new BadRequestException(`Failed to fetch users: ${error.message}`);
    }
    
    return data || [];
  }

  async findOne(id: string): Promise<User> {
    const supabase = this.supabaseService.getClient();
    
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error || !data) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    
    return data;
  }

  async findByEmail(email: string): Promise<User | null> {
    const supabase = this.supabaseService.getClient();
    
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();
    
    if (error) {
      return null;
    }
    
    return data;
  }

  async findPatientDetails(id: string): Promise<any> {
    const supabase = this.supabaseService.getAdminClient();

    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        patients!patients_id_fkey (
          date_of_birth,
          emergency_contact,
          assigned_doctor_id,
          insurance_id
        )
      `)
      .eq('id', id)
      .single();

    if (error || !data) {
      throw new NotFoundException(`Patient with ID ${id} not found`);
    }

    // Handle the case where patients might be null or an object (not array)
    const patientData = data.patients || {};

    const result = {
      ...data,
      ...patientData,
      patients: undefined,
    };

    return result;
  }

  async findDoctorDetails(id: string): Promise<any> {
    const supabase = this.supabaseService.getClient();
    
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        doctors (
          specialization,
          license_number,
          hospital_id,
          hospital:hospital_id (id, name, email)
        )
      `)
      .eq('id', id)
      .eq('role', 'doctor')
      .single();
    
    if (error || !data) {
      throw new NotFoundException(`Doctor with ID ${id} not found`);
    }
    
    return {
      ...data,
      ...data.doctors[0],
      doctors: undefined,
    };
  }

  async findHospitalDetails(id: string): Promise<any> {
    const supabase = this.supabaseService.getClient();
    
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        hospitals (
          address,
          phone,
          website
        )
      `)
      .eq('id', id)
      .eq('role', 'hospital')
      .single();
    
    if (error || !data) {
      throw new NotFoundException(`Hospital with ID ${id} not found`);
    }
    
    return {
      ...data,
      ...data.hospitals[0],
      hospitals: undefined,
    };
  }

  async findInsuranceDetails(id: string): Promise<any> {
    const supabase = this.supabaseService.getClient();
    
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        insurance_providers (
          company_name,
          address,
          phone,
          website,
          policy_types,
          coverage_areas
        )
      `)
      .eq('id', id)
      .eq('role', 'insurance')
      .single();
    
    if (error || !data) {
      throw new NotFoundException(`Insurance provider with ID ${id} not found`);
    }
    
    return {
      ...data,
      ...data.insurance_providers[0],
      insurance_providers: undefined,
    };
  }

  async createPatient(createPatientDto: CreatePatientDto): Promise<any> {
    const supabase = this.supabaseService.getAdminClient();
    
    // Check if email already exists
    const existingUser = await this.findByEmail(createPatientDto.email);
    if (existingUser) {
      throw new BadRequestException('User with this email already exists');
    }
    
    // Create user first
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        email: createPatientDto.email,
        name: createPatientDto.name,
        role: 'patient',
        avatar: createPatientDto.avatar,
      })
      .select()
      .single();
    
    if (userError) {
      throw new BadRequestException(`Failed to create user: ${userError.message}`);
    }
    
    // Create patient record
    const { data: patientData, error: patientError } = await supabase
      .from('patients')
      .insert({
        id: userData.id,
        date_of_birth: createPatientDto.date_of_birth,
        emergency_contact: createPatientDto.emergency_contact,
        assigned_doctor_id: createPatientDto.assigned_doctor_id,
        insurance_id: createPatientDto.insurance_id,
      })
      .select()
      .single();
    
    if (patientError) {
      // Rollback user creation
      await supabase.from('users').delete().eq('id', userData.id);
      throw new BadRequestException(`Failed to create patient: ${patientError.message}`);
    }
    
    return {
      ...userData,
      ...patientData,
    };
  }

  async createDoctor(createDoctorDto: CreateDoctorDto): Promise<any> {
    const supabase = this.supabaseService.getAdminClient();
    
    // Check if email already exists
    const existingUser = await this.findByEmail(createDoctorDto.email);
    if (existingUser) {
      throw new BadRequestException('User with this email already exists');
    }
    
    // Create user first
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        email: createDoctorDto.email,
        name: createDoctorDto.name,
        role: 'doctor',
        avatar: createDoctorDto.avatar,
      })
      .select()
      .single();
    
    if (userError) {
      throw new BadRequestException(`Failed to create user: ${userError.message}`);
    }
    
    // Create doctor record
    const { data: doctorData, error: doctorError } = await supabase
      .from('doctors')
      .insert({
        id: userData.id,
        specialization: createDoctorDto.specialization,
        license_number: createDoctorDto.license_number,
        hospital_id: createDoctorDto.hospital_id,
      })
      .select()
      .single();
    
    if (doctorError) {
      // Rollback user creation
      await supabase.from('users').delete().eq('id', userData.id);
      throw new BadRequestException(`Failed to create doctor: ${doctorError.message}`);
    }
    
    return {
      ...userData,
      ...doctorData,
    };
  }

  async createHospital(createHospitalDto: CreateHospitalDto): Promise<any> {
    const supabase = this.supabaseService.getAdminClient();

    // Check if email already exists
    const existingUser = await this.findByEmail(createHospitalDto.email);
    if (existingUser) {
      throw new BadRequestException('User with this email already exists');
    }

    // Create user first
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        email: createHospitalDto.email,
        name: createHospitalDto.name,
        role: 'hospital',
        avatar: createHospitalDto.avatar,
      })
      .select()
      .single();

    if (userError) {
      throw new BadRequestException(`Failed to create user: ${userError.message}`);
    }

    // Create hospital record
    const { data: hospitalData, error: hospitalError } = await supabase
      .from('hospitals')
      .insert({
        id: userData.id,
        address: createHospitalDto.address,
        phone: createHospitalDto.phone,
        website: createHospitalDto.website,
      })
      .select()
      .single();

    if (hospitalError) {
      // Rollback user creation
      await supabase.from('users').delete().eq('id', userData.id);
      throw new BadRequestException(`Failed to create hospital: ${hospitalError.message}`);
    }

    return {
      ...userData,
      ...hospitalData,
    };
  }

  async createInsurance(createInsuranceDto: CreateInsuranceDto): Promise<any> {
    const supabase = this.supabaseService.getAdminClient();

    // Check if email already exists
    const existingUser = await this.findByEmail(createInsuranceDto.email);
    if (existingUser) {
      throw new BadRequestException('User with this email already exists');
    }

    // Create user first
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        email: createInsuranceDto.email,
        name: createInsuranceDto.name,
        role: 'insurance',
        avatar: createInsuranceDto.avatar,
      })
      .select()
      .single();

    if (userError) {
      throw new BadRequestException(`Failed to create user: ${userError.message}`);
    }

    // Create insurance record
    const { data: insuranceData, error: insuranceError } = await supabase
      .from('insurance_providers')
      .insert({
        id: userData.id,
        company_name: createInsuranceDto.company_name,
        address: createInsuranceDto.address,
        phone: createInsuranceDto.phone,
        website: createInsuranceDto.website,
        policy_types: createInsuranceDto.policy_types || [],
        coverage_areas: createInsuranceDto.coverage_areas || [],
      })
      .select()
      .single();

    if (insuranceError) {
      // Rollback user creation
      await supabase.from('users').delete().eq('id', userData.id);
      throw new BadRequestException(`Failed to create insurance provider: ${insuranceError.message}`);
    }

    return {
      ...userData,
      ...insuranceData,
    };
  }

  async updatePatient(id: string, updatePatientDto: UpdatePatientDto, currentUser: User): Promise<any> {
    // Check permissions
    if (currentUser.role !== 'admin' && currentUser.id !== id) {
      throw new ForbiddenException('You can only update your own profile');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Verify user exists and is a patient
    const user = await this.findOne(id);
    if (user.role !== 'patient') {
      throw new BadRequestException('User is not a patient');
    }

    // Update user table
    const userUpdates: any = {};
    if (updatePatientDto.name) userUpdates.name = updatePatientDto.name;
    if (updatePatientDto.avatar !== undefined) userUpdates.avatar = updatePatientDto.avatar;

    if (Object.keys(userUpdates).length > 0) {
      const { error: userError } = await supabase
        .from('users')
        .update(userUpdates)
        .eq('id', id);

      if (userError) {
        throw new BadRequestException(`Failed to update user: ${userError.message}`);
      }
    }

    // Update patient table
    const patientUpdates: any = {};
    if (updatePatientDto.date_of_birth !== undefined) patientUpdates.date_of_birth = updatePatientDto.date_of_birth;
    if (updatePatientDto.emergency_contact !== undefined) patientUpdates.emergency_contact = updatePatientDto.emergency_contact;
    if (updatePatientDto.assigned_doctor_id !== undefined) patientUpdates.assigned_doctor_id = updatePatientDto.assigned_doctor_id;
    if (updatePatientDto.insurance_id !== undefined) patientUpdates.insurance_id = updatePatientDto.insurance_id;

    if (Object.keys(patientUpdates).length > 0) {
      const { error: patientError } = await supabase
        .from('patients')
        .update(patientUpdates)
        .eq('id', id);

      if (patientError) {
        throw new BadRequestException(`Failed to update patient: ${patientError.message}`);
      }
    }

    return this.findPatientDetails(id);
  }

  async updateDoctor(id: string, updateDoctorDto: UpdateDoctorDto, currentUser: User): Promise<any> {
    // Check permissions
    if (currentUser.role !== 'admin' && currentUser.id !== id) {
      throw new ForbiddenException('You can only update your own profile');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Verify user exists and is a doctor
    const user = await this.findOne(id);
    if (user.role !== 'doctor') {
      throw new BadRequestException('User is not a doctor');
    }

    // Update user table
    const userUpdates: any = {};
    if (updateDoctorDto.name) userUpdates.name = updateDoctorDto.name;
    if (updateDoctorDto.avatar !== undefined) userUpdates.avatar = updateDoctorDto.avatar;

    if (Object.keys(userUpdates).length > 0) {
      const { error: userError } = await supabase
        .from('users')
        .update(userUpdates)
        .eq('id', id);

      if (userError) {
        throw new BadRequestException(`Failed to update user: ${userError.message}`);
      }
    }

    // Update doctor table
    const doctorUpdates: any = {};
    if (updateDoctorDto.specialization) doctorUpdates.specialization = updateDoctorDto.specialization;
    if (updateDoctorDto.hospital_id !== undefined) doctorUpdates.hospital_id = updateDoctorDto.hospital_id;

    if (Object.keys(doctorUpdates).length > 0) {
      const { error: doctorError } = await supabase
        .from('doctors')
        .update(doctorUpdates)
        .eq('id', id);

      if (doctorError) {
        throw new BadRequestException(`Failed to update doctor: ${doctorError.message}`);
      }
    }

    return this.findDoctorDetails(id);
  }

  async updateHospital(id: string, updateHospitalDto: UpdateHospitalDto, currentUser: User): Promise<any> {
    // Check permissions
    if (currentUser.role !== 'admin' && currentUser.id !== id) {
      throw new ForbiddenException('You can only update your own profile');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Verify user exists and is a hospital
    const user = await this.findOne(id);
    if (user.role !== 'hospital') {
      throw new BadRequestException('User is not a hospital');
    }

    // Update user table
    const userUpdates: any = {};
    if (updateHospitalDto.name) userUpdates.name = updateHospitalDto.name;
    if (updateHospitalDto.avatar !== undefined) userUpdates.avatar = updateHospitalDto.avatar;

    if (Object.keys(userUpdates).length > 0) {
      const { error: userError } = await supabase
        .from('users')
        .update(userUpdates)
        .eq('id', id);

      if (userError) {
        throw new BadRequestException(`Failed to update user: ${userError.message}`);
      }
    }

    // Update hospital table
    const hospitalUpdates: any = {};
    if (updateHospitalDto.address) hospitalUpdates.address = updateHospitalDto.address;
    if (updateHospitalDto.phone) hospitalUpdates.phone = updateHospitalDto.phone;
    if (updateHospitalDto.website !== undefined) hospitalUpdates.website = updateHospitalDto.website;

    if (Object.keys(hospitalUpdates).length > 0) {
      const { error: hospitalError } = await supabase
        .from('hospitals')
        .update(hospitalUpdates)
        .eq('id', id);

      if (hospitalError) {
        throw new BadRequestException(`Failed to update hospital: ${hospitalError.message}`);
      }
    }

    return this.findHospitalDetails(id);
  }

  async updateInsurance(id: string, updateInsuranceDto: UpdateInsuranceDto, currentUser: User): Promise<any> {
    // Check permissions
    if (currentUser.role !== 'admin' && currentUser.id !== id) {
      throw new ForbiddenException('You can only update your own profile');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Verify user exists and is an insurance provider
    const user = await this.findOne(id);
    if (user.role !== 'insurance') {
      throw new BadRequestException('User is not an insurance provider');
    }

    // Update user table
    const userUpdates: any = {};
    if (updateInsuranceDto.name) userUpdates.name = updateInsuranceDto.name;
    if (updateInsuranceDto.avatar !== undefined) userUpdates.avatar = updateInsuranceDto.avatar;

    if (Object.keys(userUpdates).length > 0) {
      const { error: userError } = await supabase
        .from('users')
        .update(userUpdates)
        .eq('id', id);

      if (userError) {
        throw new BadRequestException(`Failed to update user: ${userError.message}`);
      }
    }

    // Update insurance table
    const insuranceUpdates: any = {};
    if (updateInsuranceDto.company_name) insuranceUpdates.company_name = updateInsuranceDto.company_name;
    if (updateInsuranceDto.address) insuranceUpdates.address = updateInsuranceDto.address;
    if (updateInsuranceDto.phone) insuranceUpdates.phone = updateInsuranceDto.phone;
    if (updateInsuranceDto.website !== undefined) insuranceUpdates.website = updateInsuranceDto.website;
    if (updateInsuranceDto.policy_types) insuranceUpdates.policy_types = updateInsuranceDto.policy_types;
    if (updateInsuranceDto.coverage_areas) insuranceUpdates.coverage_areas = updateInsuranceDto.coverage_areas;

    if (Object.keys(insuranceUpdates).length > 0) {
      const { error: insuranceError } = await supabase
        .from('insurance_providers')
        .update(insuranceUpdates)
        .eq('id', id);

      if (insuranceError) {
        throw new BadRequestException(`Failed to update insurance provider: ${insuranceError.message}`);
      }
    }

    return this.findInsuranceDetails(id);
  }

  async remove(id: string, currentUser: User): Promise<void> {
    // Only admins can delete users
    if (currentUser.role !== 'admin') {
      throw new ForbiddenException('Only administrators can delete users');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Verify user exists
    await this.findOne(id);

    // Delete user (cascade will handle role-specific tables)
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', id);

    if (error) {
      throw new BadRequestException(`Failed to delete user: ${error.message}`);
    }
  }
}
