import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { UsersService } from './users.service';
import {
  CreatePatientDto,
  CreateDoctorDto,
  CreateHospitalDto,
  CreateInsuranceDto,
  UpdatePatientDto,
  UpdateDoctorDto,
  UpdateHospitalDto,
  UpdateInsuranceDto,
} from './dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User, UserRole } from '../../common/types';

@ApiTags('users')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @ApiOperation({ summary: 'Get all users' })
  @ApiQuery({ name: 'role', required: false, enum: ['patient', 'doctor', 'hospital', 'admin', 'insurance'] })
  @ApiResponse({ status: 200, description: 'Users retrieved successfully' })
  @Roles('admin', 'doctor', 'hospital', 'insurance')
  async findAll(@Query('role') role?: UserRole) {
    return this.usersService.findAll(role);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get user by ID' })
  @ApiResponse({ status: 200, description: 'User retrieved successfully' })
  @ApiResponse({ status: 404, description: 'User not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.usersService.findOne(id);
  }

  @Get(':id/patient-details')
  @ApiOperation({ summary: 'Get patient details' })
  @ApiResponse({ status: 200, description: 'Patient details retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Patient not found' })
  @Roles('admin', 'doctor', 'hospital', 'insurance', 'patient')
  async getPatientDetails(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    // Patients can only view their own details, others need appropriate roles
    if (currentUser.role === 'patient' && currentUser.id !== id) {
      throw new Error('You can only view your own patient details');
    }
    return this.usersService.findPatientDetails(id);
  }

  @Get(':id/doctor-details')
  @ApiOperation({ summary: 'Get doctor details' })
  @ApiResponse({ status: 200, description: 'Doctor details retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Doctor not found' })
  @Roles('admin', 'doctor', 'hospital', 'patient')
  async getDoctorDetails(@Param('id', ParseUUIDPipe) id: string) {
    return this.usersService.findDoctorDetails(id);
  }

  @Get(':id/hospital-details')
  @ApiOperation({ summary: 'Get hospital details' })
  @ApiResponse({ status: 200, description: 'Hospital details retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Hospital not found' })
  @Roles('admin', 'doctor', 'hospital', 'patient', 'insurance')
  async getHospitalDetails(@Param('id', ParseUUIDPipe) id: string) {
    return this.usersService.findHospitalDetails(id);
  }

  @Get(':id/insurance-details')
  @ApiOperation({ summary: 'Get insurance provider details' })
  @ApiResponse({ status: 200, description: 'Insurance provider details retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Insurance provider not found' })
  @Roles('admin', 'insurance', 'patient', 'hospital')
  async getInsuranceDetails(@Param('id', ParseUUIDPipe) id: string) {
    return this.usersService.findInsuranceDetails(id);
  }

  @Post('patients')
  @ApiOperation({ summary: 'Create a new patient' })
  @ApiResponse({ status: 201, description: 'Patient created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @Roles('admin', 'doctor', 'hospital')
  async createPatient(@Body() createPatientDto: CreatePatientDto) {
    return this.usersService.createPatient(createPatientDto);
  }

  @Post('doctors')
  @ApiOperation({ summary: 'Create a new doctor' })
  @ApiResponse({ status: 201, description: 'Doctor created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @Roles('admin', 'hospital')
  async createDoctor(@Body() createDoctorDto: CreateDoctorDto) {
    return this.usersService.createDoctor(createDoctorDto);
  }

  @Post('hospitals')
  @ApiOperation({ summary: 'Create a new hospital' })
  @ApiResponse({ status: 201, description: 'Hospital created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @Roles('admin')
  async createHospital(@Body() createHospitalDto: CreateHospitalDto) {
    return this.usersService.createHospital(createHospitalDto);
  }

  @Post('insurance')
  @ApiOperation({ summary: 'Create a new insurance provider' })
  @ApiResponse({ status: 201, description: 'Insurance provider created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @Roles('admin')
  async createInsurance(@Body() createInsuranceDto: CreateInsuranceDto) {
    return this.usersService.createInsurance(createInsuranceDto);
  }

  @Patch(':id/patient')
  @ApiOperation({ summary: 'Update patient details' })
  @ApiResponse({ status: 200, description: 'Patient updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Patient not found' })
  @Roles('admin', 'patient', 'doctor')
  async updatePatient(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePatientDto: UpdatePatientDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.usersService.updatePatient(id, updatePatientDto, currentUser);
  }

  @Patch(':id/doctor')
  @ApiOperation({ summary: 'Update doctor details' })
  @ApiResponse({ status: 200, description: 'Doctor updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Doctor not found' })
  @Roles('admin', 'doctor', 'hospital')
  async updateDoctor(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDoctorDto: UpdateDoctorDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.usersService.updateDoctor(id, updateDoctorDto, currentUser);
  }

  @Patch(':id/hospital')
  @ApiOperation({ summary: 'Update hospital details' })
  @ApiResponse({ status: 200, description: 'Hospital updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Hospital not found' })
  @Roles('admin', 'hospital')
  async updateHospital(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateHospitalDto: UpdateHospitalDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.usersService.updateHospital(id, updateHospitalDto, currentUser);
  }

  @Patch(':id/insurance')
  @ApiOperation({ summary: 'Update insurance provider details' })
  @ApiResponse({ status: 200, description: 'Insurance provider updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Insurance provider not found' })
  @Roles('admin', 'insurance')
  async updateInsurance(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateInsuranceDto: UpdateInsuranceDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.usersService.updateInsurance(id, updateInsuranceDto, currentUser);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete user' })
  @ApiResponse({ status: 200, description: 'User deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @Roles('admin')
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    await this.usersService.remove(id, currentUser);
    return { message: 'User deleted successfully' };
  }
}
