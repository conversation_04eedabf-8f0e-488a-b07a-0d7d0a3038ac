import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsEnum, IsArray, IsDateString, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';

// Query DTOs
export class HospitalPatientsQueryDto {
  @ApiPropertyOptional({ example: 1, description: 'Page number' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ example: 10, description: 'Items per page' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ example: 'John', description: 'Search by patient name' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ example: 'doctor_id', description: 'Filter by assigned doctor' })
  @IsOptional()
  @IsString()
  doctor_id?: string;

  @ApiPropertyOptional({ example: 'General Medicine', description: 'Filter by doctor specialization' })
  @IsOptional()
  @IsString()
  specialization?: string;
}

export class HospitalAdherenceReportQueryDto {
  @ApiPropertyOptional({ example: 30, description: 'Number of days to analyze' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(365)
  days?: number = 30;

  @ApiPropertyOptional({ example: true, description: 'Include detailed medication breakdown' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  include_medications?: boolean = false;

  @ApiPropertyOptional({ example: true, description: 'Include gamification data' })
  @IsOptional()
  @Transform(({ value }) => value === 'true')
  include_gamification?: boolean = false;
}

export class HospitalBulkAdherenceQueryDto {
  @ApiProperty({ example: ['patient1', 'patient2'], description: 'Array of patient IDs' })
  @IsArray()
  @IsString({ each: true })
  patient_ids: string[];

  @ApiPropertyOptional({ example: 30, description: 'Number of days to analyze' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(365)
  days?: number = 30;

  @ApiPropertyOptional({ example: true, description: 'Include detailed breakdown for each patient' })
  @IsOptional()
  include_details?: boolean = false;
}

export class HospitalDepartmentAnalysisQueryDto {
  @ApiPropertyOptional({ example: 30, description: 'Number of days to analyze' })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  @Max(365)
  days?: number = 30;

  @ApiPropertyOptional({ example: 'General Medicine', description: 'Filter by department/specialization' })
  @IsOptional()
  @IsString()
  department?: string;

  @ApiPropertyOptional({ example: 70, description: 'Minimum adherence rate threshold' })
  @IsOptional()
  @Transform(({ value }) => parseFloat(value))
  @IsNumber()
  @Min(0)
  @Max(100)
  min_adherence_rate?: number = 0;

  @ApiPropertyOptional({ example: 'low', description: 'Risk level filter' })
  @IsOptional()
  @IsEnum(['low', 'medium', 'high', 'critical'])
  risk_level?: 'low' | 'medium' | 'high' | 'critical';
}

// Response Interfaces
export interface HospitalPatient {
  patient_id: string;
  patient_name: string;
  patient_email: string;
  date_of_birth?: Date;
  emergency_contact?: string;
  assigned_doctor: {
    id: string;
    name: string;
    specialization: string;
  };
  admission_date?: Date;
  current_medications_count: number;
  last_adherence_check: Date;
}

export interface HospitalAdherenceReport {
  patient_id: string;
  patient_name: string;
  hospital_id: string;
  report_date: Date;
  analysis_period: {
    start_date: Date;
    end_date: Date;
    days_analyzed: number;
  };
  overall_adherence: {
    adherence_rate: number;
    total_doses: number;
    taken_doses: number;
    missed_doses: number;
    current_streak: number;
    longest_streak: number;
  };
  medication_details?: {
    medicine_id: string;
    medicine_name: string;
    dosage: string;
    frequency: string;
    adherence_rate: number;
    missed_doses: number;
    last_taken?: Date;
  }[];
  weekly_trends: {
    week_start: Date;
    adherence_rate: number;
    improvement: number;
  }[];
  assigned_doctor: {
    id: string;
    name: string;
    specialization: string;
  };
  gamification_data?: {
    total_points: number;
    current_streak: number;
    longest_streak: number;
    completion_rate: number;
    level: string;
  };
  recommendations: string[];
  risk_assessment: {
    risk_level: 'low' | 'medium' | 'high' | 'critical';
    risk_factors: string[];
    intervention_needed: boolean;
  };
}

export interface HospitalBulkAdherenceReport {
  hospital_id: string;
  report_date: Date;
  analysis_period: {
    start_date: Date;
    end_date: Date;
    days_analyzed: number;
  };
  summary: {
    total_patients: number;
    average_adherence_rate: number;
    patients_above_threshold: number;
    patients_needing_intervention: number;
  };
  patient_summaries: {
    patient_id: string;
    patient_name: string;
    adherence_rate: number;
    risk_level: 'low' | 'medium' | 'high' | 'critical';
    assigned_doctor: string;
    department: string;
  }[];
  detailed_reports?: HospitalAdherenceReport[];
  department_breakdown: {
    department: string;
    patient_count: number;
    average_adherence: number;
    high_risk_patients: number;
  }[];
}

export interface HospitalDepartmentAnalysis {
  hospital_id: string;
  analysis_date: Date;
  criteria: {
    analysis_period_days: number;
    department_filter?: string;
    min_adherence_rate: number;
    risk_level_filter?: string;
  };
  department_performance: {
    department: string;
    doctor_count: number;
    patient_count: number;
    average_adherence_rate: number;
    high_risk_patients: number;
    top_performing_doctor: {
      id: string;
      name: string;
      patient_count: number;
      average_adherence: number;
    };
    improvement_opportunities: string[];
  }[];
  overall_hospital_metrics: {
    total_departments: number;
    total_doctors: number;
    total_patients: number;
    hospital_average_adherence: number;
    best_performing_department: string;
    departments_needing_attention: string[];
  };
  recommendations: {
    department: string;
    priority: 'high' | 'medium' | 'low';
    action_items: string[];
  }[];
}

export interface HospitalDashboardSummary {
  hospital_id: string;
  hospital_name: string;
  summary_date: Date;
  overview: {
    total_patients: number;
    total_doctors: number;
    total_departments: number;
    active_prescriptions: number;
  };
  adherence_metrics: {
    overall_adherence_rate: number;
    patients_above_80_percent: number;
    patients_needing_intervention: number;
    trend_direction: 'improving' | 'declining' | 'stable';
  };
  department_highlights: {
    best_performing: {
      department: string;
      adherence_rate: number;
    };
    needs_attention: {
      department: string;
      adherence_rate: number;
      patient_count: number;
    };
  };
  recent_alerts: {
    critical_patients: number;
    missed_appointments: number;
    medication_adherence_alerts: number;
  };
  quick_actions: {
    patients_to_contact: number;
    reports_pending_review: number;
    doctors_to_notify: number;
  };
}
