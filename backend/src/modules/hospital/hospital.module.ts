import { Module } from '@nestjs/common';
import { HospitalController } from './hospital.controller';
import { HospitalService } from './hospital.service';
import { SupabaseService } from '../../config/supabase.service';
import { AdherenceService } from '../adherence/adherence.service';
import { GamificationService } from '../gamification/gamification.service';

@Module({
  controllers: [HospitalController],
  providers: [
    HospitalService,
    SupabaseService,
    AdherenceService,
    GamificationService,
  ],
  exports: [HospitalService],
})
export class HospitalModule {}
