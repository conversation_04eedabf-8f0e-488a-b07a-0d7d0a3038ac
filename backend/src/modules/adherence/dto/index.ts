import { IsString, IsUUI<PERSON>, <PERSON><PERSON>ptional, <PERSON>Enum, IsDateString, IsNotEmpty, MaxLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export enum AdherenceStatus {
  TAKEN = 'taken',
  MISSED = 'missed',
  SKIPPED = 'skipped',
}

export class CreateAdherenceRecordDto {
  @ApiProperty({
    description: 'ID of the patient',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  patient_id: string;

  @ApiProperty({
    description: 'ID of the medicine',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  medicine_id: string;

  @ApiProperty({
    description: 'Scheduled time for the medication',
    example: '2024-01-15T08:00:00Z',
  })
  @IsDateString()
  @IsNotEmpty()
  scheduled_time: string;

  @ApiPropertyOptional({
    description: 'Actual time when medication was taken',
    example: '2024-01-15T08:15:00Z',
  })
  @IsOptional()
  @IsDateString()
  taken_time?: string;

  @ApiProperty({
    description: 'Status of medication adherence',
    enum: AdherenceStatus,
    example: AdherenceStatus.TAKEN,
  })
  @IsEnum(AdherenceStatus)
  @IsNotEmpty()
  status: AdherenceStatus;

  @ApiPropertyOptional({
    description: 'Optional notes about the medication event',
    example: 'Took with breakfast',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  notes?: string;
}

export class UpdateAdherenceRecordDto {
  @ApiPropertyOptional({
    description: 'Actual time when medication was taken',
    example: '2024-01-15T08:15:00Z',
  })
  @IsOptional()
  @IsDateString()
  taken_time?: string;

  @ApiPropertyOptional({
    description: 'Status of medication adherence',
    enum: AdherenceStatus,
    example: AdherenceStatus.TAKEN,
  })
  @IsOptional()
  @IsEnum(AdherenceStatus)
  status?: AdherenceStatus;

  @ApiPropertyOptional({
    description: 'Optional notes about the medication event',
    example: 'Took with breakfast',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  notes?: string;
}

export class AdherenceQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by patient ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  patient_id?: string;

  @ApiPropertyOptional({
    description: 'Filter by medicine ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  medicine_id?: string;

  @ApiPropertyOptional({
    description: 'Filter by adherence status',
    enum: AdherenceStatus,
    example: AdherenceStatus.TAKEN,
  })
  @IsOptional()
  @IsEnum(AdherenceStatus)
  status?: AdherenceStatus;

  @ApiPropertyOptional({
    description: 'Start date for filtering (ISO string)',
    example: '2024-01-01T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  start_date?: string;

  @ApiPropertyOptional({
    description: 'End date for filtering (ISO string)',
    example: '2024-01-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  end_date?: string;

  @ApiPropertyOptional({
    description: 'Number of records to return',
    example: 50,
    default: 50,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  limit?: number = 50;

  @ApiPropertyOptional({
    description: 'Number of records to skip',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  offset?: number = 0;
}

export class AdherenceAnalyticsDto {
  @ApiProperty({
    description: 'Patient ID for analytics',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  patient_id: string;

  @ApiPropertyOptional({
    description: 'Number of days to analyze (default: 30)',
    example: 30,
    default: 30,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  days?: number = 30;
}

export interface AdherenceAnalytics {
  patient_id: string;
  total_scheduled: number;
  total_taken: number;
  total_missed: number;
  total_skipped: number;
  adherence_rate: number;
  on_time_rate: number;
  late_rate: number;
  current_streak: number;
  longest_streak: number;
  daily_breakdown: {
    date: string;
    scheduled: number;
    taken: number;
    missed: number;
    skipped: number;
    rate: number;
  }[];
  medicine_breakdown: {
    medicine_id: string;
    medicine_name: string;
    scheduled: number;
    taken: number;
    missed: number;
    skipped: number;
    rate: number;
  }[];
}
