import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { AdherenceService } from './adherence.service';
import { 
  CreateAdherenceRecordDto, 
  UpdateAdherenceRecordDto, 
  AdherenceQueryDto,
  AdherenceAnalyticsDto,
  AdherenceAnalytics 
} from './dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { AdherenceRecord } from '../../common/types';

@ApiTags('adherence')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('adherence')
export class AdherenceController {
  constructor(private readonly adherenceService: AdherenceService) {}

  @Post()
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Record medication adherence',
    description: 'Create a new adherence record for a patient taking medication'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Adherence record created successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or duplicate record',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Patient or medicine not found',
  })
  async create(
    @Body() createAdherenceDto: CreateAdherenceRecordDto,
    @Request() req: any,
  ): Promise<AdherenceRecord> {
    return this.adherenceService.create(createAdherenceDto, req.user);
  }

  @Get()
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get adherence records',
    description: 'Retrieve adherence records with optional filtering'
  })
  @ApiQuery({ name: 'patient_id', required: false, description: 'Filter by patient ID' })
  @ApiQuery({ name: 'medicine_id', required: false, description: 'Filter by medicine ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by adherence status' })
  @ApiQuery({ name: 'start_date', required: false, description: 'Start date for filtering' })
  @ApiQuery({ name: 'end_date', required: false, description: 'End date for filtering' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of records to return' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of records to skip' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Adherence records retrieved successfully',
    type: [Object],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid query parameters',
  })
  async findAll(
    @Query() query: AdherenceQueryDto,
    @Request() req: any,
  ): Promise<AdherenceRecord[]> {
    return this.adherenceService.findAll(query, req.user);
  }

  @Get('analytics/:patient_id')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get adherence analytics',
    description: 'Retrieve detailed adherence analytics for a patient'
  })
  @ApiParam({ name: 'patient_id', description: 'Patient ID for analytics' })
  @ApiQuery({ name: 'days', required: false, description: 'Number of days to analyze (default: 30)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Analytics retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Patient not found',
  })
  async getAnalytics(
    @Param('patient_id') patientId: string,
    @Request() req: any,
    @Query('days') days?: number,
  ): Promise<AdherenceAnalytics> {
    const analyticsDto: AdherenceAnalyticsDto = {
      patient_id: patientId,
      days: days || 30,
    };
    return this.adherenceService.getAnalytics(analyticsDto, req.user);
  }

  @Get(':id')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Get adherence record by ID',
    description: 'Retrieve a specific adherence record'
  })
  @ApiParam({ name: 'id', description: 'Adherence record ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Adherence record retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Adherence record not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  async findOne(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<AdherenceRecord> {
    return this.adherenceService.findOne(id, req.user);
  }

  @Patch(':id')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Update adherence record',
    description: 'Update an existing adherence record'
  })
  @ApiParam({ name: 'id', description: 'Adherence record ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Adherence record updated successfully',
    type: Object,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Adherence record not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  async update(
    @Param('id') id: string,
    @Body() updateAdherenceDto: UpdateAdherenceRecordDto,
    @Request() req: any,
  ): Promise<AdherenceRecord> {
    return this.adherenceService.update(id, updateAdherenceDto, req.user);
  }

  @Delete(':id')
  @Roles('patient', 'doctor', 'hospital', 'admin')
  @ApiOperation({ 
    summary: 'Delete adherence record',
    description: 'Delete an adherence record'
  })
  @ApiParam({ name: 'id', description: 'Adherence record ID' })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Adherence record deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Adherence record not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions',
  })
  async remove(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    return this.adherenceService.remove(id, req.user);
  }
}
