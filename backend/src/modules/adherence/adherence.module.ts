import { Module, forwardRef } from '@nestjs/common';
import { AdherenceService } from './adherence.service';
import { AdherenceController } from './adherence.controller';
import { SupabaseService } from '../../config/supabase.service';
import { GamificationModule } from '../gamification/gamification.module';
import { AchievementsModule } from '../achievements/achievements.module';

@Module({
  imports: [
    forwardRef(() => GamificationModule),
    forwardRef(() => AchievementsModule),
  ],
  controllers: [AdherenceController],
  providers: [AdherenceService, SupabaseService],
  exports: [AdherenceService],
})
export class AdherenceModule {}
