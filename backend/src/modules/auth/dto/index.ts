import { IsE<PERSON>, Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsDateString, IsA<PERSON>y, <PERSON><PERSON>ength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserRole } from '../../../common/types';

export class LoginDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: 'password123' })
  @IsString()
  @MinLength(6)
  password: string;
}

export class RegisterDto {
  @ApiProperty({ example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({ example: 'password123' })
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({ example: 'John Doe' })
  @IsString()
  name: string;

  @ApiProperty({ enum: ['patient', 'doctor', 'hospital', 'insurance'] })
  @IsEnum(['patient', 'doctor', 'hospital', 'insurance'])
  role: UserRole;

  // Patient-specific fields
  @ApiPropertyOptional({ example: '1990-01-01' })
  @IsOptional()
  @IsDateString()
  dateOfBirth?: string;

  @ApiPropertyOptional({ example: '+**********' })
  @IsOptional()
  @IsString()
  emergencyContact?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  assignedDoctorId?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  insuranceId?: string;

  // Doctor-specific fields
  @ApiPropertyOptional({ example: 'Cardiology' })
  @IsOptional()
  @IsString()
  specialization?: string;

  @ApiPropertyOptional({ example: 'MD123456' })
  @IsOptional()
  @IsString()
  licenseNumber?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  hospitalId?: string;

  // Hospital-specific fields
  @ApiPropertyOptional({ example: '123 Medical St, City, State' })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({ example: '+**********' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ example: 'https://hospital.com' })
  @IsOptional()
  @IsString()
  website?: string;

  // Insurance-specific fields
  @ApiPropertyOptional({ example: 'HealthCare Inc.' })
  @IsOptional()
  @IsString()
  companyName?: string;

  @ApiPropertyOptional({ example: ['Health', 'Dental', 'Vision'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  policyTypes?: string[];

  @ApiPropertyOptional({ example: ['New York', 'California'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  coverageAreas?: string[];
}

export class UserResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ enum: ['patient', 'doctor', 'hospital', 'admin', 'insurance'] })
  role: UserRole;

  @ApiPropertyOptional()
  avatar?: string;
}

export class AuthResponseDto {
  @ApiProperty({ type: UserResponseDto })
  user: UserResponseDto;

  @ApiProperty()
  accessToken: string;
}
