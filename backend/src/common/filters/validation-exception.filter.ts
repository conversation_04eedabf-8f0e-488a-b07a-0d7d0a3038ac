import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { ValidationError } from 'class-validator';
import { CustomValidationException } from '../exceptions';

/**
 * Filter specifically for handling validation exceptions
 */
@Catch(BadRequestException)
export class ValidationExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(ValidationExceptionFilter.name);

  catch(exception: BadRequestException, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const requestId = (request as any).requestId || 'unknown';

    const exceptionResponse = exception.getResponse();
    
    // Check if this is a validation error from class-validator
    if (this.isValidationError(exceptionResponse)) {
      const validationErrors = this.extractValidationErrors(exceptionResponse);
      
      const errorResponse = {
        message: 'Validation failed',
        error: 'Validation Error',
        statusCode: 400,
        errorCode: 'VALIDATION_FAILED',
        validationErrors,
        requestId,
        path: request.url,
        timestamp: new Date().toISOString(),
      };

      this.logger.warn(
        `Validation Error on ${request.method} ${request.url}`,
        {
          requestId,
          validationErrors,
          userId: (request as any).user?.id,
        },
      );

      response.status(400).json(errorResponse);
    } else {
      // Let the global exception filter handle non-validation BadRequestExceptions
      throw exception;
    }
  }

  private isValidationError(exceptionResponse: any): boolean {
    return (
      typeof exceptionResponse === 'object' &&
      Array.isArray(exceptionResponse.message) &&
      exceptionResponse.message.length > 0 &&
      typeof exceptionResponse.message[0] === 'string'
    );
  }

  private extractValidationErrors(exceptionResponse: any): any[] {
    if (!Array.isArray(exceptionResponse.message)) {
      return [];
    }

    return exceptionResponse.message.map((error: string) => {
      // Parse validation error messages from class-validator
      // Format: "property constraint1, constraint2"
      const parts = error.split(' ');
      const property = parts[0];
      const constraints = parts.slice(1).join(' ');

      return {
        field: property,
        constraints: { [property]: constraints },
        message: error,
      };
    });
  }
}

/**
 * Custom validation pipe that throws CustomValidationException
 */
import { ValidationPipe, ValidationPipeOptions } from '@nestjs/common';

export class CustomValidationPipe extends ValidationPipe {
  constructor(options?: ValidationPipeOptions) {
    super({
      ...options,
      exceptionFactory: (errors: ValidationError[]) => {
        return new CustomValidationException(errors);
      },
    });
  }
}
