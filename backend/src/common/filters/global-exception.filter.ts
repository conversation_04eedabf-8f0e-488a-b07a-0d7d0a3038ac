import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import {
  BusinessException,
  DatabaseException,
  ExternalServiceException,
  CustomValidationException,
} from '../exceptions';

/**
 * Global exception filter that handles all exceptions
 */
@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost): void {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const requestId = uuidv4();

    // Add request ID to request for tracking
    (request as any).requestId = requestId;

    let status: HttpStatus;
    let errorResponse: any;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'object') {
        errorResponse = {
          ...exceptionResponse,
          requestId,
          path: request.url,
        };
      } else {
        errorResponse = {
          message: exceptionResponse,
          error: exception.name,
          statusCode: status,
          requestId,
          path: request.url,
          timestamp: new Date().toISOString(),
        };
      }
    } else {
      // Handle unexpected errors
      status = HttpStatus.INTERNAL_SERVER_ERROR;
      errorResponse = {
        message: 'Internal server error',
        error: 'Internal Server Error',
        statusCode: status,
        requestId,
        path: request.url,
        timestamp: new Date().toISOString(),
      };
    }

    // Log the error with context
    this.logError(exception, request, requestId, status);

    // Send error response
    response.status(status).json(errorResponse);
  }

  private logError(
    exception: unknown,
    request: Request,
    requestId: string,
    status: HttpStatus,
  ): void {
    const errorContext = {
      requestId,
      method: request.method,
      url: request.url,
      userAgent: request.get('User-Agent'),
      ip: request.ip,
      userId: (request as any).user?.id,
      userRole: (request as any).user?.role,
      statusCode: status,
    };

    if (exception instanceof BusinessException) {
      this.logger.warn(
        `Business Exception: ${exception.message}`,
        {
          ...errorContext,
          exception: exception.getResponse(),
        },
      );
    } else if (exception instanceof DatabaseException) {
      this.logger.error(
        `Database Exception: ${exception.message}`,
        {
          ...errorContext,
          exception: exception.getResponse(),
          stack: exception.stack,
        },
      );
    } else if (exception instanceof ExternalServiceException) {
      this.logger.error(
        `External Service Exception: ${exception.message}`,
        {
          ...errorContext,
          exception: exception.getResponse(),
        },
      );
    } else if (exception instanceof CustomValidationException) {
      this.logger.warn(
        `Validation Exception: ${exception.message}`,
        {
          ...errorContext,
          exception: exception.getResponse(),
        },
      );
    } else if (exception instanceof HttpException) {
      if (status >= 500) {
        this.logger.error(
          `HTTP Exception: ${exception.message}`,
          {
            ...errorContext,
            exception: exception.getResponse(),
            stack: exception.stack,
          },
        );
      } else {
        this.logger.warn(
          `HTTP Exception: ${exception.message}`,
          {
            ...errorContext,
            exception: exception.getResponse(),
          },
        );
      }
    } else {
      // Log unexpected errors with full stack trace
      this.logger.error(
        `Unexpected Error: ${exception}`,
        {
          ...errorContext,
          stack: exception instanceof Error ? exception.stack : 'No stack trace available',
        },
      );
    }
  }
}
