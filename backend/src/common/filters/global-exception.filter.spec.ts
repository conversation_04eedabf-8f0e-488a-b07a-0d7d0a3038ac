import { ArgumentsHost, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { GlobalExceptionFilter } from './global-exception.filter';
import { BusinessException, ResourceNotFoundException } from '../exceptions';

describe('GlobalExceptionFilter', () => {
  let filter: GlobalExceptionFilter;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockArgumentsHost: Partial<ArgumentsHost>;

  beforeEach(() => {
    filter = new GlobalExceptionFilter();
    
    mockRequest = {
      method: 'GET',
      url: '/api/v1/test',
      ip: '127.0.0.1',
      get: jest.fn().mockReturnValue('test-user-agent'),
    };

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getRequest: () => mockRequest,
        getResponse: () => mockResponse,
      }),
    };
  });

  describe('catch', () => {
    it('should handle BusinessException correctly', () => {
      const exception = new ResourceNotFoundException('User', '123');
      
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.NOT_FOUND);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: "User with identifier '123' not found",
          error: 'Business Logic Error',
          statusCode: HttpStatus.NOT_FOUND,
          errorCode: 'RESOURCE_NOT_FOUND',
          requestId: expect.any(String),
          path: '/api/v1/test',
        }),
      );
    });

    it('should handle generic HttpException', () => {
      const exception = new BusinessException('Test error', HttpStatus.BAD_REQUEST);
      
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Test error',
          statusCode: HttpStatus.BAD_REQUEST,
          requestId: expect.any(String),
          path: '/api/v1/test',
        }),
      );
    });

    it('should handle unexpected errors', () => {
      const exception = new Error('Unexpected error');
      
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Internal server error',
          error: 'Internal Server Error',
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          requestId: expect.any(String),
          path: '/api/v1/test',
          timestamp: expect.any(String),
        }),
      );
    });

    it('should add requestId to request object', () => {
      const exception = new BusinessException('Test error');
      
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      expect((mockRequest as any).requestId).toBeDefined();
      expect(typeof (mockRequest as any).requestId).toBe('string');
    });

    it('should include user context in error response when available', () => {
      (mockRequest as any).user = { id: 'user123', role: 'patient' };
      const exception = new BusinessException('Test error');
      
      filter.catch(exception, mockArgumentsHost as ArgumentsHost);

      // The user context should be logged but not exposed in the response
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Test error',
          requestId: expect.any(String),
        }),
      );
    });
  });
});
