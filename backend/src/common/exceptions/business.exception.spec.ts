import { HttpStatus } from '@nestjs/common';
import {
  BusinessException,
  UnauthorizedAccessException,
  ResourceNotFoundException,
  DuplicateResourceException,
  InvalidOperationException,
  InsufficientPermissionsException,
  RateLimitExceededException,
} from './business.exception';

describe('BusinessException', () => {
  describe('BusinessException', () => {
    it('should create a business exception with default values', () => {
      const message = 'Test business error';
      const exception = new BusinessException(message);

      expect(exception.getStatus()).toBe(HttpStatus.BAD_REQUEST);
      expect(exception.message).toBe(message);
      
      const response = exception.getResponse() as any;
      expect(response.message).toBe(message);
      expect(response.error).toBe('Business Logic Error');
      expect(response.statusCode).toBe(HttpStatus.BAD_REQUEST);
      expect(response.timestamp).toBeDefined();
    });

    it('should create a business exception with custom values', () => {
      const message = 'Custom error';
      const statusCode = HttpStatus.CONFLICT;
      const errorCode = 'CUSTOM_ERROR';
      const details = { field: 'value' };

      const exception = new BusinessException(message, statusCode, errorCode, details);

      expect(exception.getStatus()).toBe(statusCode);
      
      const response = exception.getResponse() as any;
      expect(response.message).toBe(message);
      expect(response.statusCode).toBe(statusCode);
      expect(response.errorCode).toBe(errorCode);
      expect(response.details).toEqual(details);
    });
  });

  describe('UnauthorizedAccessException', () => {
    it('should create an unauthorized access exception', () => {
      const resource = 'User';
      const action = 'delete';
      const exception = new UnauthorizedAccessException(resource, action);

      expect(exception.getStatus()).toBe(HttpStatus.FORBIDDEN);
      
      const response = exception.getResponse() as any;
      expect(response.message).toBe(`Unauthorized access: Cannot ${action} ${resource}`);
      expect(response.errorCode).toBe('UNAUTHORIZED_ACCESS');
      expect(response.details).toEqual({ resource, action });
    });
  });

  describe('ResourceNotFoundException', () => {
    it('should create a resource not found exception', () => {
      const resource = 'User';
      const identifier = '123';
      const exception = new ResourceNotFoundException(resource, identifier);

      expect(exception.getStatus()).toBe(HttpStatus.NOT_FOUND);
      
      const response = exception.getResponse() as any;
      expect(response.message).toBe(`${resource} with identifier '${identifier}' not found`);
      expect(response.errorCode).toBe('RESOURCE_NOT_FOUND');
      expect(response.details).toEqual({ resource, identifier });
    });
  });

  describe('DuplicateResourceException', () => {
    it('should create a duplicate resource exception', () => {
      const resource = 'User';
      const field = 'email';
      const value = '<EMAIL>';
      const exception = new DuplicateResourceException(resource, field, value);

      expect(exception.getStatus()).toBe(HttpStatus.CONFLICT);
      
      const response = exception.getResponse() as any;
      expect(response.message).toBe(`${resource} with ${field} '${value}' already exists`);
      expect(response.errorCode).toBe('DUPLICATE_RESOURCE');
      expect(response.details).toEqual({ resource, field, value });
    });
  });

  describe('InvalidOperationException', () => {
    it('should create an invalid operation exception', () => {
      const operation = 'delete user';
      const reason = 'user has active prescriptions';
      const exception = new InvalidOperationException(operation, reason);

      expect(exception.getStatus()).toBe(HttpStatus.BAD_REQUEST);
      
      const response = exception.getResponse() as any;
      expect(response.message).toBe(`Invalid operation: ${operation}. Reason: ${reason}`);
      expect(response.errorCode).toBe('INVALID_OPERATION');
      expect(response.details).toEqual({ operation, reason });
    });
  });

  describe('InsufficientPermissionsException', () => {
    it('should create an insufficient permissions exception', () => {
      const requiredRole = 'admin';
      const currentRole = 'user';
      const exception = new InsufficientPermissionsException(requiredRole, currentRole);

      expect(exception.getStatus()).toBe(HttpStatus.FORBIDDEN);
      
      const response = exception.getResponse() as any;
      expect(response.message).toBe(`Insufficient permissions: Required role '${requiredRole}', current role '${currentRole}'`);
      expect(response.errorCode).toBe('INSUFFICIENT_PERMISSIONS');
      expect(response.details).toEqual({ requiredRole, currentRole });
    });
  });

  describe('RateLimitExceededException', () => {
    it('should create a rate limit exceeded exception', () => {
      const limit = 100;
      const windowMs = 60000;
      const exception = new RateLimitExceededException(limit, windowMs);

      expect(exception.getStatus()).toBe(HttpStatus.TOO_MANY_REQUESTS);
      
      const response = exception.getResponse() as any;
      expect(response.message).toBe(`Rate limit exceeded: ${limit} requests per ${windowMs}ms`);
      expect(response.errorCode).toBe('RATE_LIMIT_EXCEEDED');
      expect(response.details).toEqual({ limit, windowMs });
    });
  });
});
