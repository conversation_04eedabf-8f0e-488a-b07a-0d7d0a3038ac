import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * Base exception for external service errors
 */
export class ExternalServiceException extends HttpException {
  constructor(
    service: string,
    message: string,
    statusCode: HttpStatus = HttpStatus.SERVICE_UNAVAILABLE,
    errorCode?: string,
    details?: any,
  ) {
    const errorResponse = {
      message: `External service error (${service}): ${message}`,
      error: 'External Service Error',
      statusCode,
      errorCode,
      service,
      details,
      timestamp: new Date().toISOString(),
    };

    super(errorResponse, statusCode);
  }
}

/**
 * Exception for Twilio service errors
 */
export class TwilioServiceException extends ExternalServiceException {
  constructor(message: string, errorCode?: string, details?: any) {
    super('Twilio', message, HttpStatus.SERVICE_UNAVAILABLE, errorCode, details);
  }
}

/**
 * Exception for ElevenLabs service errors
 */
export class ElevenLabsServiceException extends ExternalServiceException {
  constructor(message: string, errorCode?: string, details?: any) {
    super('ElevenLabs', message, HttpStatus.SERVICE_UNAVAILABLE, errorCode, details);
  }
}

/**
 * Exception for AWS service errors
 */
export class AWSServiceException extends ExternalServiceException {
  constructor(service: string, message: string, errorCode?: string, details?: any) {
    super(`AWS ${service}`, message, HttpStatus.SERVICE_UNAVAILABLE, errorCode, details);
  }
}

/**
 * Exception for Supabase service errors
 */
export class SupabaseServiceException extends ExternalServiceException {
  constructor(message: string, errorCode?: string, details?: any) {
    super('Supabase', message, HttpStatus.SERVICE_UNAVAILABLE, errorCode, details);
  }
}

/**
 * Exception for external API timeout
 */
export class ExternalServiceTimeoutException extends ExternalServiceException {
  constructor(service: string, timeout: number) {
    super(
      service,
      `Request timeout after ${timeout}ms`,
      HttpStatus.REQUEST_TIMEOUT,
      'SERVICE_TIMEOUT',
      { timeout },
    );
  }
}

/**
 * Exception for external API rate limiting
 */
export class ExternalServiceRateLimitException extends ExternalServiceException {
  constructor(service: string, retryAfter?: number) {
    super(
      service,
      'Rate limit exceeded',
      HttpStatus.TOO_MANY_REQUESTS,
      'EXTERNAL_RATE_LIMIT',
      { retryAfter },
    );
  }
}

/**
 * Exception for external service authentication errors
 */
export class ExternalServiceAuthException extends ExternalServiceException {
  constructor(service: string, message: string = 'Authentication failed') {
    super(
      service,
      message,
      HttpStatus.UNAUTHORIZED,
      'EXTERNAL_AUTH_ERROR',
    );
  }
}
