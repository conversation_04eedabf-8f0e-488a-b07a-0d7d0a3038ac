import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * Custom exception for business logic errors
 */
export class BusinessException extends HttpException {
  constructor(
    message: string,
    statusCode: HttpStatus = HttpStatus.BAD_REQUEST,
    errorCode?: string,
    details?: any,
  ) {
    const errorResponse = {
      message,
      error: 'Business Logic Error',
      statusCode,
      errorCode,
      details,
      timestamp: new Date().toISOString(),
    };

    super(errorResponse, statusCode);
  }
}

/**
 * Exception for unauthorized access to resources
 */
export class UnauthorizedAccessException extends BusinessException {
  constructor(resource: string, action: string) {
    super(
      `Unauthorized access: Cannot ${action} ${resource}`,
      HttpStatus.FORBIDDEN,
      'UNAUTHORIZED_ACCESS',
      { resource, action },
    );
  }
}

/**
 * Exception for resource not found
 */
export class ResourceNotFoundException extends BusinessException {
  constructor(resource: string, identifier: string) {
    super(
      `${resource} with identifier '${identifier}' not found`,
      HttpStatus.NOT_FOUND,
      'RESOURCE_NOT_FOUND',
      { resource, identifier },
    );
  }
}

/**
 * Exception for duplicate resource creation
 */
export class DuplicateResourceException extends BusinessException {
  constructor(resource: string, field: string, value: string) {
    super(
      `${resource} with ${field} '${value}' already exists`,
      HttpStatus.CONFLICT,
      'DUPLICATE_RESOURCE',
      { resource, field, value },
    );
  }
}

/**
 * Exception for invalid business operations
 */
export class InvalidOperationException extends BusinessException {
  constructor(operation: string, reason: string) {
    super(
      `Invalid operation: ${operation}. Reason: ${reason}`,
      HttpStatus.BAD_REQUEST,
      'INVALID_OPERATION',
      { operation, reason },
    );
  }
}

/**
 * Exception for insufficient permissions
 */
export class InsufficientPermissionsException extends BusinessException {
  constructor(requiredRole: string, currentRole: string) {
    super(
      `Insufficient permissions: Required role '${requiredRole}', current role '${currentRole}'`,
      HttpStatus.FORBIDDEN,
      'INSUFFICIENT_PERMISSIONS',
      { requiredRole, currentRole },
    );
  }
}

/**
 * Exception for rate limiting
 */
export class RateLimitExceededException extends BusinessException {
  constructor(limit: number, windowMs: number) {
    super(
      `Rate limit exceeded: ${limit} requests per ${windowMs}ms`,
      HttpStatus.TOO_MANY_REQUESTS,
      'RATE_LIMIT_EXCEEDED',
      { limit, windowMs },
    );
  }
}
