import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * Base exception for database errors
 */
export class DatabaseException extends HttpException {
  constructor(
    message: string,
    statusCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
    errorCode?: string,
    details?: any,
  ) {
    const errorResponse = {
      message: `Database error: ${message}`,
      error: 'Database Error',
      statusCode,
      errorCode,
      details,
      timestamp: new Date().toISOString(),
    };

    super(errorResponse, statusCode);
  }
}

/**
 * Exception for database connection errors
 */
export class DatabaseConnectionException extends DatabaseException {
  constructor(message: string = 'Failed to connect to database') {
    super(message, HttpStatus.SERVICE_UNAVAILABLE, 'DB_CONNECTION_ERROR');
  }
}

/**
 * Exception for database constraint violations
 */
export class DatabaseConstraintException extends DatabaseException {
  constructor(constraint: string, table: string, details?: any) {
    super(
      `Constraint violation: ${constraint} on table ${table}`,
      HttpStatus.CONFLICT,
      'DB_CONSTRAINT_VIOLATION',
      { constraint, table, ...details },
    );
  }
}

/**
 * Exception for foreign key constraint violations
 */
export class ForeignKeyConstraintException extends DatabaseConstraintException {
  constructor(table: string, column: string, referencedTable: string) {
    super(
      `foreign_key_${column}`,
      table,
      {
        column,
        referencedTable,
        type: 'foreign_key',
      },
    );
  }
}

/**
 * Exception for unique constraint violations
 */
export class UniqueConstraintException extends DatabaseConstraintException {
  constructor(table: string, column: string, value: any) {
    super(
      `unique_${column}`,
      table,
      {
        column,
        value,
        type: 'unique',
      },
    );
  }
}

/**
 * Exception for database query timeout
 */
export class DatabaseTimeoutException extends DatabaseException {
  constructor(query: string, timeout: number) {
    super(
      `Query timeout after ${timeout}ms`,
      HttpStatus.REQUEST_TIMEOUT,
      'DB_QUERY_TIMEOUT',
      { query: query.substring(0, 100), timeout },
    );
  }
}

/**
 * Exception for database transaction errors
 */
export class DatabaseTransactionException extends DatabaseException {
  constructor(message: string, operation: string) {
    super(
      `Transaction error during ${operation}: ${message}`,
      HttpStatus.INTERNAL_SERVER_ERROR,
      'DB_TRANSACTION_ERROR',
      { operation },
    );
  }
}

/**
 * Exception for database migration errors
 */
export class DatabaseMigrationException extends DatabaseException {
  constructor(migration: string, message: string) {
    super(
      `Migration error in ${migration}: ${message}`,
      HttpStatus.INTERNAL_SERVER_ERROR,
      'DB_MIGRATION_ERROR',
      { migration },
    );
  }
}
