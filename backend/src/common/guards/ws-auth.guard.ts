import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';
import { SupabaseService } from '../../config/supabase.service';
import { User } from '../types';

@Injectable()
export class WsAuthGuard implements CanActivate {
  private readonly logger = new Logger(WsAuthGuard.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly supabaseService: SupabaseService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const client: Socket = context.switchToWs().getClient();
      const token = this.extractTokenFromSocket(client);

      if (!token) {
        throw new WsException('Authentication token not provided');
      }

      // Verify JWT token
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
      });

      if (!payload || !payload.sub) {
        throw new WsException('Invalid authentication token');
      }

      // Get user details from Supabase
      const user = await this.getUserFromDatabase(payload.sub);
      if (!user) {
        throw new WsException('User not found');
      }

      // Attach user to socket for later use
      client.data.user = user;
      client.data.userId = user.id;
      client.data.userRole = user.role;

      this.logger.log(`WebSocket authenticated: ${user.id} (${user.role})`);
      return true;
    } catch (error) {
      this.logger.error('WebSocket authentication failed:', error.message);
      throw new WsException('Authentication failed');
    }
  }

  private extractTokenFromSocket(client: Socket): string | null {
    // Try to get token from handshake auth
    const authToken = client.handshake.auth?.token;
    if (authToken) {
      return authToken;
    }

    // Try to get token from query parameters
    const queryToken = client.handshake.query?.token;
    if (queryToken && typeof queryToken === 'string') {
      return queryToken;
    }

    // Try to get token from headers
    const headerAuth = client.handshake.headers?.authorization;
    if (headerAuth && typeof headerAuth === 'string') {
      const [type, token] = headerAuth.split(' ');
      if (type === 'Bearer' && token) {
        return token;
      }
    }

    return null;
  }

  private async getUserFromDatabase(userId: string): Promise<User | null> {
    try {
      const supabase = this.supabaseService.getAdminClient();
      
      const { data: user, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        this.logger.error('Error fetching user from database:', error);
        return null;
      }

      return user as User;
    } catch (error) {
      this.logger.error('Database error while fetching user:', error);
      return null;
    }
  }
}

// WebSocket Role Guard
@Injectable()
export class WsRolesGuard implements CanActivate {
  private readonly logger = new Logger(WsRolesGuard.name);

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.getRequiredRoles(context);
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const client: Socket = context.switchToWs().getClient();
    const user = client.data.user as User;

    if (!user) {
      throw new WsException('User not authenticated');
    }

    const hasRole = requiredRoles.includes(user.role);
    if (!hasRole) {
      this.logger.warn(`Access denied for user ${user.id} with role ${user.role}. Required roles: ${requiredRoles.join(', ')}`);
      throw new WsException('Insufficient permissions');
    }

    return true;
  }

  private getRequiredRoles(context: ExecutionContext): string[] {
    const handler = context.getHandler();
    const classRef = context.getClass();
    
    // Get roles from method decorator
    const methodRoles = Reflect.getMetadata('roles', handler) || [];
    // Get roles from class decorator
    const classRoles = Reflect.getMetadata('roles', classRef) || [];
    
    return [...methodRoles, ...classRoles];
  }
}

// WebSocket Roles Decorator
export const WsRoles = (...roles: string[]) => {
  return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
    if (propertyKey && descriptor) {
      // Method decorator
      Reflect.defineMetadata('roles', roles, descriptor.value);
    } else {
      // Class decorator
      Reflect.defineMetadata('roles', roles, target);
    }
  };
};
