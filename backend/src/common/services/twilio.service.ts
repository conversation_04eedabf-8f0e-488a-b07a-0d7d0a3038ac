import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Twilio } from 'twilio';

export interface SMSOptions {
  to: string;
  message: string;
  from?: string;
}

export interface CallOptions {
  to: string;
  message: string;
  from?: string;
  voice?: 'man' | 'woman' | 'alice';
}

export interface SMSResponse {
  success: boolean;
  messageSid?: string;
  error?: string;
}

export interface CallResponse {
  success: boolean;
  callSid?: string;
  error?: string;
}

@Injectable()
export class TwilioService {
  private readonly logger = new Logger(TwilioService.name);
  private readonly twilioClient: Twilio;
  private readonly fromPhoneNumber: string;
  private readonly isEnabled: boolean;

  constructor(private readonly configService: ConfigService) {
    const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
    const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');
    this.fromPhoneNumber = this.configService.get<string>('TWILIO_PHONE_NUMBER', '');
    
    // Check if Twilio is properly configured
    this.isEnabled = !!(accountSid && authToken && this.fromPhoneNumber);
    
    if (this.isEnabled) {
      this.twilioClient = new Twilio(accountSid, authToken);
      this.logger.log('Twilio service initialized successfully');
    } else {
      this.logger.warn('Twilio service disabled - missing configuration (TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER)');
    }
  }

  async sendSMS(options: SMSOptions): Promise<SMSResponse> {
    if (!this.isEnabled) {
      this.logger.warn('Twilio not configured - SMS not sent');
      return { success: false, error: 'Twilio not configured' };
    }

    try {
      const message = await this.twilioClient.messages.create({
        body: options.message,
        from: options.from || this.fromPhoneNumber,
        to: options.to,
      });

      this.logger.log(`SMS sent successfully to ${options.to}, SID: ${message.sid}`);
      return { success: true, messageSid: message.sid };
    } catch (error) {
      this.logger.error(`Failed to send SMS to ${options.to}:`, error);
      return { success: false, error: error.message };
    }
  }

  async makeCall(options: CallOptions): Promise<CallResponse> {
    if (!this.isEnabled) {
      this.logger.warn('Twilio not configured - call not made');
      return { success: false, error: 'Twilio not configured' };
    }

    try {
      // Create TwiML for the call
      const twiml = this.generateTwiML(options.message, options.voice);
      
      const call = await this.twilioClient.calls.create({
        twiml: twiml,
        from: options.from || this.fromPhoneNumber,
        to: options.to,
      });

      this.logger.log(`Call initiated successfully to ${options.to}, SID: ${call.sid}`);
      return { success: true, callSid: call.sid };
    } catch (error) {
      this.logger.error(`Failed to make call to ${options.to}:`, error);
      return { success: false, error: error.message };
    }
  }

  async sendMedicationReminder(
    phoneNumber: string, 
    patientName: string, 
    medicineName: string, 
    dosage: string,
    reminderType: 'sms' | 'call' = 'sms'
  ): Promise<SMSResponse | CallResponse> {
    const message = this.generateMedicationReminderMessage(patientName, medicineName, dosage);
    
    if (reminderType === 'sms') {
      return this.sendSMS({
        to: phoneNumber,
        message: message,
      });
    } else {
      return this.makeCall({
        to: phoneNumber,
        message: message,
        voice: 'alice', // Use Alice voice for better clarity
      });
    }
  }

  private generateMedicationReminderMessage(
    patientName: string, 
    medicineName: string, 
    dosage: string
  ): string {
    return `Hi ${patientName}, this is a friendly reminder to take your medication: ${medicineName} (${dosage}). Please take it as prescribed by your doctor. Stay healthy! - MedCare Team`;
  }

  private generateTwiML(message: string, voice: string = 'alice'): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="${voice}">${this.escapeXml(message)}</Say>
  <Pause length="1"/>
  <Say voice="${voice}">Thank you for using MedCare. Have a great day!</Say>
</Response>`;
  }

  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  // Utility method to validate phone numbers
  isValidPhoneNumber(phoneNumber: string): boolean {
    // Basic phone number validation (E.164 format)
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phoneNumber);
  }

  // Get Twilio service status
  getServiceStatus(): { enabled: boolean; configured: boolean } {
    return {
      enabled: this.isEnabled,
      configured: !!(
        this.configService.get('TWILIO_ACCOUNT_SID') &&
        this.configService.get('TWILIO_AUTH_TOKEN') &&
        this.configService.get('TWILIO_PHONE_NUMBER')
      ),
    };
  }

  // Send bulk SMS (for multiple patients)
  async sendBulkSMS(recipients: Array<{ phoneNumber: string; message: string }>): Promise<Array<SMSResponse & { phoneNumber: string }>> {
    if (!this.isEnabled) {
      return recipients.map(r => ({
        phoneNumber: r.phoneNumber,
        success: false,
        error: 'Twilio not configured'
      }));
    }

    const results: Array<SMSResponse & { phoneNumber: string }> = [];

    for (const recipient of recipients) {
      const result = await this.sendSMS({
        to: recipient.phoneNumber,
        message: recipient.message,
      });

      results.push({
        phoneNumber: recipient.phoneNumber,
        ...result,
      });

      // Add small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    return results;
  }
}
