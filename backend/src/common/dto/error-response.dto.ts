import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Standard error response DTO
 */
export class ErrorResponseDto {
  @ApiProperty({
    description: 'Error message',
    example: 'Resource not found',
  })
  message: string;

  @ApiProperty({
    description: 'Error type',
    example: 'Not Found',
  })
  error: string;

  @ApiProperty({
    description: 'HTTP status code',
    example: 404,
  })
  statusCode: number;

  @ApiPropertyOptional({
    description: 'Specific error code for client handling',
    example: 'RESOURCE_NOT_FOUND',
  })
  errorCode?: string;

  @ApiPropertyOptional({
    description: 'Additional error details',
    example: { resource: 'User', identifier: '123' },
  })
  details?: any;

  @ApiProperty({
    description: 'Timestamp when the error occurred',
    example: '2024-01-15T10:30:00.000Z',
  })
  timestamp: string;

  @ApiPropertyOptional({
    description: 'Request ID for tracking',
    example: 'req_123456789',
  })
  requestId?: string;

  @ApiPropertyOptional({
    description: 'API path where the error occurred',
    example: '/api/v1/users/123',
  })
  path?: string;
}

/**
 * Validation error response DTO
 */
export class ValidationErrorResponseDto extends ErrorResponseDto {
  @ApiProperty({
    description: 'Detailed validation errors',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        field: {
          type: 'string',
          description: 'Field name that failed validation',
          example: 'email',
        },
        value: {
          description: 'Value that failed validation',
          example: 'invalid-email',
        },
        constraints: {
          type: 'object',
          description: 'Validation constraints that were violated',
          example: {
            isEmail: 'email must be a valid email',
            isNotEmpty: 'email should not be empty',
          },
        },
        children: {
          type: 'array',
          description: 'Nested validation errors',
          items: { $ref: '#/components/schemas/ValidationErrorDetail' },
        },
      },
    },
  })
  validationErrors: ValidationErrorDetail[];
}

/**
 * Validation error detail interface
 */
export interface ValidationErrorDetail {
  field: string;
  value: any;
  constraints: Record<string, string>;
  children?: ValidationErrorDetail[];
}

/**
 * External service error response DTO
 */
export class ExternalServiceErrorResponseDto extends ErrorResponseDto {
  @ApiProperty({
    description: 'External service name',
    example: 'Twilio',
  })
  service: string;

  @ApiPropertyOptional({
    description: 'Retry after seconds (for rate limiting)',
    example: 60,
  })
  retryAfter?: number;
}

/**
 * Database error response DTO
 */
export class DatabaseErrorResponseDto extends ErrorResponseDto {
  @ApiPropertyOptional({
    description: 'Database table involved in the error',
    example: 'users',
  })
  table?: string;

  @ApiPropertyOptional({
    description: 'Database constraint that was violated',
    example: 'unique_email',
  })
  constraint?: string;

  @ApiPropertyOptional({
    description: 'Database operation that failed',
    example: 'INSERT',
  })
  operation?: string;
}

/**
 * Rate limit error response DTO
 */
export class RateLimitErrorResponseDto extends ErrorResponseDto {
  @ApiProperty({
    description: 'Rate limit that was exceeded',
    example: 100,
  })
  limit: number;

  @ApiProperty({
    description: 'Time window in milliseconds',
    example: 60000,
  })
  windowMs: number;

  @ApiProperty({
    description: 'Seconds until rate limit resets',
    example: 45,
  })
  retryAfter: number;

  @ApiProperty({
    description: 'Current request count in the window',
    example: 101,
  })
  currentRequests: number;
}
