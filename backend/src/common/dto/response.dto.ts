import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Standard success response wrapper
 */
export class SuccessResponseDto<T = any> {
  @ApiProperty({
    description: 'Success message',
    example: 'Operation completed successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Response data',
  })
  data: T;

  @ApiProperty({
    description: 'HTTP status code',
    example: 200,
  })
  statusCode: number;

  @ApiProperty({
    description: 'Timestamp of the response',
    example: '2024-01-15T10:30:00.000Z',
  })
  timestamp: string;

  @ApiPropertyOptional({
    description: 'Request ID for tracking',
    example: 'req_123456789',
  })
  requestId?: string;
}

/**
 * Paginated response DTO
 */
export class PaginatedResponseDto<T = any> {
  @ApiProperty({
    description: 'Array of items',
    type: 'array',
  })
  items: T[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: 'object',
    properties: {
      total: {
        type: 'number',
        description: 'Total number of items',
        example: 100,
      },
      page: {
        type: 'number',
        description: 'Current page number',
        example: 1,
      },
      limit: {
        type: 'number',
        description: 'Items per page',
        example: 10,
      },
      totalPages: {
        type: 'number',
        description: 'Total number of pages',
        example: 10,
      },
      hasNext: {
        type: 'boolean',
        description: 'Whether there is a next page',
        example: true,
      },
      hasPrevious: {
        type: 'boolean',
        description: 'Whether there is a previous page',
        example: false,
      },
    },
  })
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

/**
 * Generic list response DTO
 */
export class ListResponseDto<T = any> {
  @ApiProperty({
    description: 'Array of items',
    type: 'array',
  })
  items: T[];

  @ApiProperty({
    description: 'Total count of items',
    example: 25,
  })
  count: number;
}

/**
 * Status response DTO
 */
export class StatusResponseDto {
  @ApiProperty({
    description: 'Service status',
    example: 'healthy',
    enum: ['healthy', 'degraded', 'unhealthy'],
  })
  status: 'healthy' | 'degraded' | 'unhealthy';

  @ApiProperty({
    description: 'Timestamp of the status check',
    example: '2024-01-15T10:30:00.000Z',
  })
  timestamp: string;

  @ApiPropertyOptional({
    description: 'Additional status details',
    example: { database: 'connected', redis: 'connected' },
  })
  details?: Record<string, any>;
}

/**
 * Bulk operation response DTO
 */
export class BulkOperationResponseDto {
  @ApiProperty({
    description: 'Number of items processed successfully',
    example: 8,
  })
  successful: number;

  @ApiProperty({
    description: 'Number of items that failed processing',
    example: 2,
  })
  failed: number;

  @ApiProperty({
    description: 'Total number of items processed',
    example: 10,
  })
  total: number;

  @ApiPropertyOptional({
    description: 'Details of failed items',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        item: { description: 'Failed item identifier' },
        error: { description: 'Error message' },
      },
    },
  })
  errors?: Array<{
    item: any;
    error: string;
  }>;
}

/**
 * File upload response DTO
 */
export class FileUploadResponseDto {
  @ApiProperty({
    description: 'Uploaded file name',
    example: 'prescription_123.pdf',
  })
  filename: string;

  @ApiProperty({
    description: 'File URL',
    example: 'https://storage.example.com/files/prescription_123.pdf',
  })
  url: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 1024000,
  })
  size: number;

  @ApiProperty({
    description: 'File MIME type',
    example: 'application/pdf',
  })
  mimeType: string;

  @ApiProperty({
    description: 'Upload timestamp',
    example: '2024-01-15T10:30:00.000Z',
  })
  uploadedAt: string;
}
