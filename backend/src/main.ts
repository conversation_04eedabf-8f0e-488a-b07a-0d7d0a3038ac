import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { GlobalExceptionFilter, CustomValidationPipe } from './common/filters';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Global exception filter
  app.useGlobalFilters(new GlobalExceptionFilter());

  // Global validation pipe with custom exception factory
  app.useGlobalPipes(
    new CustomValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // CORS configuration
  app.enableCors({
    origin: configService.get('CORS_ORIGIN'),
    credentials: true,
  });

  // Global prefix
  const apiPrefix = configService.get('API_PREFIX') || 'api/v1';
  app.setGlobalPrefix(apiPrefix);

  // Swagger documentation
  const config = new DocumentBuilder()
    .setTitle('MedCare API')
    .setDescription(`
      ## Medicine Adherence Platform API

      A comprehensive API for managing medicine adherence across patients, doctors, hospitals, and insurance providers.

      ### Features
      - **Authentication & Authorization**: JWT-based auth with role-based access control
      - **User Management**: Support for patients, doctors, hospitals, and insurance providers
      - **Medicine Tracking**: Prescription management and adherence monitoring
      - **Real-time Notifications**: WebSocket-based notifications and reminders
      - **Gamification**: Achievement system and progress tracking
      - **External Integrations**: Twilio, ElevenLabs, and AWS services

      ### Error Handling
      All endpoints return standardized error responses with detailed error codes and messages.

      ### Rate Limiting
      API endpoints are rate-limited to ensure fair usage and system stability.
    `)
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addTag('auth', 'Authentication and authorization endpoints')
    .addTag('users', 'User management endpoints')
    .addTag('medicines', 'Medicine and prescription management')
    .addTag('reminders', 'Reminder and notification management')
    .addTag('adherence', 'Adherence tracking and gamification')
    .addTag('insurance', 'Insurance provider endpoints')
    .addTag('hospital', 'Hospital management endpoints')
    .addTag('notifications', 'Real-time notification endpoints')
    .addTag('external-services', 'External service integrations')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
    customSiteTitle: 'MedCare API Documentation',
    customfavIcon: '/favicon.ico',
    customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .info .title { color: #2c5aa0 }
    `,
  });

  const port = configService.get('PORT') || 3001;
  await app.listen(port);

  console.log(`🚀 MedCare API is running on: http://localhost:${port}/${apiPrefix}`);
  console.log(`📚 API Documentation: http://localhost:${port}/api/docs`);
}
bootstrap();
