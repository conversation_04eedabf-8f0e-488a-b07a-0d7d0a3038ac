import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

@Injectable()
export class SupabaseService {
  private supabase: SupabaseClient;
  private supabaseAdmin: SupabaseClient;

  constructor(private configService: ConfigService) {
    const supabaseUrl = this.configService.get<string>('supabase.url') || '';
    const supabaseAnonKey = this.configService.get<string>('supabase.anonKey') || '';
    const supabaseServiceKey = this.configService.get<string>('supabase.serviceRoleKey') || '';

    // Client for user operations (with RLS)
    this.supabase = createClient(supabaseUrl, supabaseAnonKey);

    // Admin client for server operations (bypasses RLS)
    this.supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);
  }

  getClient(): SupabaseClient {
    return this.supabase;
  }

  getAdminClient(): SupabaseClient {
    return this.supabaseAdmin;
  }

  // Helper method to set auth token for user-specific operations
  setAuthToken(token: string): SupabaseClient {
    return createClient(
      this.configService.get<string>('supabase.url') || '',
      this.configService.get<string>('supabase.anonKey') || '',
      {
        global: {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      },
    );
  }
}
