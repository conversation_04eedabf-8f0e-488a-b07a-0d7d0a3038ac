#!/bin/bash

# Start Development Servers Script
# This script starts both frontend and backend in development mode using PM2

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting Medical Adherence Platform in Development Mode${NC}"

# Check if we're in the right directory
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo -e "${RED}Error: Please run this script from the project root directory${NC}"
    exit 1
fi

# Check if PM2 is installed
if ! command -v pm2 &> /dev/null; then
    echo -e "${YELLOW}Installing PM2...${NC}"
    sudo npm install -g pm2
fi

# Stop existing processes if running
echo -e "${YELLOW}Stopping existing processes...${NC}"
pm2 delete backend-dev 2>/dev/null || true
pm2 delete frontend-dev 2>/dev/null || true

# Start backend in development mode
echo -e "${BLUE}Starting backend server...${NC}"
cd backend
pm2 start npm --name "backend-dev" -- run start:dev
cd ..

# Wait a moment for backend to initialize
sleep 3

# Start frontend in development mode
echo -e "${BLUE}Starting frontend server...${NC}"
cd frontend
pm2 start npm --name "frontend-dev" -- run dev
cd ..

# Show PM2 status
echo -e "${GREEN}✅ Servers started successfully!${NC}"
pm2 list

# Show logs
echo -e "${YELLOW}Showing recent logs...${NC}"
pm2 logs --lines 10

echo ""
echo -e "${GREEN}🎉 Your application is now running!${NC}"
echo -e "${BLUE}Frontend:${NC} http://$(curl -s ifconfig.me):3000"
echo -e "${BLUE}Backend API:${NC} http://$(curl -s ifconfig.me):3001"
echo ""
echo -e "${YELLOW}Useful PM2 commands:${NC}"
echo "  pm2 logs           - View all logs"
echo "  pm2 logs backend-dev   - View backend logs only"
echo "  pm2 logs frontend-dev  - View frontend logs only"
echo "  pm2 restart all    - Restart all processes"
echo "  pm2 stop all       - Stop all processes"
echo "  pm2 delete all     - Delete all processes"
